# This file contains .gitignore rules that are often used with Drupal projects.
# Because .gitignore is specific to your site and its deployment processes, you
# may need to uncomment, add, or remove rules.

# Pantheon commits a settings.php for environment-specific settings.
# Place local settings in settings.local.php
/web/sites/*/settings.local.php
/web/sites/*/services*.yml
!/web/sites/*/services.pantheon.*.yml
!/web/sites/*/services.yml
/web/libraries/font
/web/libraries/jquery-ui-touch-punch

# Ignore paths that contain user-generated content.
/web/sites/*/files
/web/sites/*/private

# Ignore SimpleTest multi-site environment.
/web/sites/simpletest


# Ignore custom modules managed by Composer.
#
# When a development team creates one or more custom modules that
# are intended for use on more than one site, the typical strategy
# is to register them in Packagist and give them the type
# `drupal-custom-module` instead of `drupal-module`. This will cause
# Composer to install them to the directory `modules/custom`.
# This will cause a build error on Pantheon unless this location
# is .gitignore'd as shown below.
#
# An alternate strategy for custom modules is to commit them
# directly to the repository of the site where they are used. This
# is commonly done with modules that are specific to just one site.
#
# Sites that have both Composer-managed custom modules and custom
# modules that are committed directly to the repository may ignore
# the `modules/custom` directory with the first rule shown below,
# and then allow the locations to be committed on a case-by-case
# basis by re-adding paths using `!` rules. A path that begins with
# a `!` will allow a previously-ignored path to be added to the
# repository. Note, however, that a path cannot be re-added if any
# of its parent directories are excluded. This is why we use the
# rule `/modules/custom/*` instead of `/modules/custom`. Alternately,
# you might instead commit Git-tracked custom modules to some other
# path, such as /web/modules/<site-namespace>.
#
# Sites that do not have any Composer-managed custom modules do
# not need to use any of the `modules/custom` rules below.
# ------------------------------------------------------------------
#/web/modules/custom/*
#!/web/modules/custom/module_in_repo


# Ignore directories generated by Composer
#
# See the "installer-paths" section in the top-level composer.json
# file.
# ------------------------------------------------------------------
/drush/Commands/contrib/
/web/core/
/web/modules/contrib/
/web/themes/contrib/
/web/profiles/contrib/
/web/private/scripts/quicksilver
#/web/libraries/

# Generally you should only ignore the root vendor directory. It's important
# that core/assets/vendor and any other vendor directories within contrib or
# custom module, theme, etc., are not ignored unless you purposely do so.
/vendor/

# Ignore other scaffold files
#
# Ignore all files placed directly at the project root. Rely on
# .gitignore files placed by drupal/core-composer-scaffold to avoid
# build errors that arise if non-ignored files are scaffolded.
# ------------------------------------------------------------------
/.editorconfig
/.gitattributes

# The pantheon-systems/drupal-integrations project places a
# .drush-lock-update file at the project root.
/.drush-lock-update

# The drupal/core-composer-scaffold component will write .gitignore
# files to other locations where it scaffolds files that are not
# already ignored. We must also ignore these generated files.
/web/**/.gitignore


# Other common rules
# ------------------
# Ignore files generated by PhpStorm
/.idea/

# Ignore .env files as they are personal
#/.env

.DS_Store
composer.lock
