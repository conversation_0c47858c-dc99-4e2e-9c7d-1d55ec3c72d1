# @file
# .travis.yml - Drupal for Travis CI Integration
#
# Template provided by https://github.com/LionsAd/drupal_ti.
#
# Based for simpletest upon:
#   https://github.com/sonnym/travis-ci-drupal-module-example

language: php

sudo: false

php:
  - 7.1
  - 7.2

branches:
  only:
    - "8.x-3.x"

matrix:
  fast_finish: true

env:
  global:
    # - PHANTOMJS2_VERSION="2.0.0"
    # add composer's global bin directory to the path
    # see: https://github.com/drush-ops/drush#install---composer
    - PATH="$PATH:$HOME/.composer/vendor/bin"

    # Configuration variables.
    - DRUPAL_TI_MODULE_NAME="fontyourface"
    - DRUPAL_TI_SIMPLETEST_GROUP="fontyourface"

    # Define runners and environment vars to include before and after the
    # main runners / environment vars.
    #- DRUPAL_TI_SCRIPT_DIR_BEFORE="./drupal_ti/before"
    #- DRUPAL_TI_SCRIPT_DIR_AFTER="./drupal_ti/after"

    # The environment to use, supported are: drupal-7, drupal-8
    - DRUPAL_TI_ENVIRONMENT="drupal-8"
    - DRUPAL_TI_CORE_BRANCH="8.7.x"

    # Drupal specific variables.
    - DRUPAL_TI_DB="drupal_travis_db"
    - DRUPAL_TI_DB_URL="mysql://root:@127.0.0.1/drupal_travis_db"
    # Note: Do not add a trailing slash here.
    - DRUPAL_TI_WEBSERVER_URL="http://127.0.0.1"
    - DRUPAL_TI_WEBSERVER_PORT="8080"

    # Simpletest specific commandline arguments, the DRUPAL_TI_SIMPLETEST_GROUP is appended at the end.
    - DRUPAL_TI_SIMPLETEST_ARGS="--verbose --color --concurrency 4 --url $DRUPAL_TI_WEBSERVER_URL:$DRUPAL_TI_WEBSERVER_PORT"

    # === Behat specific variables.
    # This is relative to $TRAVIS_BUILD_DIR
    - DRUPAL_TI_BEHAT_DIR="./tests/behat"
    # These arguments are passed to the bin/behat command.
    - DRUPAL_TI_BEHAT_ARGS=""
    # Specify the filename of the behat.yml with the $DRUPAL_TI_DRUPAL_DIR variables.
    - DRUPAL_TI_BEHAT_YML="behat.yml.dist"
    # This is used to setup Xvfb.
    - DRUPAL_TI_BEHAT_SCREENSIZE_COLOR="1280x1024x16"
    # The version of selenium that should be used.
    - DRUPAL_TI_BEHAT_SELENIUM_VERSION="2.44"
    # Set DRUPAL_TI_BEHAT_DRIVER to "selenium" to use "firefox" or "chrome" here.
    - DRUPAL_TI_BEHAT_DRIVER="phantomjs"
    - DRUPAL_TI_BEHAT_BROWSER="firefox"

    # PHPUnit specific commandline arguments.
    - DRUPAL_TI_PHPUNIT_ARGS=""
    # Specifying the phpunit-core src/ directory is useful when e.g. a vendor/
    # directory is present in the module directory, which phpunit would then
    # try to find tests in. This option is relative to $TRAVIS_BUILD_DIR.
    #- DRUPAL_TI_PHPUNIT_CORE_SRC_DIRECTORY="./tests/src"

    # Code coverage via coveralls.io
    - DRUPAL_TI_COVERAGE="satooshi/php-coveralls:0.6.*"
    # This needs to match your .coveralls.yml file.
    - DRUPAL_TI_COVERAGE_FILE="build/logs/clover.xml"

    # Debug options
    #- DRUPAL_TI_DEBUG="-x -v"
    # Set to "all" to output all files, set to e.g. "xvfb selenium" or "selenium",
    # etc. to only output those channels.
    #- DRUPAL_TI_DEBUG_FILE_OUTPUT="selenium xvfb webserver"

  matrix:
    # [[[ SELECT ANY OR MORE OPTIONS ]]]
    #- DRUPAL_TI_RUNNERS="phpunit"
    #- DRUPAL_TI_RUNNERS="phpunit-core"
    #- DRUPAL_TI_RUNNERS="behat"
    - DRUPAL_TI_RUNNERS="simpletest"

mysql:
  database: drupal_travis_db
  username: root
  encoding: utf8

before_install:
  # Remove xdebug. We aren't generating code coverage, and it slows down Composer.
  - phpenv config-rm xdebug.ini || true
  # Add the oauth token to prevent GitHub timeouts.
  # - git config --global github.accesstoken $GITHUB_OAUTH_TOKEN
  - composer self-update
  - composer global require "hirak/prestissimo:^0.3"
  - composer global require "lionsad/drupal_ti:dev-master"
  - composer global require "drupal/coder:^8.3.1"
  - composer global require "dealerdirect/phpcodesniffer-composer-installer"
  - phpcs --config-set installed_paths $HOME/.composer/vendor/drupal/coder/coder_sniffer
  - drupal-ti before_install

install:
  - drupal-ti install
  - mkdir travis-phantomjs
  - wget https://s3.amazonaws.com/travis-phantomjs/phantomjs-2.0.0-ubuntu-12.04.tar.bz2 -O $PWD/travis-phantomjs/phantomjs-2.0.0-ubuntu-12.04.tar.bz2
  - tar -xvf $PWD/travis-phantomjs/phantomjs-2.0.0-ubuntu-12.04.tar.bz2 -C $PWD/travis-phantomjs
  - export PATH=$PWD/travis-phantomjs:$PATH
  - phantomjs --version

before_script:
  - drupal-ti --include ".travis-before-script.sh"
  - drupal-ti before_script

script:
  - phpcs --standard=phpcs.xml src -s
  - phpcs --standard=phpcs.xml modules -s
  - phpcs --standard=phpcs.xml tests -s
  - find . -name "*.module" | xargs phpcs --standard=phpcs.xml
  - find . -name "*.install" | xargs phpcs --standard=phpcs.xml
  - drupal-ti script

after_script:
  - drupal-ti after_script

notifications:
  email: true
