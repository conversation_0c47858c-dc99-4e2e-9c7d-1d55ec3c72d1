CONTENTS OF THIS FILE
----------------------
 * Introduction
 * Requirements
 * Installation
 * Configuration
 * Maintainers


INTRODUCTION
------------
 A simple module providing a feature to redirect users according to an URL-defined
 parameter after logging in. Allows redirect only internal url of site.
 
 * For a full description of the module visit:
   https://www.drupal.org/project/redirect_after_login

 * To submit bug reports and feature suggestions, or to track changes visit:
   https://www.drupal.org/project/issues/redirect_after_login

REQUIREMENTS
------------
This module requires no modules outside of Drupal core.

INSTALLATION
--------------
 * Install the Redirect after login module as you would normally install a
   contributed Drupal module. Visit https://www.drupal.org/node/1897420 for
   further information.

CONFIGURATION
--------------
  1. After successfully installing the module Redirect After Login,
  you can add URL to each role of site using 
  admin -> configuration -> system -> "Set Login Destination" titled menu or 
  go to /admin/config/system/redirect.
  2. Add a valid url to each roles

  Note: url should starts with '#', '/', '?' character

MAINTAINERS
------------

 * Rahul <PERSON> (rahul-kr-sh) - https://www.drupal.org/u/rahul-kr-sh
 
Supporting organization:
 
 * Sapient - https://www.drupal.org/sapient
 
Sapient is a marketing and consulting company that provides business, marketing, 
and technology services to clients.
