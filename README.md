# Composer-enabled Drupal template

This is Pantheon's recommended starting point for forking new [Drupal](https://www.drupal.org/) upstreams
that work with the Platform's Integrated Composer build process. It is also the
Platform's standard Drupal 9 upstream.

Unlike with earlier Pantheon upstreams, files such as Drupal Core that you are
unlikely to adjust while building sites are not in the main branch of the 
repository. Instead, they are referenced as dependencies that are installed by
Composer.

For more information and detailed installation guides, please visit the
Integrated Composer Pantheon documentation: https://pantheon.io/docs/integrated-composer

## TODO Notes for StreamLine

**Ckeditor 5 support issues**
- [ ] Ckeditor 4 is still required by the `video_embed_field` module. 
Currently, the patch to go to ckeditor 5 is being worked on. 
Monitor status for it to be complete, so we can upgrade to ckeditor 5.
<br><br>**Issue Link:** https://www.drupal.org/project/video_embed_field/issues/3311063

