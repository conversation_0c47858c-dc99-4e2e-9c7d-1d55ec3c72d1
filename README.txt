CONTENTS OF THIS FILE
---------------------

 * Introduction
 * Requirements
 * Installation
 * Configuration
 * FAQ
 * Maintainers


INTRODUCTION
------------

The Theme Switcher Rules module allows you to create theme-switching rules which
allow automatic selection of a theme based on Drupal 8 Conditions system.
Because of this it can also be easily extended to support additional
custom conditions exposed by other modules. In Drupal 8 conditions are no
longer defined through a hook and they are plugins now.

This modules is currently only available for Drupal 8.

 * For a full description of the module, visit the project page:
   https://www.drupal.org/project/theme_switcher

 * To submit bug reports and feature suggestions, or track changes:
   https://www.drupal.org/project/issues/theme_switcher

 * For Conditions further information:
   https://www.drupal.org/docs/8/modules/d8-rules-essentials/for-developers/conditions


REQUIREMENTS
------------

The module require PHP >=7.1 and Drupal 8.5 or higher.


INSTALLATION
------------

Install as you would normally install a contributed Drupal module. See
https://www.drupal.org/docs/8/extending-drupal-8/installing-drupal-8-modules
for further information.


CONFIGURATION
-------------

* Configure the user permissions in Administration >> People >> Permissions:

    - Administer all Theme Switcher Rules

        Allows all permissions for the module.

    - View Theme Switcher Rules

        Users with this permission will be allow to view all Theme Switcher
        Rules.

    - Create Theme Switcher Rules

        Users with this permission will be allow to create Theme Switcher
        Rules.

    - Edit Theme Switcher Rules

        Users with this permission will be allow to edit Theme Switcher Rules.

    - Delete Theme Switcher Rules

        Users with this permission will be allow to delete Theme Switcher
        Rules.

* Configuring theme switcher rules:

    - To create a theme switcher rule, you must provide the following
    information:

        * A *name* to be used in lists of theme switcher rules.
        * A *machine_name* that must be unique. This value will be
        autogenerated and cannot be edited once created.
        * A *status* indicating `active` or `inactive`.
        * A hidden field *weight* that can be changed using the list drag&drop
        interface.
        * A *theme* to apply when each conditions are evaluated and pass.
        * A set of *Conditions* that indicate each one of the variables that
        must be satisfied by the rule to apply the above selected theme.

    **IMPORTANT**: All the rules will be evaluated in the order of the list.
    This means that once the rule pass all its conditions in a satisfying way
    the rest of the rules will not be evaluated.


FAQ
---

 Q: What can I do with this module?

 A: You can apply a specific theme to a unique node, use different themes based
    on the page language, use contrib conditions like a domain to apply one
    theme or another. The best of all is that you can do al of the above at
    once!


Maintainers
-----------

Current maintainers:
 * Adrian Marin (amarincolas) - https://www.drupal.org/u/amarincolas
