{"name": "pantheon-upstreams/drupal-composer-managed", "description": "Install Drupal 9 with Composer on Pantheon.", "type": "project", "license": "MIT", "repositories": [{"type": "composer", "url": "https://packages.drupal.org/8"}, {"type": "path", "url": "upstream-configuration"}], "require": {"pantheon-upstreams/upstream-configuration": "dev-main", "composer/installers": "^1.9", "drupal/core-composer-scaffold": "^10", "drupal/core-recommended": "^10", "pantheon-systems/drupal-integrations": "^10", "cweagans/composer-patches": "^1.7", "drush/drush": "^10 || ^11 || ^12"}, "require-dev": {"drupal/core-dev": "^10"}, "conflict": {"drupal/drupal": "*"}, "minimum-stability": "dev", "prefer-stable": true, "extra": {"drupal-scaffold": {"locations": {"web-root": "./web"}, "allowed-packages": ["pantheon-systems/drupal-integrations"], "file-mapping": {"[project-root]/.editorconfig": false, "[project-root]/pantheon.upstream.yml": false, "[project-root]/.gitattributes": false}}, "installer-paths": {"web/core": ["type:drupal-core"], "web/libraries/{$name}": ["type:drupal-library"], "web/modules/contrib/{$name}": ["type:drupal-module"], "web/profiles/contrib/{$name}": ["type:drupal-profile"], "web/themes/contrib/{$name}": ["type:drupal-theme"], "drush/Commands/contrib/{$name}": ["type:drupal-drush"], "web/modules/custom/{$name}": ["type:drupal-custom-module"], "web/profiles/custom/{$name}": ["type:drupal-custom-profile"], "web/themes/custom/{$name}": ["type:drupal-custom-theme"], "web/private/scripts/quicksilver/{$name}/": ["type:quicksilver-script"]}, "composer-exit-on-patch-failure": true, "patchLevel": {"drupal/core": "-p2"}, "enable-patching": true}, "autoload": {"classmap": ["upstream-configuration/scripts/ComposerScripts.php"]}, "scripts": {"pre-update-cmd": ["DrupalComposerManaged\\ComposerScripts::preUpdate"], "post-update-cmd": ["DrupalComposerManaged\\ComposerScripts::postUpdate"]}, "config": {"preferred-install": "dist", "sort-packages": false, "platform": {"php": "8.1.13"}, "allow-plugins": {"composer/installers": true, "cweagans/composer-patches": true, "drupal/core-composer-scaffold": true, "dealerdirect/phpcodesniffer-composer-installer": true, "phpstan/extension-installer": true, "pantheon-systems/upstream-management": true, "php-http/discovery": true, "tbachert/spi": true}}}