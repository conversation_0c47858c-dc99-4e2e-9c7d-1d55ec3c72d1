bypass_transactional_safe_connection: FALSE
queue:
  enabled: FALSE
  timeouts:
    lease_time: 30
    time: 15
  delays:
    empty_delay: 0
    item_delay: 0
  throttle:
    enabled: TRUE
    threads: 4
    threshold: 10
launcher:
  thread: 'any'
  max_threads: 1
  lock_timeout: 3600
  max_execution_time: 3600
logger:
  cache:
    bin: 'ultimate_cron_logger'
    timeout: -1
  database:
    method: 3
    expire: 1209600
    retain: 1000
scheduler:
  crontab:
    catch_up: 86400
    rules:
      - '*/10+@ * * * *'
  simple:
    rule: '*/15+@ * * * *'
