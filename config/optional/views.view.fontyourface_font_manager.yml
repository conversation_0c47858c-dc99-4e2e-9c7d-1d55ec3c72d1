langcode: en
status: true
dependencies:
  config:
    - core.entity_view_mode.font.teaser
    - taxonomy.vocabulary.font_classification
    - taxonomy.vocabulary.font_foundry
    - taxonomy.vocabulary.languages_supported
  module:
    - fontyourface
    - taxonomy
    - user
id: fontyourface_font_manager
label: 'FontYourFace Font Manager'
module: views
description: ''
tag: ''
base_table: fontyourface_font
base_field: fid
display:
  default:
    display_plugin: default
    id: default
    display_title: Master
    position: 0
    display_options:
      access:
        type: perm
        options:
          perm: 'administer font entities'
      cache:
        type: none
        options: {  }
      query:
        type: views_query
        options:
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_comment: ''
          query_tags: {  }
      exposed_form:
        type: basic
        options:
          submit_button: Apply
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
      pager:
        type: full
        options:
          items_per_page: 50
          offset: 0
          id: 0
          total_pages: null
          tags:
            previous: '‹ Previous'
            next: 'Next ›'
            first: '« First'
            last: 'Last »'
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
          quantity: 9
      style:
        type: html_list
        options:
          row_class: fontyourface-font-manager-item
          default_row_class: true
          uses_fields: false
          type: ul
          wrapper_class: item-list
          class: ''
      row:
        type: 'entity:font'
        options:
          relationship: none
          view_mode: teaser
      fields:
        name:
          id: name
          table: fontyourface_font
          field: name
          relationship: none
          group_type: group
          admin_label: ''
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
          entity_type: null
          entity_field: name
          plugin_id: field
        pid:
          id: pid
          table: fontyourface_font
          field: pid
          relationship: none
          group_type: group
          admin_label: ''
          label: 'Provider ID'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
          entity_type: font
          entity_field: pid
          plugin_id: field
        css_style:
          id: css_style
          table: fontyourface_font
          field: css_style
          relationship: none
          group_type: group
          admin_label: ''
          label: 'CSS Style'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
          entity_type: font
          entity_field: css_style
          plugin_id: field
        operations:
          id: operations
          table: fontyourface_font
          field: operations
          relationship: none
          group_type: group
          admin_label: ''
          label: Operations
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          destination: true
          entity_type: font
          plugin_id: entity_operations
      filters:
        name:
          id: name
          table: fontyourface_font
          field: name
          relationship: none
          group_type: group
          admin_label: ''
          operator: contains
          value: ''
          group: 1
          exposed: true
          expose:
            operator_id: name_op
            label: Name
            description: ''
            use_operator: false
            operator: name_op
            identifier: name
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          entity_type: font
          entity_field: name
          plugin_id: string
        pid:
          id: pid
          table: fontyourface_font
          field: pid
          relationship: none
          group_type: group
          admin_label: ''
          operator: '='
          value: All
          group: 1
          exposed: true
          expose:
            operator_id: pid_op
            label: 'Font Provider'
            description: ''
            use_operator: false
            operator: pid_op
            identifier: pid
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          entity_type: font
          entity_field: pid
          plugin_id: fontyourface_font_pid
        css_style:
          id: css_style
          table: fontyourface_font
          field: css_style
          relationship: none
          group_type: group
          admin_label: ''
          operator: '='
          value: All
          group: 1
          exposed: true
          expose:
            operator_id: css_style_op
            label: 'CSS Style'
            description: ''
            use_operator: false
            operator: css_style_op
            identifier: css_style
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          entity_type: font
          entity_field: css_style
          plugin_id: fontyourface_font_style
        css_weight:
          id: css_weight
          table: fontyourface_font
          field: css_weight
          relationship: none
          group_type: group
          admin_label: ''
          operator: '='
          value: All
          group: 1
          exposed: true
          expose:
            operator_id: css_weight_op
            label: 'CSS Weight'
            description: ''
            use_operator: false
            operator: css_weight_op
            identifier: css_weight
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          entity_type: font
          entity_field: css_weight
          plugin_id: fontyourface_font_weight
        field_classification_target_id:
          id: field_classification_target_id
          table: font__field_classification
          field: field_classification_target_id
          relationship: none
          group_type: group
          admin_label: ''
          operator: or
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: field_classification_target_id_op
            label: Classification
            description: ''
            use_operator: false
            operator: field_classification_target_id_op
            identifier: field_classification_target_id
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          reduce_duplicates: false
          type: select
          limit: true
          vid: font_classification
          hierarchy: false
          error_message: true
          plugin_id: taxonomy_index_tid
        field_supported_languages_target_id:
          id: field_supported_languages_target_id
          table: font__field_supported_languages
          field: field_supported_languages_target_id
          relationship: none
          group_type: group
          admin_label: ''
          operator: or
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: field_supported_languages_target_id_op
            label: Language/subset
            description: ''
            use_operator: false
            operator: field_supported_languages_target_id_op
            identifier: field_supported_languages_target_id
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          reduce_duplicates: false
          type: select
          limit: true
          vid: languages_supported
          hierarchy: false
          error_message: true
          plugin_id: taxonomy_index_tid
        field_tags_target_id:
          id: field_tags_target_id
          table: font__field_tags
          field: field_tags_target_id
          relationship: none
          group_type: group
          admin_label: ''
          operator: or
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: field_tags_target_id_op
            label: Tags
            description: ''
            use_operator: false
            operator: field_tags_target_id_op
            identifier: field_tags_target_id
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          reduce_duplicates: false
          type: select
          limit: true
          vid: font_foundry
          hierarchy: false
          error_message: true
          plugin_id: taxonomy_index_tid
      sorts:
        status:
          id: status
          table: fontyourface_font
          field: status
          relationship: none
          group_type: group
          admin_label: ''
          order: DESC
          exposed: false
          expose:
            label: ''
          entity_type: font
          entity_field: status
          plugin_id: standard
        name:
          id: name
          table: fontyourface_font
          field: name
          relationship: none
          group_type: group
          admin_label: ''
          order: ASC
          exposed: false
          expose:
            label: ''
          entity_type: font
          entity_field: name
          plugin_id: standard
      title: 'Font Selector'
      header: {  }
      footer: {  }
      empty: {  }
      relationships: {  }
      arguments: {  }
      display_extenders: {  }
      filter_groups:
        operator: AND
        groups:
          1: AND
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - user
        - user.permissions
      tags: {  }
  page_1:
    display_plugin: page
    id: page_1
    display_title: Page
    position: 1
    display_options:
      display_extenders: {  }
      path: admin/appearance/font
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - user
        - user.permissions
      tags: {  }
