mailsystem.settings:
  type: config_object
  label: Mail System settings
  mapping:
    theme:
      type: string
      label: Selected theme
    defaults:
      type: mapping
      label: Default settings
      mapping:
        sender:
          type: string
          label: The sender of an email
        formatter:
          type: string
          label: The formatter used for emails
    modules:
      type: sequence
      label: List of modules
      sequence:
        type: sequence
        label: List of keys for a given module
        sequence:
          type: mapping
          label: Per module/key setting.
          mapping:
            sender:
              type: string
              label: The sender of an email
            formatter:
              type: string
              label: The formatter used for emails
