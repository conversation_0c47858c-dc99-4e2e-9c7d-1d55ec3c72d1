img {
  behavior: url(iepngfix.htc);
}
.instructions {
  color: #ffffff;
  padding: 5px 10px;
  width: 430px;
}
.captchaWrapper {
  background: url('images/bwm-captcha.png') 0 -204px no-repeat;
  height: 112px;
  position: relative;
  width: 433px;
}
a.captchaRefresh:link,
a.captchaRefresh:visited,
a.captchaRefresh:active {
  background: url('images/bwm-captcha.png') -265px -170px no-repeat;
  display: block;
  height: 16px;
  left: 15px;
  position: absolute;
  top: 0;
  width: 88px;
}
a.captchaRefresh:hover {
  background: url('images/bwm-captcha.png') -265px -187px no-repeat;
  display: block;
  height: 16px;
  left: 15px;
  position: absolute;
  top: 0;
  width: 88px;
}
.targetWrapper {
  height: 79px;
  left: 294px;
  position: absolute;
  top: 21px;
  width: 130px;
}
.captchaFail {
  background: url('images/bwm-captcha.png') 0 -122px no-repeat;
  height: 82px;
  width: 132px;
}
.captchaSuccess {
  background: url('images/bwm-captcha.png') -132px -122px no-repeat;
  height: 82px;
  width: 132px;
}
.target {
  background: url('images/bwm-captcha.png');
  height: 55px;
  margin: 13px auto 0 auto;
  width: 56px;
}
.captchaWrapper .draggable {
  background: url('images/bwm-captcha.png');
  cursor: move;
  height: 55px;
  position: absolute;
  top: 35px;
  width: 56px;
  z-index: 10000;
}
