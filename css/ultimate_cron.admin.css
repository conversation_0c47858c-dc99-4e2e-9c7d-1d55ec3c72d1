.ultimate-cron-admin-status a {
  width: 16px;
  height: 16px;
  display: block;
}

.ultimate-cron-admin-status span {
  display: none;
}

.ultimate-cron-admin-status-info,
.ultimate-cron-admin-status-error,
.ultimate-cron-admin-status-success,
.ultimate-cron-admin-status-warning,
.ultimate-cron-admin-status-running {
  background-position: center;
  background-repeat: no-repeat;
}

a.ultimate-cron-admin-status-info,
a.ultimate-cron-admin-status-info:hover,
a.ultimate-cron-admin-status-error,
a.ultimate-cron-admin-status-error:hover,
a.ultimate-cron-admin-status-success,
a.ultimate-cron-admin-status-success:hover,
a.ultimate-cron-admin-status-warning,
a.ultimate-cron-admin-status-warning:hover,
a.ultimate-cron-admin-status-running,
a.ultimate-cron-admin-status-running:hover {
  padding-left: 24px !important;
  background-position: 5px center !important;
  background-repeat: no-repeat !important;
}


.ultimate-cron-admin-status-info {
  background-image: url(../icons/message-16-info.png) !important;
}

.ultimate-cron-admin-status-error {
  background-image: url(../icons/message-16-error.png) !important;
}

.ultimate-cron-admin-status-success {
  background-image: url(../icons/message-16-ok.png) !important;
}

.ultimate-cron-admin-status-warning {
  background-image: url(../icons/message-16-warning.png) !important;
}

.ultimate-cron-admin-status-running {
  background-image: url(../icons/hourglass.png) !important;
}

.ultimate-cron-admin-start,
.ultimate-cron-admin-end,
.ultimate-cron-admin-duration,
.ultimate-cron-admin-rules {
  white-space: nowrap;
}

.ultimate-cron-admin-message {
  white-space: pre-line;
}
