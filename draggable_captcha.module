<?php
use <PERSON><PERSON>al\Core\Site\Settings;
use Drupal\Component\Utility\Crypt;

/**
 * @file
 * Implements Draggable Captcha for use with the CAPTCHA module
 */

define('DRAGGABLE_CAPTCHA_PATH', \Drupal::service('extension.path.resolver')->getPath('module', 'draggable_captcha'));

/**
 * Implements hook_menu_site_status_alter(&$menu_site_status, $path).
 */
function draggable_captcha_menu_site_status_alter(&$menu_site_status, $path) {
  // Skip maintenance mode.
  if ($path == 'draggable-captcha/target-img' || $path == 'draggable-captcha-mini/target-img') {
    $menu_site_status = MENU_SITE_ONLINE;
  }
}

/**
 * Implements hook_theme().
 */
function draggable_captcha_theme() {

  $variables=array(
    'image_buttons' => null,
    'captcha_codes' => null,
    'captcha_sid' => null,
    'base_path' => null
  );

  return array(
    'draggable_captcha' => array(
      'variables' => $variables,
    ),
    'draggable_captcha_mini' => array(
      'variables' => $variables,
    ),
  );
}

/**
 * Implements hook_captcha().
 */
function draggable_captcha_captcha($op, $captcha_type = '', $captcha_sid = NULL) {
  switch ($op) {
    case 'list':
      return array('Draggable Captcha', 'Draggable Captcha Mini');
    case 'generate':
      if ($captcha_type == 'Draggable Captcha') {
        return draggable_captcha_generate_form($captcha_sid);
      }
      if ($captcha_type == 'Draggable Captcha Mini') {
        return draggable_captcha_generate_form($captcha_sid, 'mini');
      }
      break;
  }
}

/**
 * Form builder.
 */
function draggable_captcha_generate_form($captcha_sid, $type = '') {

  $captcha = array();
  $captcha_codes = _draggable_captcha_setup($type);
  $image_buttons = _draggable_captcha_image_buttons($type);

  $captcha['form']['captcha_image'] = array(
    '#theme' => 'draggable_captcha' . ($type?"_".$type:""),
    '#image_buttons' => $image_buttons,
    '#captcha_codes' => $captcha_codes,
    '#captcha_sid' => $captcha_sid,
    '#base_path' => base_path(),
    '#attached' => [
        'library' => ["draggable_captcha/default"],
        "drupalSettings" => [
            'draggable_captcha' => [
                    'captcha_sid' => $captcha_sid
            ]
        ]
    ]
  );
  $captcha['form']['captcha_response'] = array(
    '#type' => 'textfield',
    '#attributes' => array('class' => array('captchaAnswer visually-hidden')),
    '#default_value' => '',
    '#required' => TRUE,
  );

  if ($type) {
    $DraggableCaptchaCodes = 'DraggableCaptchaCodes_'.$type;
    $DraggableCaptchaAnswer = 'DraggableCaptchaAnswer_'.$type;
  } else {
    $DraggableCaptchaCodes = 'DraggableCaptchaCodes';
    $DraggableCaptchaAnswer = 'DraggableCaptchaAnswer';
  }
  // This value is recorded in the captcha_sessions table.
  $captcha['solution'] = 'draggable_' . $_SESSION[$DraggableCaptchaCodes][$_SESSION[$DraggableCaptchaAnswer]];
  $captcha['captcha_validate'] = 'draggable_captcha_custom_validation';

  return $captcha;
}

/**
 * Helper function for captcha validation.
 */
function draggable_captcha_custom_validation($solution, $response, $element, $form_state) {
  if (empty($response)) {
    \Drupal::messenger()->addError(t('Captcha must be finished, try click or drag any shape.')); //@TODO switch to other captcha if can't do this.
    return ;
  }

  return $response == $solution;
}

/**
 * Helper function for generating the Draggable Captcha.
 */
function _draggable_captcha_setup($type = '') {
  $captcha = array();
  $image_buttons = _draggable_captcha_image_buttons($type);

  $key_salt = Settings::getHashSalt();
  $captcha_codes = array();
  foreach ($image_buttons as $key => $value) {
    $captcha_codes[$key] = Crypt::hmacBase64(mt_rand(00000000, 99999999), $key_salt);
  }

  $random_captcha = array_rand($image_buttons);

  if ($type) {
    $DraggableCaptchaCodes = 'DraggableCaptchaCodes_'.$type;
    $DraggableCaptchaAnswer = 'DraggableCaptchaAnswer_'.$type;
  } else {
    $DraggableCaptchaCodes = 'DraggableCaptchaCodes';
    $DraggableCaptchaAnswer = 'DraggableCaptchaAnswer';
  }
  $_SESSION[$DraggableCaptchaAnswer] = $random_captcha;
  $_SESSION[$DraggableCaptchaCodes] = $captcha_codes;

  return $captcha_codes;
}

function _draggable_captcha_log_error($captcha_sid, $action) {
  // Wrong answer.
  \Drupal::database()->update('captcha_sessions')
    ->condition('csid', $captcha_sid)
    ->expression('attempts', 'attempts + 1')
    ->execute();
  // Update wrong response counter.
  \Drupal::state()->set('captcha_wrong_response_counter', \Drupal::state()->get('captcha_wrong_response_counter', 0) + 1);
  // Log to watchdog if needed.
  if (\Drupal::state()->get('captcha_log_wrong_responses', FALSE)) {
    \Drupal::logger('captcha')->notice(
             'Draggable Captcha ajax verify failed on action "%action"',
             array('%action' => $action)
             );
  }
}

/**
 * Helper function for build draggable image buttons.
 */
function _draggable_captcha_image_buttons($type = '') {
  $image_buttons = array(
    'heart' => array(
      'on'	=> array('top' => '0','left' => '-3px'),
      'off'	=> array('top' => '0','left' => '-66px'),
      'target' => array('top' => 0,'left' => 66),
    ),
    'bwm' => array(
      'on'	=> array('top' => '-56px','left' => '-3px'),
      'off'	=> array('top' => '-56px','left' => '-66px'),
      'target' => array('top' => 56,'left' => 66),
    ),
    'star' => array(
      'on'	=> array('top' => '-120px','left' => '-3px'),
      'off'	=> array('top' => '-120px','left' => '-66px'),
      'target' => array('top' => 120,'left' => 66),
    ),
    'diamond' => array(
      'on'	=> array('top' => '-185px','left' => '-3px'),
      'off'	=> array('top' => '-185px','left' => '-66px'),
      'target' => array('top' => 185,'left' => 66),
    ),
  );

  if ($type == 'mini') {
    $image_buttons = array(
    'heart' => array(
      'on'  => array('top' => '1px','left' => '-1px'),
      'off' => array('top' => '1px','left' => '-37px'),
      'target' => array('top' => 0,'left' => 37),
    ),
    'bwm' => array(
      'on'  => array('top' => '-34px','left' => '-1px'),
      'off' => array('top' => '-34px','left' => '-37px'),
      'target' => array('top' => 34,'left' => 37),
    ),
    'star' => array(
      'on'  => array('top' => '-69px','left' => '-1px'),
      'off' => array('top' => '-69px','left' => '-37px'),
      'target' => array('top' => 69,'left' => 37),
    ),
    'diamond' => array(
      'on'  => array('top' => '-106px','left' => '-1px'),
      'off' => array('top' => '-106px','left' => '-37px'),
      'target' => array('top' => 106,'left' => 37),
    ),
    );
  }

  return _draggable_captcha_shuffle_assoc($image_buttons);
}

/**
 * Helper function for array shuffle.
 */
function _draggable_captcha_shuffle_assoc($list) {
  if (!is_array($list)) {
    return $list;
  }

  $keys = array_keys($list);
  shuffle($keys);
  $random = array();
  foreach ($keys as $key) {
    $random[$key] = $list[$key];
  }

  return $random;
}

