# Callback for ajax verify.
draggable_captcha.verify:
  path: '/draggable-captcha/{captcha_sid}/verify'
  defaults:
    _controller: '\Drupal\draggable_captcha\Controller\DraggableCaptchaController::verify'
  requirements:
    _access: 'TRUE'
draggable_captcha_mini.verify:
  path: '/draggable-captcha-mini/{captcha_sid}/verify'
  defaults:
    _controller: '\Drupal\draggable_captcha\Controller\DraggableCaptchaController::verify'
    type: mini
  requirements:
    _access: 'TRUE'
# Callback for generating target image.
draggable_captcha.target:
  path: '/draggable-captcha/target-img'
  defaults:
    _controller: '\Drupal\draggable_captcha\Controller\DraggableCaptchaController::targetImg'
  requirements:
    _access: 'TRUE'
draggable_captcha_mini.target:
  path: '/draggable-captcha-mini/target-img'
  defaults:
    _controller: '\Drupal\draggable_captcha\Controller\DraggableCaptchaController::targetImg'
    type: mini
  requirements:
    _access: 'TRUE'
# Callback for ajax refresh.
draggable_captcha.refresh:
  path: '/draggable-captcha/{captcha_sid}/refresh/ajax'
  defaults:
    _controller: '\Drupal\draggable_captcha\Controller\DraggableCaptchaController::generateRefresh'
  requirements:
    _access: 'TRUE'
draggable_captcha_mini.refresh:
  path: '/draggable-captcha-mini/{captcha_sid}/refresh/ajax'
  defaults:
    _controller: '\Drupal\draggable_captcha\Controller\DraggableCaptchaController::generateRefresh'
    type: mini
  requirements:
    _access: 'TRUE'
