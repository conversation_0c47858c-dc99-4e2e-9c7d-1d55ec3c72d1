<?php

/**
 * @file
 * Font-Your-Face install/update/uninstall hook implementation.
 */

use <PERSON><PERSON>al\Core\Field\FieldStorageDefinitionInterface;
use <PERSON><PERSON><PERSON>\field\Entity\FieldConfig;
use Dr<PERSON>al\field\Entity\FieldStorageConfig;
use Dr<PERSON>al\fontyourface\Entity\Font;
use Drupal\taxonomy\Entity\Vocabulary;

/**
 * Default hook constant.
 */
define('FONTYOURFACE_HOOK_API', 'fontyourface_api');

/**
 * Implements hook_install().
 */
function fontyourface_install() {
  module_set_weight('fontyourface', 1);
  Drupal::messenger()->addMessage(t('If you have not yet enabled any @font-your-face provider modules, please do so. If you have already enabled @font-your-face provider modules, please use the font settings page in the appearance section to import fonts from them.'));
}

/**
 * Implements hook_uninstall().
 */
function fontyourface_uninstall() {
  $vids = [
    'font_classification',
    'font_tags',
    'languages_supported',
    'font_designer',
    'font_foundry',
  ];
  foreach ($vids as $vid) {
    $vocabulary = Vocabulary::load($vid);
    if (!empty($vocabulary)) {
      $vocabulary->delete();
    }
  }
}

/**
 * Updates fonts which are enabled to have enabled property in Font status.
 */
function fontyourface_update_8001() {
  $fonts = Font::loadActivatedFonts();
  foreach ($fonts as $font) {
    $font->enable();
  }
}

/**
 * Adds classification, languages, tags vocabularies and fields from config.
 */
function fontyourface_update_8002() {
  $classification = Vocabulary::create([
    'name' => 'Classification',
    'description' => 'This is the general font type.',
    'vid' => 'font_classification',
  ]);
  $classification->save();

  $languages_supported = Vocabulary::create([
    'name' => 'Languages Supported',
    'description' => 'The languages supported by the font.',
    'vid' => 'languages_supported',
  ]);
  $languages_supported->save();

  $tags = Vocabulary::create([
    'name' => 'Font Tags',
    'description' => 'The tags that came with the font.',
    'vid' => 'font_tags',
  ]);
  $tags->save();

  $foundry = Vocabulary::create([
    'name' => 'Font Foundry',
    'description' => 'Font Foundry',
    'vid' => 'font_foundry',
  ]);
  $foundry->save();

  $designer = Vocabulary::create([
    'name' => 'Font Designer',
    'description' => 'Font Designer',
    'vid' => 'font_designer',
  ]);
  $designer->save();

  // field_classification.
  $classification_field_storage = FieldStorageConfig::create([
    'field_name' => 'field_classification',
    'entity_type' => 'font',
    'translatable' => TRUE,
    'entity_types' => [],
    'settings' => [
      'target_type' => 'taxonomy_term',
    ],
    'type' => 'entity_reference',
    'cardinality' => FieldStorageDefinitionInterface::CARDINALITY_UNLIMITED,
  ]);
  $classification_field_storage->save();

  $classification_field = FieldConfig::create([
    'label' => 'Classification',
    'field_name' => 'field_classification',
    'field_storage' => $classification_field_storage,
    'entity_type' => 'font',
    'bundle' => 'font',
    'settings' => [
      'handler' => 'default:taxonomy_term',
      'handler_settings' => [
        // Reference a single vocabulary.
        'target_bundles' => [
          'font_classification' => 'font_classification',
        ],
        // Enable auto-create.
        'auto_create' => TRUE,
        'auto_create_bundle' => FALSE,
        'sort' => [
          'field' => '_none',
        ],
      ],
    ],
    'translatable' => FALSE,
  ]);
  $classification_field->save();

  // field_supported_languages.
  $supported_languages_field_storage = FieldStorageConfig::create([
    'field_name' => 'field_supported_languages',
    'entity_type' => 'font',
    'translatable' => TRUE,
    'entity_types' => [],
    'settings' => [
      'target_type' => 'taxonomy_term',
    ],
    'type' => 'entity_reference',
    'cardinality' => FieldStorageDefinitionInterface::CARDINALITY_UNLIMITED,
  ]);
  $supported_languages_field_storage->save();

  $supported_languages_field = FieldConfig::create([
    'label' => 'Supported Languages',
    'field_name' => 'field_supported_languages',
    'field_storage' => $supported_languages_field_storage,
    'entity_type' => 'font',
    'bundle' => 'font',
    'settings' => [
      'handler' => 'default:taxonomy_term',
      'handler_settings' => [
        // Reference a single vocabulary.
        'target_bundles' => [
          'languages_supported' => 'languages_supported',
        ],
        // Enable auto-create.
        'auto_create' => TRUE,
        'auto_create_bundle' => FALSE,
        'sort' => [
          'field' => '_none',
        ],
      ],
    ],
    'translatable' => FALSE,
  ]);
  $supported_languages_field->save();

  // field_tags.
  $tags_field_storage = FieldStorageConfig::create([
    'field_name' => 'field_tags',
    'entity_type' => 'font',
    'translatable' => TRUE,
    'entity_types' => [],
    'settings' => [
      'target_type' => 'taxonomy_term',
    ],
    'type' => 'entity_reference',
    'cardinality' => FieldStorageDefinitionInterface::CARDINALITY_UNLIMITED,
  ]);
  $tags_field_storage->save();

  $tags_field = FieldConfig::create([
    'label' => 'Tags',
    'field_name' => 'field_tags',
    'field_storage' => $tags_field_storage,
    'entity_type' => 'font',
    'bundle' => 'font',
    'settings' => [
      'handler' => 'default:taxonomy_term',
      'handler_settings' => [
        // Reference a single vocabulary.
        'target_bundles' => [
          'font_designer' => 'font_designer',
          'font_foundry' => 'font_foundry',
          'font_tags' => 'font_tags',
        ],
        // Enable auto-create.
        'auto_create' => TRUE,
        'auto_create_bundle' => FALSE,
        'sort' => [
          'field' => '_none',
        ],
      ],
    ],
    'translatable' => FALSE,
  ]);
  $tags_field->save();
}

/**
 * Updates fontyourface weights since there are issues around ordering.
 */
function fontyourface_update_8003() {
  // Set the module weight. There is some general Drupal funk around module
  // weights during install.
  module_set_weight(FONTYOURFACE_HOOK_API, 1);
  \Drupal::moduleHandler()->invokeAllWith(FONTYOURFACE_HOOK_API, function (callable $hook, string $module) {
    module_set_weight($module, 10);
  });
}

/**
 * Updates fontyourface font displays to be stored in files.
 */
function fontyourface_update_8004() {
  $pager = 0;
  while (TRUE) {
    $storage_handler = \Drupal::entityTypeManager()->getStorage('font_display');
    $fdids = \Drupal::entityQuery('font_display')
      ->range($pager, 50)
      ->execute();
    if (!empty($fdids)) {
      $styles = $storage_handler->loadMultiple(array_keys($fdids));
      foreach ($styles as $style) {
        fontyourface_save_and_generate_font_display_css($style);
      }
    }
    else {
      break;
    }
    $pager++;
  }
}

/**
 * Ensure to flush all caches.
 */
function fontyourface_update_8005() {
  drupal_flush_all_caches();
}
