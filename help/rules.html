<h3>Fields order</h3>
<pre>
 +---------------- minute (0 - 59)
 |  +------------- hour (0 - 23)
 |  |  +---------- day of month (1 - 31)
 |  |  |  +------- month (1 - 12)
 |  |  |  |  +---- day of week (0 - 7) (Sunday=0)
 |  |  |  |  |
 *  *  *  *  *
</pre>
<p>Each of the patterns from the first five fields may be either * (an asterisk),
which matches all legal values, or a list of elements separated by commas (see below).</p>
<p>For "day of the week" (field 5), 0 is considered Sunday, 6 is Saturday and 7 is also
considered Sunday. Literal names (e.g. mon, tue, wed, etc.) are also accepted.</p>
<p>Literal names are also accepted for months (e.g. jan, feb, mar, etc.).
<p>A job is executed when the time/date specification fields all match the current
time and date. There is one exception: if both "day of month" and "day of week"
are restricted (not "*"), then either the "day of month" field (3) or the "day of week"
field (5) must match the current day (even though the other of the two fields
need not match the current day).</p>

<h3>Fields operators</h3>
<p>There are several ways of specifying multiple date/time values in a field:</p>
<ul>
<li>The comma (',') operator specifies a list of values, for example: "1,3,4,7,8"</li>
<li>The dash ('-') operator specifies a range of values, for example: "1-6", which is equivalent to "1,2,3,4,5,6"</li>
<li>The asterisk ('*') operator specifies all possible values for a field. For example, an asterisk in the hour time field would be equivalent to 'every hour' (subject to matching other specified fields).</li>
<li>The slash ('/') operator (called "step") can be used to skip a given number of values. For example, "*/3" in the hour time field is equivalent to "0,3,6,9,12,15,18,21".</li>
<li>The plus ('+') operator (called "offset") can be used as an offset to a given range. For example, "*/10+2" in the hour or minute field is equivalent to "2,12,22,32,42,52".</li>
<li>The at ('@') operator (called "skew") can be used as an auto calculated value for a given range. The value is calculated per job and is fixed per job. For example, if one job has the skew "2", the rule "*/10+@" in the hour or minute field is equivalent to "2,12,22,32,42,52".</li>
</ul>

<h3>Examples</h3>
<pre>
 */15 * * * *: Execute job every 15 minutes
 */15+@ * * * : Execute job every 15 minutes, but at a different offset for each job
 0 2,14 * * *: Execute job every day at 2:00 and 14:00
 0 2 * * 1-5: Execute job at 2:00 of every working day
 0 12 1 */2 1: Execute job every 2 month, at 12:00 of first day of the month OR at every monday.
</pre>

