<?php

/**
 * @file
 * Provide UI for controlling the mail_system variable.
 */

use Drupal\Core\Routing\RouteMatchInterface;

/**
 * Retrieves the key of the theme used to render the emails.
 *
 * @todo Add some kind of hook to let other modules alter this behavior.
 */
function mailsystem_get_mail_theme() {
  $theme_key = \Drupal::theme()->getActiveTheme()->getName();
  $config = \Drupal::config('mailsystem.settings');
  $theme_config = \Drupal::config('system.theme');

  $theme = $config->get('theme');
  switch ($theme) {
    case 'default':
      $theme = $theme_config->get('default');
      break;

    case 'current':
      $theme = $theme_key;
      break;

    case 'domain':
      // Fetch the theme for the current domain.
      // @todo: Reimplement this as soon as module port or similar module is around.
      if (FALSE && \Drupal::moduleHandler()->moduleExists('domain_theme')) {
        // Assign the selected theme, based on the active domain.
        global $_domain;
        $domain_theme = domain_theme_lookup($_domain['domain_id']);
        // The above returns -1 on failure.
        $theme = ($domain_theme != -1) ? $domain_theme['theme'] : $theme_key;
      }
      break;
  }
  return $theme;
}

/**
 * Implements hook_help().
 */
function mailsystem_help($route_name, RouteMatchInterface $route_match) {
  switch ($route_name) {
    case 'help.page.mailsystem':
      $output = '';
      $output .= '<h3>' . t('About') . '</h3>';
      $output .= '<p>' . t('<a href=":mailsystem">Mail System</a> Provides an Administrative UI and Developers API for managing the used mail backend/plugin.</a>',
          [
            ':mailsystem' => 'http://drupal.org/project/mailsystem',
          ]
        ) . '</p>';

      $output .= '<h3>' . t('Uses') . '</h3>';
      $output .= '<p>' . t('Allows to use different backends for formatting and sending e-mails by default, per module and per mail key. Additionally, a theme can be configured that is used for sent mails.') . '</p>';

      return $output;
  }
}
