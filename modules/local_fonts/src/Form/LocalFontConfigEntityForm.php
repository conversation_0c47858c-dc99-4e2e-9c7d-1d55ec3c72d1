<?php

namespace Drupal\local_fonts\Form;

use <PERSON><PERSON><PERSON>\Component\Utility\Environment;
use <PERSON><PERSON>al\Core\Entity\EntityForm;
use <PERSON><PERSON>al\Core\Entity\EntityTypeManagerInterface;
use <PERSON><PERSON>al\Core\Form\FormStateInterface;
use <PERSON><PERSON>al\Core\Messenger\MessengerInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Config form to set the local fonts.
 *
 * @package Drupal\local_fonts\Form
 */
class LocalFontConfigEntityForm extends EntityForm {

  /**
   * Load.
   *
   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
   */
  protected $entityTypeManager;

  /**
   * Messenger.
   *
   * @var \Drupal\Core\Messenger\MessengerInterface
   */
  protected $messenger;

  /**
   * The class constructor.
   *
   * @param \Drupal\Core\Entity\EntityTypeManagerInterface $entityTypeManager
   *   The entity type manager.
   * @param \Drupal\Core\Messenger\MessengerInterface $messenger
   *   The messenger service.
   */
  public function __construct(EntityTypeManagerInterface $entityTypeManager, MessengerInterface $messenger) {
    $this->entityTypeManager = $entityTypeManager;
    $this->messenger = $messenger;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container) {
    return new static(
      $container->get('entity_type.manager'),
      $container->get('messenger')
    );
  }

  /**
   * {@inheritdoc}
   */
  public function form(array $form, FormStateInterface $form_state) {
    $form = parent::form($form, $form_state);

    $local_font_config_entity = $this->entity;
    $form['label'] = [
      '#type' => 'textfield',
      '#title' => $this->t('Label'),
      '#maxlength' => 255,
      '#default_value' => $local_font_config_entity->label(),
      '#description' => $this->t("Name of the Custom Font. Note that while Font Family is not necessarily unique, this name is."),
      '#required' => TRUE,
    ];

    $form['id'] = [
      '#type' => 'machine_name',
      '#default_value' => $local_font_config_entity->id(),
      '#machine_name' => [
        'exists' => '\Drupal\local_fonts\Entity\LocalFontConfigEntity::load',
      ],
      '#disabled' => !$local_font_config_entity->isNew(),
    ];

    $form['font_family'] = [
      '#type' => 'textfield',
      '#title' => $this->t('Font family'),
      '#default_value' => (isset($local_font_config_entity->font_family)) ? $local_font_config_entity->font_family : '',
      '#description' => $this->t('The CSS Font Family. The @font-face name will be based on this.'),
      '#size' => 60,
      '#maxlength' => 128,
      '#required' => TRUE,
    ];

    $form['font_style'] = [
      '#type' => 'select',
      '#title' => $this->t('Font Style'),
      '#options' => [
        'normal' => $this->t('Normal'),
        'italic' => $this->t('Italics'),
      ],
      '#default_value' => (isset($local_font_config_entity->font_style)) ? $local_font_config_entity->font_style : 'normal',
    ];

    $form['font_weight'] = [
      '#type' => 'select',
      '#title' => $this->t('Font Weight'),
      '#options' => [
        '100' => $this->t('100 (Thin)'),
        '200' => $this->t('200 (Extra Light, Ultra Light)'),
        '300' => $this->t('300 (Light)'),
        'normal' => $this->t('400 (Normal, Book, Regular)'),
        '500' => $this->t('500 (Medium)'),
        '600' => $this->t('600 (Semi Bold, Demi Bold)'),
        '700' => $this->t('700 (Bold)'),
        '800' => $this->t('800 (Extra Bold, Ultra Bold)'),
        '900' => $this->t('900 (Black, Heavy)'),
      ],
      '#default_value' => (isset($local_font_config_entity->font_weight)) ? $local_font_config_entity->font_weight : '400',
    ];

    $form['font_classification'] = [
      '#type' => 'checkboxes',
      '#title' => $this->t('Font Classification'),
      '#description' => $this->t('This is mostly useful for filtering.'),
      '#options' => [
        'serif' => $this->t('Serif'),
        'sans-serif' => $this->t('Sans Serif'),
        'slab-serif' => $this->t('Slab Serif'),
        'handwriting' => $this->t('Handwriting'),
        'decorative' => $this->t('Decorative'),
        'monospace' => $this->t('Monospace'),
      ],
      '#default_value' => (isset($local_font_config_entity->font_classification)) ? $local_font_config_entity->font_classification : [],
    ];

    $form['font_file'] = [
      '#type' => 'managed_file',
      '#title' => $this->t('Font File'),
      '#description' => $this->t('The font file must be in WOFF format since that is accepted by all modern browsers.'),
      '#size' => 50,
      '#upload_validators' => [
        'file_validate_extensions' => ['woff'],
        'file_validate_size' => [Environment::getUploadMaxSize()],
        'file_validate_name_length' => [],
      ],
    ];

    return $form;
  }

  /**
   * {@inheritdoc}
   */
  public function validateForm(array &$form, FormStateInterface $form_state) {
    $local_font_config_entity = $this->entity;

    $values = $form_state->getValues();
    if (empty($values['font_file']) && empty($local_font_config_entity->getFontWoffData())) {
      $form_state->setErrorByName('font_file', $this->t('WOFF file must be uploaded'));
    }
  }

  /**
   * {@inheritdoc}
   */
  public function save(array $form, FormStateInterface $form_state) {
    $values = $form_state->getValues();

    // Save Custom Font Config Entity.
    $local_font_config_entity = $this->entity;
    if (!empty($values['font_file'])) {
      // Get contents of Font File.
      $font_file = $this->entityTypeManager->getStorage('file')->load($values['font_file'][0]);
      $font_file_data = base64_encode(file_get_contents($font_file->getFileUri()));
      $local_font_config_entity->setFontWoffData($font_file_data);
    }
    $status = $local_font_config_entity->save();

    switch ($status) {
      case SAVED_NEW:
        $this->messenger->addMessage($this->t('Created the %label Custom Font.', [
          '%label' => $local_font_config_entity->label(),
        ]));
        break;

      default:
        $this->messenger->addMessage($this->t('Saved the %label Custom Font.', [
          '%label' => $local_font_config_entity->label(),
        ]));
    }
    $form_state->setRedirectUrl($local_font_config_entity->toUrl('collection'));
  }

}
