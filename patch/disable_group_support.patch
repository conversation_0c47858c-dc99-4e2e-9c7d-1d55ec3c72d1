Index: src/BlazyAlter.php
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/src/BlazyAlter.php b/src/BlazyAlter.php
--- a/src/BlazyAlter.php	
+++ b/src/BlazyAlter.php	(date 1686920280588)
@@ -250,9 +250,9 @@
       if ($name = $blazies->get('field.name')) {
         if ($field = ($view->field[$name] ?? NULL)) {
           $options = $field->options ?? [];
-          if (!empty($options['group_rows'])) {
-            $settings['use_theme_field'] = TRUE;
-          }
+//          if (!empty($options['group_rows'])) {
+//            $settings['use_theme_field'] = TRUE;
+//          }
         }
       }
     }
