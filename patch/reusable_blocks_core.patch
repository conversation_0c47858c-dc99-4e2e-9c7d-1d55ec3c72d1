diff --git a/core/modules/layout_builder/layout_builder.permissions.yml b/core/modules/layout_builder/layout_builder.permissions.yml
index 5799cb652b..4a356da83e 100644
--- a/core/modules/layout_builder/layout_builder.permissions.yml
+++ b/core/modules/layout_builder/layout_builder.permissions.yml
@@ -4,6 +4,9 @@ configure any layout:
 create and edit custom blocks:
   title: 'Create and edit content blocks'
   description: 'Manage the single-use blocks within the Layout Builder'
+create reusable blocks:
+  title: 'Create reusable blocks'
+  description: 'Create reusable blocks which can be used in different layouts'

 permission_callbacks:
   - \Drupal\layout_builder\LayoutBuilderOverridesPermissions::permissions
diff --git a/core/modules/layout_builder/src/Controller/ChooseBlockController.php b/core/modules/layout_builder/src/Controller/ChooseBlockController.php
index 687840acdd..d40fb72a07 100644
--- a/core/modules/layout_builder/src/Controller/ChooseBlockController.php
+++ b/core/modules/layout_builder/src/Controller/ChooseBlockController.php
@@ -225,19 +225,62 @@ protected function getBlockLinks(SectionStorageInterface $section_storage, int $
     foreach ($blocks as $block_id => $block) {
       $attributes = $this->getAjaxAttributes();
       $attributes['class'][] = 'js-layout-builder-block-link';
-      $link = [
-        'title' => $block['admin_label'],
-        'url' => Url::fromRoute('layout_builder.add_block',
-          [
-            'section_storage_type' => $section_storage->getStorageType(),
-            'section_storage' => $section_storage->getStorageId(),
-            'delta' => $delta,
-            'region' => $region,
-            'plugin_id' => $block_id,
-          ]
-        ),
-        'attributes' => $attributes,
-      ];
+
+      if ($block['id'] == 'block_content' && $block['category']->render() == 'Content block' && $block['admin_label'] != '') {
+        $parts = explode(':', $block_id);
+        $plugin_id = $parts[0];
+        $uuid = $parts[1];
+        $block_content = $this->entityTypeManager->getStorage('block_content')->loadByProperties(['uuid' => $uuid]);
+        $block_content =  reset($block_content);
+        $current_language = \Drupal::languageManager()->getCurrentLanguage()->getId();
+
+        if ($block_content->hasTranslation($current_language) && $block_content->isReusable()) {
+          $block_translation = $block_content->getTranslation($current_language);
+          $link = [
+            'title' => $block_translation->label(),
+            'url' => Url::fromRoute('layout_builder.add_block',
+              [
+                'section_storage_type' => $section_storage->getStorageType(),
+                'section_storage' => $section_storage->getStorageId(),
+                'delta' => $delta,
+                'region' => $region,
+                'plugin_id' => $plugin_id . ":" . $block_translation->uuid(),
+              ]
+            ),
+            'attributes' => $attributes,
+          ];
+        }
+        else {
+          $link = [
+            'title' => $block['admin_label'],
+            'url' => Url::fromRoute('layout_builder.add_block',
+              [
+                'section_storage_type' => $section_storage->getStorageType(),
+                'section_storage' => $section_storage->getStorageId(),
+                'delta' => $delta,
+                'region' => $region,
+                'plugin_id' => $block_id,
+              ]
+            ),
+            'attributes' => $attributes,
+          ];
+        }
+      }
+      else {
+        $link = [
+          'title' => $block['admin_label'],
+          'url' => Url::fromRoute('layout_builder.add_block',
+            [
+              'section_storage_type' => $section_storage->getStorageType(),
+              'section_storage' => $section_storage->getStorageId(),
+              'delta' => $delta,
+              'region' => $region,
+              'plugin_id' => $block_id,
+            ]
+          ),
+          'attributes' => $attributes,
+        ];
+      }

       $links[] = $link;
     }
diff --git a/core/modules/layout_builder/src/Form/ConfigureBlockFormBase.php b/core/modules/layout_builder/src/Form/ConfigureBlockFormBase.php
index 76f2526d04..e7d3c582a2 100644
--- a/core/modules/layout_builder/src/Form/ConfigureBlockFormBase.php
+++ b/core/modules/layout_builder/src/Form/ConfigureBlockFormBase.php
@@ -3,19 +3,24 @@
 namespace Drupal\layout_builder\Form;

 use Drupal\Component\Utility\Html;
+use Drupal\Component\Utility\NestedArray;
 use Drupal\Component\Uuid\UuidInterface;
 use Drupal\Core\Ajax\AjaxFormHelperTrait;
 use Drupal\Core\Block\BlockManagerInterface;
 use Drupal\Core\Block\BlockPluginInterface;
+use Drupal\Core\Entity\Entity\EntityFormDisplay;
+use Drupal\Core\Entity\EntityTypeManagerInterface;
 use Drupal\Core\Form\BaseFormIdInterface;
 use Drupal\Core\Form\FormBase;
 use Drupal\Core\Form\FormStateInterface;
 use Drupal\Core\Form\SubformState;
+use Drupal\Core\Form\SubformStateInterface;
 use Drupal\Core\Plugin\Context\ContextRepositoryInterface;
 use Drupal\Core\Plugin\ContextAwarePluginAssignmentTrait;
 use Drupal\Core\Plugin\ContextAwarePluginInterface;
 use Drupal\Core\Plugin\PluginFormFactoryInterface;
 use Drupal\Core\Plugin\PluginWithFormsInterface;
+use Drupal\Core\Session\AccountInterface;
 use Drupal\layout_builder\Context\LayoutBuilderContextTrait;
 use Drupal\layout_builder\Controller\LayoutRebuildTrait;
 use Drupal\layout_builder\LayoutTempstoreRepositoryInterface;
@@ -99,6 +104,20 @@ abstract class ConfigureBlockFormBase extends FormBase implements BaseFormIdInte
    */
   protected $sectionStorage;

+  /**
+   * The entity type manager service.
+   *
+   * @var \Drupal\Core\Entity\EntityTypeManagerInterface
+   */
+  protected $entityTypeManager;
+
+  /**
+   * The current user.
+   *
+   * @var \Drupal\Core\Session\AccountInterface
+   */
+  protected $currentUser;
+
   /**
    * Constructs a new block form.
    *
@@ -112,13 +131,19 @@ abstract class ConfigureBlockFormBase extends FormBase implements BaseFormIdInte
    *   The UUID generator.
    * @param \Drupal\Core\Plugin\PluginFormFactoryInterface $plugin_form_manager
    *   The plugin form manager.
+   * @param EntityTypeManagerInterface $entity_type_manager
+   *   The entity type manager service.
+   * @param AccountInterface $current_user
+   *   The current user.
    */
-  public function __construct(LayoutTempstoreRepositoryInterface $layout_tempstore_repository, ContextRepositoryInterface $context_repository, BlockManagerInterface $block_manager, UuidInterface $uuid, PluginFormFactoryInterface $plugin_form_manager) {
+  public function __construct(LayoutTempstoreRepositoryInterface $layout_tempstore_repository, ContextRepositoryInterface $context_repository, BlockManagerInterface $block_manager, UuidInterface $uuid, PluginFormFactoryInterface $plugin_form_manager, EntityTypeManagerInterface $entity_type_manager, AccountInterface $current_user) {
     $this->layoutTempstoreRepository = $layout_tempstore_repository;
     $this->contextRepository = $context_repository;
     $this->blockManager = $block_manager;
     $this->uuidGenerator = $uuid;
     $this->pluginFormFactory = $plugin_form_manager;
+    $this->entityTypeManager = $entity_type_manager;
+    $this->currentUser = $current_user;
   }

   /**
@@ -130,7 +155,9 @@ public static function create(ContainerInterface $container) {
       $container->get('context.repository'),
       $container->get('plugin.manager.block'),
       $container->get('uuid'),
-      $container->get('plugin_form.factory')
+      $container->get('plugin_form.factory'),
+      $container->get('entity_type.manager'),
+      $container->get('current_user')
     );
   }

@@ -164,6 +191,7 @@ public function doBuildForm(array $form, FormStateInterface $form_state, Section
     $this->uuid = $component->getUuid();
     $this->block = $component->getPlugin();

+    $langcode = \Drupal::languageManager()->getCurrentLanguage()->getId();
     $form_state->setTemporaryValue('gathered_contexts', $this->getPopulatedContexts($section_storage));

     $form['#tree'] = TRUE;
@@ -171,6 +199,54 @@ public function doBuildForm(array $form, FormStateInterface $form_state, Section
     $subform_state = SubformState::createForSubform($form['settings'], $form, $form_state);
     $form['settings'] = $this->getPluginForm($this->block)->buildConfigurationForm($form['settings'], $subform_state);

+    if ($this->block->getBaseId() === 'block_content') {
+      // Show the block content form here.
+      /** @var \Drupal\block_content\Plugin\Derivative\BlockContent[] $block_contents */
+      $block_contents = $this->entityTypeManager->getStorage('block_content')->loadByProperties([ 'uuid' => $this->block->getDerivativeId() ]);
+      $block_contents = reset($block_contents);
+      if ($block_contents->hasTranslation($langcode)) {
+        $block_contents = $block_contents->getTranslation($langcode);
+        $form['settings']['admin_label']['#plain_text'] = $block_contents->info->value;
+        $form['settings']['label']['#default_value'] = $block_contents->info->value;
+      }
+      if ($block_contents) {
+        $form['messages'] = [
+          '#theme' => 'status_messages',
+          '#message_list' => [
+            'warning' => [$this->t("This block is reusable! Any changes made will be applied globally.")],
+          ],
+        ];
+        $form['settings']['block_form'] = [
+          '#type' => 'container',
+          '#process' => [[static::class, 'processBlockContentForm']],
+          '#block' => $block_contents,
+          '#access' => $this->currentUser->hasPermission('create and edit custom blocks'),
+        ];
+      }
+    }
+    elseif ($this->block->getBaseId() === 'inline_block') {
+      /** @var \Drupal\block_content\BlockContentInterface $block_content */
+      $block_content = $form['settings']['block_form']['#block'];
+      $form['reusable'] = [
+        '#type' => 'checkbox',
+        '#title' => $this->t('Reusable'),
+        '#description' => $this->t('Would you like to be able to reuse this block? This option can not be changed after saving.'),
+        '#default_value' => $block_content->isReusable(),
+        '#access' => $this->currentUser->hasPermission('create reusable blocks'),
+      ];
+      $form['info'] = [
+        '#type' => 'textfield',
+        '#title' => $this->t('Admin title'),
+        '#description' => $this->t('The title used to find and reuse this block later.'),
+        '#access' => $this->currentUser->hasPermission('create reusable blocks'),
+        '#states' => [
+          'visible' => [
+            ':input[name="reusable"]' => [ 'checked' => TRUE ],
+          ],
+        ],
+      ];
+    }
+
     $form['actions']['submit'] = [
       '#type' => 'submit',
       '#value' => $this->submitLabel(),
@@ -194,6 +270,38 @@ public function doBuildForm(array $form, FormStateInterface $form_state, Section
     return $form;
   }

+  /**
+   * Process callback to insert a Custom Block form.
+   *
+   * @param array $element
+   *   The containing element.
+   * @param \Drupal\Core\Form\FormStateInterface $form_state
+   *   The form state.
+   *
+   * @return array
+   *   The containing element, with the Custom Block form inserted.
+   */
+  public static function processBlockContentForm(array $element, FormStateInterface $form_state) {
+    $langcode = \Drupal::languageManager()->getCurrentLanguage()->getId();
+    /** @var \Drupal\block_content\BlockContentInterface $block */
+    $block = $element['#block'];
+
+    if ($block->hasTranslation($langcode)) {
+      $block = $block->getTranslation($langcode);
+      $element['#block'] = $block;
+      $values = $form_state->getValues();
+      $settings = $values['settings'];
+      $settings['label'] = $block->info->value;
+      $values['settings'] = $settings;
+      $form_state->setValues($values);
+    }
+
+    EntityFormDisplay::collectRenderDisplay($block, 'edit')->buildForm($block, $element, $form_state);
+    $element['revision_log']['#access'] = FALSE;
+    $element['info']['#access'] = FALSE;
+    return $element;
+  }
+
   /**
    * Returns the label for the submit button.
    *
@@ -208,6 +316,23 @@ abstract protected function submitLabel();
   public function validateForm(array &$form, FormStateInterface $form_state) {
     $subform_state = SubformState::createForSubform($form['settings'], $form, $form_state);
     $this->getPluginForm($this->block)->validateConfigurationForm($form['settings'], $subform_state);
+
+    if ($this->block->getBaseId() === 'block_content') {
+      $block_form = $form['settings']['block_form'];
+      /** @var \Drupal\block_content\BlockContentInterface $block_content */
+      $block_content = $block_form['#block'];
+      $langcode = \Drupal::languageManager()->getCurrentLanguage()->getId();
+      if ($block_content->hasTranslation($langcode)) {
+        $block_content = $block_content->getTranslation($langcode);
+        $block_form['#block'] = $block_content;
+      }
+      $form_display = EntityFormDisplay::collectRenderDisplay($block_content, 'edit');
+      $complete_form_state = $form_state instanceof SubformStateInterface ? $form_state->getCompleteFormState() : $form_state;
+      $form_display->extractFormValues($block_content, $block_form, $complete_form_state);
+      $form_display->validateFormValues($block_content, $block_form, $complete_form_state);
+      // @todo Remove when https://www.drupal.org/project/drupal/issues/2948549 is closed.
+      $form_state->setTemporaryValue('block_form_parents', $block_form['#parents']);
+    }
   }

   /**
@@ -225,6 +350,40 @@ public function submitForm(array &$form, FormStateInterface $form_state) {

     $configuration = $this->block->getConfiguration();

+    if ($this->block->getBaseId() === 'block_content' && isset($form['settings']['block_form'])) {
+      // @todo Remove when https://www.drupal.org/project/drupal/issues/2948549 is closed.
+      $block_form = NestedArray::getValue($form, $form_state->getTemporaryValue('block_form_parents'));
+      /** @var \Drupal\block_content\BlockContentInterface $block_content */
+      $block_content = $block_form['#block'];
+      $form_display = EntityFormDisplay::collectRenderDisplay($block_content, 'edit');
+      $complete_form_state = $form_state instanceof SubformStateInterface ? $form_state->getCompleteFormState() : $form_state;
+      $form_display->extractFormValues($block_content, $block_form, $complete_form_state);
+      $block_content->save();
+    }
+    // If the block got marked as reusable, then convert the inline_block plugin
+    // to a block_content plugin.
+    elseif ($this->block->getBaseId() === 'inline_block' && $form_state->getValue('reusable')) {
+      $block_info = $form_state->getValue('info');
+      if (empty($block_info)) {
+        $block_info = $form_state->getValue('settings')['label'];
+      }
+      /** @var \Drupal\block_content\BlockContentInterface $block_content */
+      $block_content = $form['settings']['block_form']['#block'];
+      $block_content->setReusable();
+      $block_content->setInfo($block_info);
+      $block_content->save();
+
+      $block_label_display = $form_state->getValue('settings')['label_display'];
+      $this->block = $this->blockManager->createInstance('block_content:' . $block_content->uuid(), [
+        'view_mode' => $configuration['view_mode'],
+        'label' => $configuration['label'],
+        'type' => $block_content->bundle(),
+        'uuid' => $block_content->uuid(),
+        'label_display' => $block_label_display
+      ]);
+      $configuration = $this->block->getConfiguration();
+    }
+
     $section = $this->sectionStorage->getSection($this->delta);
     $section->getComponent($this->uuid)->setConfiguration($configuration);
