redirect_after_login.admin_settings:
  path: '/admin/config/people/redirect'
  defaults:
    _form: '\Drupal\redirect_after_login\Form\LoginRedirectionForm'
    _title: 'Redirect After Login configuration'
  requirements:
    _permission: 'administer redirect_after_login settings'

redirect_after_login.admin_settings_redirect:
  path: 'admin/config/system/redirect'
  defaults:
    _controller: '\Drupal\redirect_after_login\Controller\LoginSettingsRedirection::settingsRedirect'
  requirements:
    _permission: 'administer redirect_after_login settings'
