<?php

namespace <PERSON><PERSON>al\theme_switcher\Form;

use Dr<PERSON>al\Core\Entity\EntityConfirmFormBase;
use <PERSON><PERSON>al\Core\Form\FormStateInterface;
use <PERSON>upal\Core\Logger\LoggerChannelInterface;
use <PERSON><PERSON>al\Core\Messenger\MessengerInterface;
use <PERSON><PERSON>al\Core\Url;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Builds the form to delete a theme_switcher_rule.
 */
class ThemeSwitcherRuleDeleteForm extends EntityConfirmFormBase {

  /**
   * The Messenger service.
   *
   * @var \Drupal\Core\Messenger\MessengerInterface
   */
  protected $messenger;

  /**
   * The logger factory.
   *
   * @var \Drupal\Core\Logger\LoggerChannelInterface
   */
  protected $logger;

  /**
   * Constructs an SwitchThemeRuleForm object.
   *
   * @param \Drupal\Core\Messenger\MessengerInterface $messenger
   *   The messenger.
   * @param \Drupal\Core\Logger\LoggerChannelInterface $logger
   *   The logger.
   */
  public function __construct(MessengerInterface $messenger, LoggerChannelInterface $logger) {
    $this->messenger = $messenger;
    $this->logger = $logger;
  }

  /**
   * {@inheritdoc}
   */
  public static function create(ContainerInterface $container) {
    return new static(
      $container->get('messenger'),
      $container->get('logger.factory')->get('theme_switcher')
    );
  }

  /**
   * {@inheritdoc}
   */
  public function getQuestion() {
    return $this->t('Are you sure you want to delete %name?',
      ['%name' => $this->entity->label()]
    );
  }

  /**
   * {@inheritdoc}
   */
  public function getCancelUrl() {
    return new Url('theme_switcher.admin');
  }

  /**
   * {@inheritdoc}
   */
  public function getConfirmText() {
    return $this->t('Delete');
  }

  /**
   * {@inheritdoc}
   */
  public function submitForm(array &$form, FormStateInterface $form_state) {
    $this->entity->delete();

    $message = $this->t("The Theme Switcher Rule '%label' has been deleted.",
      ['%label' => $this->entity->label()]
    );
    $this->messenger->addStatus($message);
    $this->logger->notice($message);

    $form_state->setRedirectUrl($this->getCancelUrl());
  }

}
