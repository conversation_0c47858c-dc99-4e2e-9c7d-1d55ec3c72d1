{#

/**
 * @file
 * Default template implementation.
 *
 * Available variables:
 * - image_buttons: An array of image button positions.
 * - captcha_codes: An array of hash codes for image buttons.
 */

#}

{% if image_buttons and captcha_codes and captcha_sid %}
<div id="draggable-captcha-mini">
  <label>
    {{ 'Drag or click the correct shape to the grey "drop area".' | t }}
  </label>
  <div class="captchaWrapper-mini" id="captchaWrapper-mini">
    {% for image_button,image_button_position in image_buttons %}
      <div id="draggable_{{captcha_codes[image_button]}}" class="draggable"
        style="left:{{(loop.index-1)*40+8}}px;background-position:{{image_button_position['on']['top']~' '~image_button_position['on']['left']}}">
      </div>
    {% endfor %}
    <div class="targetWrapper">
      <div class="target" style="background:none;">
        <img alt="Target Image" src="{{base_path}}draggable-captcha-mini/target-img?v{{random(1000..9999)}}" />
      </div>
    </div>
  </div>
  <a title={{ 'Refresh'|t }} href="{{base_path}}draggable-captcha-mini/{{captcha_sid}}/refresh/nojs/" class="captchaRefresh use-ajax">⟳ {{ 'Refresh'|t }}</a>
</div>
{% endif %}
