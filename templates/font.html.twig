{#
/**
 * @file font.html.twig
 * Default theme implementation to present Font data.
 *
 * This template is used when viewing Font pages.
 *
 *
 * Available variables:
 * - content: A list of content items. Use 'content' to print all content, or
 * - attributes: HTML attributes for the container element.
 *
 * @see template_preprocess_font()
 *
 * @ingroup themeable
 */
#}
<div{{ attributes.addClass('font') }}>
  {% if font_style %}
    <div>If you wish to use the font in your theme / css file, use:</div>
    <code>
      {{- font_style }}
    </code>
  {% endif %}
  <details open>
    <summary>Details</summary>
    <div class="details-wrapper">
      {% if content %}
        {{- content -}}
      {% endif %}
    </div>
  </details>
  <detail open>
    <summary>Preview</summary>
    <div class="fontyourface-preview" style="{{- font_style_inline }}">
      <h1>h1. This is a very large header.</h1>
      <h2>h2. This is a large header.</h2>
      <h3>h3. This is a medium header.</h3>
      <h4>h4. This is a moderate header.</h4>
      <h5>h5. This is a small header.</h5>
      <h6>h6. This is a tiny header.</h6>
      
      <br>
      
      <h1 class="subheader">h1. subheader</h1>
      <h2 class="subheader">h2. subheader</h2>
      <h3 class="subheader">h3. subheader</h3>
      <h4 class="subheader">h4. subheader</h4>
      <h5 class="subheader">h5. subheader</h5>
      <h6 class="subheader">h6. subheader</h6>
      
      <hr>
      
      <h3>Definition List</h3>
      <h5>Definition lists are great for small block of copy that describe the header</h5>
      <dl>
      <dt>Lower cost</dt>
        <dd>The new version of this product costs significantly less than the previous one!</dd>
      <dt>Easier to use</dt>
        <dd>We've changed the product so that it's much easier to use!</dd>
      <dt>Safe for kids</dt>
        <dd>You can leave your kids alone in a room with this product and they won't get hurt (not a guarantee).</dd>
      </dl>
      <hr>
      
      <h5>Un-ordered lists are great for making quick outlines bulleted.</h5>
      <ul class="disc">
        <li>List item with a much longer description or more content.</li>
        <li>List item</li>
        <li>List item
          <ul>
            <li>Nested List Item</li>
            <li>Nested List Item</li>
            <li>Nested List Item</li>
          </ul>
        </li>
        <li>List item</li>
        <li>List item</li>
        <li>List item</li>
      </ul>
      
      <h5>Ordered lists are great for lists that need order, duh.</h5>
      <ol>
        <li>List Item 1</li>
        <li>List Item 2</li>
        <li>List Item 3</li>
      </ol>
      
      
      
      <br>
      <h5>Blockquote</h5>
      <blockquote>I do not fear computers. I fear the lack of them. Maecenas faucibus mollis interdum. Aenean lacinia bibendum nulla sed consectetur.<cite>Isaac Asimov</cite></blockquote>
      
      <br>
      <h5>Vcard</h5>
      <ul class="vcard">
        <li class="fn">Gaius Baltar</li>
        <li class="street-address">123 Colonial Ave.</li>
        <li class="locality">Caprica City</li>
        <li><span class="state">Caprica</span>, <span class="zip">12345</span></li>
        <li class="email"><a href="#"><EMAIL></a></li>
      </ul>
    </div>
  </detail>
</div>
