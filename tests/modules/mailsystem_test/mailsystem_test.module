<?php

/**
 * @file
 * Enables the use of personal and site-wide contact forms.
 */

use Drupal\Core\StringTranslation\TranslatableMarkup;
use Drupal\user\Entity\User;

/**
 * Implements hook_mail().
 */
function mailsystem_test_mail($key, &$message, $params) {
  switch ($key) {
    case 'theme_test':
      $account = User::load(\Drupal::currentUser()->id());
      $username = ['#theme' => 'username', '#account' => $account];

      $message['subject'] = new TranslatableMarkup('Testing mail theme.');
      $message['body'][] = (string) \Drupal::service('renderer')->renderPlain($username);
      break;
  }
}
