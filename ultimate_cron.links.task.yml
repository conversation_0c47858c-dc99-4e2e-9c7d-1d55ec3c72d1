ultimate_cron.job_list:
  title: '<PERSON><PERSON> Jobs'
  route_name: entity.ultimate_cron_job.collection
  base_route: entity.ultimate_cron_job.collection

ultimate_cron.run_cron:
  title: 'Run cron'
  route_name: system.cron_settings
  base_route: entity.ultimate_cron_job.collection

ultimate_cron.settings:
  title: 'Cron settings'
  route_name: ultimate_cron.settings
  base_route: entity.ultimate_cron_job.collection

ultimate_cron.general_settings:
  title: Queue
  route_name: ultimate_cron.general_settings
  parent_id: ultimate_cron.settings

ultimate_cron.launcher_settings:
  title: Launcher
  route_name: ultimate_cron.launcher_settings
  parent_id: ultimate_cron.settings

ultimate_cron.logger_settings:
  title: Logger
  route_name: ultimate_cron.logger_settings
  parent_id: ultimate_cron.settings

ultimate_cron.scheduler_settings:
  title: Scheduler
  route_name: ultimate_cron.scheduler_settings
  parent_id: ultimate_cron.settings
