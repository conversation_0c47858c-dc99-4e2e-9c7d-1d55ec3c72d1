# Upstream Configuration

If you are working on your custom upstream, use this folder for upstream specific stuff.

You can require the `pantheon-systems/upstream-management` [composer package](https://packagist.org/packages/pantheon-systems/upstream-management) to get access to some upstream management tools. Require it with composer like this:

```
composer require pantheon-systems/upstream-management:^1
```

Read project description for more information about the tools that it contains.
