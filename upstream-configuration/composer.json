{"name": "pantheon-upstreams/upstream-configuration", "type": "project", "version": "dev-main", "repositories": [{"type": "composer", "url": "https://packages.drupal.org/8"}], "require": {"drupal/address": "^2.0@beta", "drupal/addtoany": "^2.0", "drupal/adminimal_admin_toolbar": "^1.x-dev@dev", "drupal/adminimal_theme": "^1.7", "drupal/antibot": "^2.0", "drupal/bakery": "^3.0@alpha", "drupal/bartik": "^1.0", "drupal/better_exposed_filters": "^6.0", "drupal/blazy": "2.16", "drupal/block_list_override": "^1.0", "drupal/captcha": "^2.0", "drupal/ckeditor_accordion": "^2.0", "drupal/ckeditor_font": "^2.0", "drupal/color": "^1.0", "drupal/color_field": "^3.0", "drupal/colorbutton": "^1.2", "drupal/csp": "^2.2", "drupal/csp_log": "^1.0", "drupal/devel": "^5.1", "drupal/devel_php": "^1.3", "drupal/draggable_captcha": "2.1.x-dev@dev", "drupal/editor_advanced_link": "^2.2", "drupal/elevatezoomplus": "1.8", "drupal/entity_browser": "^2.5", "drupal/entity_browser_block": "^1.3", "drupal/entityqueue": "^1.6", "drupal/events_log_track": "^4.0.2", "drupal/facebook_pixel": "^2.0@RC", "drupal/feeds": "^3.0", "drupal/feeds_tamper": "^2.0", "drupal/flags": "^1.0@beta", "drupal/fontyourface": "^3.x-dev", "drupal/google_analytics": "^4.0", "drupal/google_tag": "^2.0.8", "drupal/hcaptcha": "^1.1", "drupal/honeypot": "2.1.4", "drupal/image_effects": "^3.0", "drupal/jquery_ui_touch_punch": "^1.1", "drupal/key_auth": "^2.1", "drupal/layout_builder_at": "^2.11", "drupal/layout_builder_modal": "^1.1", "drupal/layout_builder_st": "^1.0@alpha", "drupal/linkit": "^6.0", "drupal/menu_item_fields": "^1.10", "drupal/metatag": "^2.0", "drupal/pager_serializer": "^1.2", "drupal/panelbutton": "^1.5", "drupal/path_redirect_import": "^2.0", "drupal/pathauto": "^1.8", "drupal/quickedit": "^1.0", "drupal/r4032login": "^2.2", "drupal/rdf": "^2.1", "drupal/recaptcha": "^3.2", "drupal/redirect": "^1.6", "drupal/redirect_after_login": "^2.x-dev", "drupal/responsive_tables_filter": "^1.5", "drupal/search_api": "^1.17", "drupal/seckit": "^2.0", "drupal/seven": "^1.0", "drupal/shy": "^2.0", "drupal/simple_sitemap": "^4.0", "drupal/slick": "2.9", "drupal/slick_lightbox": "^1.2", "drupal/slick_views": "2.7", "drupal/smtp": "^1.2", "drupal/spamaway": "^2.0", "drupal/spamicide": "^1.0@beta", "drupal/spammaster": "^2.30", "drupal/symfony_mailer": "^1.4@beta", "drupal/tmgmt": "^1.17", "drupal/tmgmt_google": "^1.2", "drupal/token": "^1.7", "drupal/turnstile": "^1.1", "drupal/twig_tweak": "^3.2", "drupal/unique_entity_title": "^1.2", "drupal/upgrade_status": "^4.0", "drupal/video_embed_field": "^3.0@beta", "drupal/views_block_filter_block": "^2.0", "drupal/views_infinite_scroll": "^2.0", "drupal/webform": "^6.2@beta", "drupal/webform_spam_words": "^2.0", "drupal/xmlsitemap": "^1.4", "league/csv": "^9.5", "phpoffice/phpspreadsheet": "^1.14", "symfony/polyfill-php81": "^1.27", "drupal/webform_pardot": "^2.0@alpha", "pantheon-systems/upstream-management": "^1", "drupal/core-recommended": "^10", "drupal/ultimate_cron": "2.x-dev", "drupal/mailsystem": "4.x-dev", "drupal/devel_entity_updates": "4.1.x-dev", "drupal/dashboards": "^2.1", "drupal/gin": "^3.0@RC", "drupal/gin_toolbar": "^1.0@RC", "drupal/theme_switcher": "2.x-dev", "drupal/entity_reference_revisions": "^1.10", "drupal/node_view_permissions": "^1.6", "drupal/phone_number": "^2.0@alpha", "drupal/email_verification": "^1.2", "drupal/advanced_email_validation": "^1.2", "drupal/user_register_notify": "^2.0@beta", "drupal/link_attributes": "^2.1", "drupal/entity_delete_log": "^1.1", "drupal/ckeditor_media_resize": "^1.0", "drupal/layout_builder_block_clone": "^1.3", "drupal/entity_clone": "^2.0@beta", "drupal/views_data_export": "^1.5", "drupal/ai": "^1.0", "drupal/ai_provider_openai": "^1.1@beta", "drupal/ai_image_alt_text": "^1.0", "drupal/ai_summarize_document": "^1.0@alpha", "drupal/auto_translation": "^1.4", "drupal/ai_tmgmt": "^1.0@beta", "drupal/key": "^1.20", "drupal/ckeditor5_font": "^1.1@beta", "drupal/ckeditor5_plugin_pack": "^1.3", "drupal/ckeditor_templates": "^2.0@beta", "drupal/tfa": "^2.0@alpha", "deeplcom/deepl-php": "^1.7", "drupal/ai_provider_deepl": "^1.0@alpha"}, "minimum-stability": "dev", "extra": {"patches": {"drupal/blazy": {"Disable supports for Views 'group_rows'": "patch/disable_group_support.patch"}, "drupal/bakery": {"Fix settings form": "patch/bakery_fix_settings_form.patch", "Fix cookies issues": "patch/bakery_fix_cookies.patch"}, "drupal/video_embed_media": {"Fix for ckeditor5": "patch/video_embed_ckeditor5_fix.patch"}, "drupal/ckeditor5_font": {"'no config schema' Error": "https://www.drupal.org/files/issues/2024-07-18/ckeditor5_font-schema-fix-10_3_x-3368736-53.patch"}}, "_README": "To make a custom upstream, clone this repository and add any dependencies to be provided by this upstream to this composer.json file. Leave the root-level composer.json file for the exclusive use of each individual site; do not modify it after your custom upstream has been published. See https://pantheon.io/docs/create-custom-upstream for more information."}, "config": {"allow-plugins": {"pantheon-systems/upstream-management": true, "php-http/discovery": true}}}