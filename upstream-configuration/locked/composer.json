{"name": "pantheon-upstreams/upstream-configuration", "type": "project", "version": "dev-main", "repositories": [{"type": "composer", "url": "https://packages.drupal.org/8"}], "require": {"drupal/address": "2.0.0-beta2", "drupal/addtoany": "dev-2.0.x", "drupal/adminimal_admin_toolbar": "dev-1.x", "drupal/adminimal_theme": "dev-1.x", "drupal/antibot": "dev-2.0.x", "drupal/bakery": "3.0.0-alpha1", "drupal/better_exposed_filters": "dev-6.0.x", "drupal/blazy": "dev-2.x", "drupal/block_list_override": "dev-1.0.x", "drupal/captcha": "dev-2.x", "drupal/ckeditor_accordion": "dev-2.x", "drupal/ckeditor_entity_link": "dev-1.x", "drupal/ckeditor_font": "dev-2.0.x", "drupal/color": "dev-1.x", "drupal/color_field": "dev-3.0.x", "drupal/colorbutton": "dev-1.x", "drupal/cookiepro": "2.0.1", "drupal/devel": "dev-5.x", "drupal/devel_php": "dev-1.x", "drupal/editor_advanced_link": "dev-2.x", "drupal/elevatezoomplus": "dev-1.x", "drupal/entity_browser": "dev-2.x", "drupal/entity_browser_block": "dev-1.x", "drupal/entityqueue": "dev-1.x", "drupal/events_log_track": "3.1.6", "drupal/facebook_pixel": "2.0.0-rc4", "drupal/feeds": "dev-3.x", "drupal/feeds_tamper": "dev-2.x", "drupal/flags": "1.0.0-beta4", "drupal/fontyourface": "dev-3.x", "drupal/google_analytics": "dev-4.x", "drupal/google_tag": "dev-2.0.x", "drupal/hcaptcha": "dev-1.x", "drupal/honeypot": "dev-2.1.x", "drupal/image_effects": "dev-3.x", "drupal/jquery_ui_touch_punch": "1.1.0", "drupal/key_auth": "2.1.0", "drupal/layout_builder_at": "dev-2.x", "drupal/layout_builder_modal": "dev-1.x", "drupal/layout_builder_st": "1.0.0-alpha3", "drupal/linkit": "dev-6.0.x", "drupal/menu_item_fields": "dev-1.x", "drupal/metatag": "dev-2.0.x", "drupal/pager_serializer": "dev-1.x", "drupal/panelbutton": "dev-1.x", "drupal/path_redirect_import": "dev-2.0.x", "drupal/pathauto": "dev-1.x", "drupal/quickedit": "dev-1.0.x", "drupal/rdf": "dev-2.x", "drupal/recaptcha": "dev-3.x", "drupal/redirect": "dev-1.x", "drupal/redirect_after_login": "dev-2.x", "drupal/responsive_tables_filter": "dev-1.x", "drupal/search_api": "dev-1.x", "drupal/seckit": "dev-2.x", "drupal/seven": "1.0.0", "drupal/shy": "dev-2.x", "drupal/simple_sitemap": "dev-4.x", "drupal/slick": "dev-2.x", "drupal/slick_lightbox": "dev-1.x", "drupal/slick_views": "dev-2.x", "drupal/smtp": "dev-1.x", "drupal/tmgmt": "dev-1.x", "drupal/tmgmt_google": "dev-1.x", "drupal/token": "dev-1.x", "drupal/twig_tweak": "dev-3.x", "drupal/unique_entity_title": "dev-1.x", "drupal/upgrade_status": "dev-4.x", "drupal/video_embed_field": "dev-2.x", "drupal/views_block_filter_block": "dev-2.0.x", "drupal/views_infinite_scroll": "dev-2.0.x", "drupal/webform": "6.2.0-beta6", "drupal/wkhtmltopdf": "dev-1.x", "drupal/xmlsitemap": "dev-1.x", "league/csv": "dev-master", "phpoffice/phpspreadsheet": "1.29.0", "symfony/polyfill-php81": "1.x-dev", "drupal/webform_pardot": "2.0.0-alpha1", "pantheon-systems/upstream-management": "1.0", "drupal/core-recommended": "9.5.x-dev", "drupal/ultimate_cron": "dev-2.x", "drupal/mailsystem": "dev-4.x", "asm89/stack-cors": "1.3.0", "commerceguys/addressing": "dev-master", "composer/semver": "3.3.2", "doctrine/annotations": "1.13.x-dev", "doctrine/collections": "2.1.x-dev", "doctrine/common": "3.5.x-dev", "doctrine/deprecations": "1.1.x-dev", "doctrine/event-manager": "2.0.x-dev", "doctrine/lexer": "1.2.x-dev", "doctrine/persistence": "3.3.x-dev", "doctrine/reflection": "1.2.x-dev", "drupal-ckeditor-libraries-group/font": "4.20.1", "drupal/admin_toolbar": "dev-3.x", "drupal/ckeditor": "dev-1.0.x", "drupal/core": "9.5.x-dev", "drupal/ctools": "dev-4.x", "drupal/file_mdm": "dev-2.x", "drupal/jquery_ui": "dev-1.x", "drupal/jquery_ui_datepicker": "dev-2.x", "drupal/jquery_ui_slider": "dev-2.x", "drupal/migrate_source_csv": "dev-3.x", "drupal/migrate_tools": "dev-6.0.x", "drupal/tamper": "dev-1.x", "easyrdf/easyrdf": "1.1.1", "egulias/email-validator": "3.2.6", "ezyang/htmlpurifier": "v4.16.0", "fileeye/pel": "0.9.20", "google/recaptcha": "dev-master", "guzzlehttp/guzzle": "6.5.x-dev", "guzzlehttp/promises": "1.5.x-dev", "guzzlehttp/psr7": "1.9.x-dev", "laminas/laminas-escaper": "2.13.x-dev", "laminas/laminas-feed": "2.22.x-dev", "laminas/laminas-servicemanager": "3.22.x-dev", "laminas/laminas-stdlib": "3.18.x-dev", "laminas/laminas-text": "2.11.x-dev", "longwave/laminas-diactoros": "2.14.x-dev", "maennchen/zipstream-php": "3.1.0", "markbaker/complex": "3.0.x-dev", "markbaker/matrix": "3.0.x-dev", "masterminds/html5": "dev-master", "mathieuviossat/arraytotexttable": "v1.0.9", "mglaman/phpstan-drupal": "1.1.x-dev", "nikic/php-parser": "4.x-dev", "pear/archive_tar": "dev-master", "pear/console_getopt": "v1.4.3", "pear/pear-core-minimal": "v1.10.13", "pear/pear_exception": "dev-master", "phenx/php-font-lib": "0.5.4", "phpmailer/phpmailer": "v6.8.0", "phpseclib/mcrypt_compat": "1.0.x-dev", "phpseclib/phpseclib": "2.0.x-dev", "phpstan/phpstan": "1.11.x-dev", "phpstan/phpstan-deprecation-rules": "1.2.x-dev", "politsin/jquery-ui-touch-punch": "1.0", "psr/cache": "1.0.1", "psr/container": "1.1.x-dev", "psr/http-client": "dev-master", "psr/http-factory": "dev-master", "psr/http-message": "1.0.1", "psr/log": "1.1.4", "psr/simple-cache": "dev-master", "ralouphie/getallheaders": "3.0.3", "stack/builder": "dev-master", "symfony-cmf/routing": "2.3.4", "symfony/console": "4.4.x-dev", "symfony/debug": "4.4.x-dev", "symfony/dependency-injection": "4.4.x-dev", "symfony/deprecation-contracts": "2.5.x-dev", "symfony/error-handler": "4.4.x-dev", "symfony/event-dispatcher": "4.4.x-dev", "symfony/event-dispatcher-contracts": "1.1.x-dev", "symfony/finder": "6.4.x-dev", "symfony/http-client-contracts": "2.5.x-dev", "symfony/http-foundation": "4.4.x-dev", "symfony/http-kernel": "4.4.x-dev", "symfony/mime": "v5.4.13", "symfony/polyfill-ctype": "v1.27.0", "symfony/polyfill-iconv": "v1.27.0", "symfony/polyfill-intl-idn": "v1.27.0", "symfony/polyfill-intl-normalizer": "v1.27.0", "symfony/polyfill-mbstring": "v1.27.0", "symfony/polyfill-php72": "1.x-dev", "symfony/polyfill-php73": "1.x-dev", "symfony/polyfill-php80": "v1.27.0", "symfony/process": "4.4.x-dev", "symfony/psr-http-message-bridge": "v2.1.4", "symfony/routing": "4.4.x-dev", "symfony/serializer": "4.4.x-dev", "symfony/service-contracts": "2.5.x-dev", "symfony/translation": "4.4.x-dev", "symfony/translation-contracts": "2.5.x-dev", "symfony/validator": "4.4.x-dev", "symfony/var-dumper": "5.4.x-dev", "symfony/yaml": "4.4.x-dev", "twig/twig": "v2.15.5", "typo3/phar-stream-wrapper": "v3.1.7", "webflo/drupal-finder": "1.2.2"}, "minimum-stability": "dev", "extra": {"patches": {"drupal/blazy": {"Disable supports for Views 'group_rows'": "patch/disable_group_support.patch"}, "drupal/adminimal_admin_toolbar": {"Drupal 10 fixes": "https://www.drupal.org/files/issues/2022-06-15/adminimal_admin_toolbar.1.x-dev.rector.patch"}, "drupal/cookiepro": {"Drupal 10 fixes": "https://www.drupal.org/files/issues/2022-09-27/3286785.cookiepro.drupal10_compatibility.patch"}}, "_README": "To make a custom upstream, clone this repository and add any dependencies to be provided by this upstream to this composer.json file. Leave the root-level composer.json file for the exclusive use of each individual site; do not modify it after your custom upstream has been published. See https://pantheon.io/docs/create-custom-upstream for more information."}, "config": {"allow-plugins": {"pantheon-systems/upstream-management": true}}}