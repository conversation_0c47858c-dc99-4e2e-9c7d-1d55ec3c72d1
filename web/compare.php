<?php

/**
 * @file
 * The PHP page that serves all page requests on a Drupal installation.
 *
 * All Drupal code is released under the GNU General Public License.
 * See COPYRIGHT.txt and LICENSE.txt files in the "core" directory.
 */

use Drupal\Core\DrupalKernel;
use Symfony\Component\HttpFoundation\Request;

$autoloader = require_once 'autoload.php';

$request = Request::createFromGlobals();

$kernel = DrupalKernel::createFromRequest($request, $autoloader, 'prod');

$kernel->boot();

//require_once 'core/includes/schema.inc';



$url = $_GET['fname'];
$ext = pathinfo($url)['extension'] ?? false;
$inputFileType = 'Csv';
// the different exts it will take
//    $inputFileType = 'Xlsx';
//    $inputFileType = 'Xls';
//    $inputFileType = 'Xml';
//    $inputFileType = 'Ods';
//    $inputFileType = 'Slk';
//    $inputFileType = 'Gnumeric';
//    $inputFileType = 'Csv';
$reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader($inputFileType);
$reader->setReadDataOnly(true);
$reader->setReadEmptyCells(false);
/**  Load $inputFileName to a Spreadsheet Object  **/
$spreadsheet = $reader->load($url);
$worksheet = $spreadsheet->getActiveSheet();
$data = $worksheet->toArray();
$data = array_map('array_filter', $data);
$data = array_filter($data);
$data = array_values($data);
$data = array_map('array_values', $data);
unset($data[0]);

$p = array_column($data, 3);

$query = \Drupal::entityQuery('node');
$query->condition('type', 'product'); //optional condition to filter results by
$query->accessCheck(False);
$node_ids = $query->execute();
$nodes = \Drupal::entityTypeManager()->getStorage('node')->loadMultiple($node_ids); //process the nodes however is desired

$matched_products = [];

echo '<h1>Drupal Products Not Found in Export</h1><br>';
echo 'Name,Drupal ID<br>';
foreach($nodes as $node){
	$test = array_search($node->getTitle(), $p);

	if ($test === false) {
		echo $node->getTitle() . ',';
		echo $node->id() . '<br>';
	} else {
		$matched_products[] = $p[$test];
	}
	//		echo 'Drupal ID: ' . $node->id() . ' : '. $data[$test+1][0] . '<br>';
}

$already_output = [];
echo '<h1>Products in Export not matched to drupal product</h1><br>';
echo "Name,LabWare ID,ID<br>";
foreach($data as $d){
	$seek = array_search($d[3], $matched_products);
	if ($seek === false && !isset($already_output[$d[0]])) {
		$already_output[$d[0]] = true;
		echo $d[3] . ',';
		echo $d[0] . ',';
		echo $d[1] . '<br>';
	}
}
