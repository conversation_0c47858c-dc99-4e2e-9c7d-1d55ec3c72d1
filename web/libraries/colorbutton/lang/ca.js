/*
Copyright (c) 2003-2020, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
*/
CKEDITOR.plugins.setLang( 'colorbutton', 'ca', {
	auto: 'Automàtic',
	bgColorTitle: 'Color de Fons',
	colors: {
		'000': 'Negre',
		'800000': 'Grana',
		'8B4513': 'Marró sella',
		'2F4F4F': '<PERSON>ris pissarra fosca',
		'008080': 'Blau xarxet',
		'000080': 'Blau marí',
		'4B0082': 'Indi',
		'696969': 'Gris Fosc',
		B22222: 'Foc Maó',
		A52A2A: 'Marró',
		DAA520: 'Solidago',
		'006400': 'Verd Fosc',
		'40E0D0': 'Turquesa',
		'0000CD': 'Blau 1/2',
		'800080': 'Lila',
		'808080': '<PERSON>ris',
		F00: 'Ver<PERSON>',
		FF8C00: '<PERSON><PERSON><PERSON>',
		FFD700: 'Or',
		'008000': 'Verd',
		'0FF': '<PERSON><PERSON>',
		'00F': 'Blau',
		EE82EE: 'Violat',
		A9A9A9: 'Gris clar',
		FFA07A: 'Salmó clar',
		FFA500: 'Taronja',
		FFFF00: 'Groc',
		'00FF00': 'Verd Llima',
		AFEEEE: 'Turquesa Pàl·lid',
		ADD8E6: 'Blau Clar',
		DDA0DD: 'Pruna',
		D3D3D3: 'Gris Clar',
		FFF0F5: 'Lavanda rosat',
		FAEBD7: 'Blanc Antic',
		FFFFE0: 'Groc Clar',
		F0FFF0: 'Verd Pàl·lid',
		F0FFFF: 'Atzur',
		F0F8FF: 'Cian pàlid',
		E6E6FA: 'Lavanda',
		FFF: 'Blanc',
		'1ABC9C': 'Strong Cyan', // MISSING
		'2ECC71': 'Emerald', // MISSING
		'3498DB': 'Bright Blue', // MISSING
		'9B59B6': 'Amethyst', // MISSING
		'4E5F70': 'Grayish Blue', // MISSING
		'F1C40F': 'Vivid Yellow', // MISSING
		'16A085': 'Dark Cyan', // MISSING
		'27AE60': 'Dark Emerald', // MISSING
		'2980B9': 'Strong Blue', // MISSING
		'8E44AD': 'Dark Violet', // MISSING
		'2C3E50': 'Desaturated Blue', // MISSING
		'F39C12': 'Orange', // MISSING
		'E67E22': 'Carrot', // MISSING
		'E74C3C': 'Pale Red', // MISSING
		'ECF0F1': 'Bright Silver', // MISSING
		'95A5A6': 'Light Grayish Cyan', // MISSING
		'DDD': 'Light Gray', // MISSING
		'D35400': 'Pumpkin', // MISSING
		'C0392B': 'Strong Red', // MISSING
		'BDC3C7': 'Silver', // MISSING
		'7F8C8D': 'Grayish Cyan', // MISSING
		'999': 'Dark Gray' // MISSING
	},
	more: 'Més Colors...',
	panelTitle: 'Colors',
	textColorTitle: 'Color del Text'
} );
