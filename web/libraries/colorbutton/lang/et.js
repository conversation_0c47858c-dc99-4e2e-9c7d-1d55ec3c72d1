/*
Copyright (c) 2003-2020, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or https://ckeditor.com/legal/ckeditor-oss-license
*/
CKEDITOR.plugins.setLang( 'colorbutton', 'et', {
	auto: 'Automaatne',
	bgColorTitle: 'Tau<PERSON> värv',
	colors: {
		'000': 'Must',
		'800000': '<PERSON><PERSON><PERSON><PERSON><PERSON>',
		'8B4513': '<PERSON><PERSON><PERSON><PERSON><PERSON>',
		'2F4F4F': 'Tume paehall',
		'008080': 'Sinakasroheline',
		'000080': 'Meresinine',
		'4B0082': 'Indigosinine',
		'696969': 'Tumehall',
		B22222: 'Šamottkivi',
		A52A2A: 'Pruun',
		DAA520: 'Kuldkollane',
		'006400': 'Tumeroheline',
		'40E0D0': 'Türkiissinine',
		'0000CD': 'Keskmine sinine',
		'800080': '<PERSON>la',
		'808080': 'Hall',
		F00: '<PERSON><PERSON><PERSON>',
		FF8C00: '<PERSON><PERSON><PERSON><PERSON>',
		FFD700: 'Kuldne',
		'008000': 'Roheline',
		'0FF': 'Tsüaniidsinine',
		'00F': 'Sinine',
		EE82EE: 'Violetne',
		A9A9A9: 'Tumehall',
		FFA07A: 'Hele lõhe',
		FFA500: 'Oranž',
		FFFF00: 'Kollane',
		'00FF00': 'Lubja hall',
		AFEEEE: 'Kahvatu türkiis',
		ADD8E6: 'Helesinine',
		DDA0DD: 'Ploomililla',
		D3D3D3: 'Helehall',
		FFF0F5: 'Lavendlipunane',
		FAEBD7: 'Antiikvalge',
		FFFFE0: 'Helekollane',
		F0FFF0: 'Meloniroheline',
		F0FFFF: 'Taevasinine',
		F0F8FF: 'Beebisinine',
		E6E6FA: 'Lavendel',
		FFF: 'Valge',
		'1ABC9C': 'Tugev taevasinine',
		'2ECC71': 'Smaragdroheline',
		'3498DB': 'Kirgas sinine',
		'9B59B6': 'Ametüst',
		'4E5F70': 'Hallikassinine',
		'F1C40F': 'Erkkollane',
		'16A085': 'Tume taevasinine',
		'27AE60': 'Tumeroheline',
		'2980B9': 'Tugev sinine',
		'8E44AD': 'Tumevioletne',
		'2C3E50': 'Hallikassinine',
		'F39C12': 'Oraanž',
		'E67E22': 'Porgand',
		'E74C3C': 'Kahvatu punane',
		'ECF0F1': 'Kirgas hõbedane',
		'95A5A6': 'Hele hallikas taevasinine',
		'DDD': 'Helehall',
		'D35400': 'Kõrvitsavärv',
		'C0392B': 'Tugev punane',
		'BDC3C7': 'Hõbedane',
		'7F8C8D': 'Hallikas taevasinine',
		'999': 'Tume hall'
	},
	more: 'Rohkem värve...',
	panelTitle: 'Värvid',
	textColorTitle: 'Teksti värv'
} );
