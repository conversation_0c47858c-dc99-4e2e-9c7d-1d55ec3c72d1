{"excludeFiles": ["node_modules/**", "bower_components/**"], "requireCurlyBraces": ["if", "else", "for", "while", "do", "try", "catch"], "requireOperatorBeforeLineBreak": true, "requireCamelCaseOrUpperCaseIdentifiers": true, "maximumLineLength": {"value": 150, "allowComments": true, "allowRegex": true}, "validateIndentation": 4, "validateQuoteMarks": "'", "disallowMultipleLineStrings": null, "disallowMixedSpacesAndTabs": true, "disallowSpaceAfterPrefixUnaryOperators": true, "disallowMultipleVarDecl": null, "disallowTrailingWhitespace": true, "requireSpaceAfterKeywords": ["if", "else", "for", "while", "do", "switch", "return", "try", "catch"], "requireSpaceBeforeBinaryOperators": ["=", "+=", "-=", "*=", "/=", "%=", "<<=", ">>=", ">>>=", "&=", "|=", "^=", "+=", "+", "-", "*", "/", "%", "<<", ">>", ">>>", "&", "|", "^", "&&", "||", "===", "==", ">=", "<=", "<", ">", "!=", "!=="], "requireSpaceAfterBinaryOperators": true, "requireSpacesInConditionalExpression": true, "requireSpaceBeforeBlockStatements": true, "requireLineFeedAtFileEnd": true, "disallowSpacesInsideObjectBrackets": "all", "disallowSpacesInsideArrayBrackets": "all", "disallowSpacesInsideParentheses": true, "validateJSDoc": {"checkParamNames": true, "requireParamTypes": true}, "disallowMultipleLineBreaks": true, "disallowCommaBeforeLineBreak": null, "disallowDanglingUnderscores": null, "disallowEmptyBlocks": null, "disallowTrailingComma": null, "requireCommaBeforeLineBreak": null, "requireDotNotation": null, "requireMultipleVarDecl": null, "requireParenthesesAroundIIFE": true}