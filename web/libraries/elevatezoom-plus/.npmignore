# Ignore Visual Studio  Project #
###################
*.user
*.gpState
*.suo
bin
obj
/packages

# Ignore Node & Bower
###################
node_modules
bower_components
build
.tmp

# Ignore Test reporters
###################
**/test/coverage
report

# Ignore Web Storm #
.idea

# Compiled source #
###################
*.com
*.class
*.dll
*.exe
*.o
*.so

# Packages #
############
# it's better to unpack these files and commit the raw source
# git has its own built in compression methods
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.xap
*.zip

# Logs and databases #
######################
*.log
*.sql
*.sqlite
# *.sdf
*.mdf
*.ldf

# OS generated files #
######################
.DS_Store*
ehthumbs.db
Icon?
Thumbs.db

# Custom             #
######################
.build
dist
*.mo

# Not for packaging  #
######################
demo
*.bat
