API methods

- `onImageClick` *(function)* - callback for click's over the large image
- `onImageSwap` *(function)* - callback for 
- `onImageSwapComplete` *(function)* - callback for 
- `onZoomedImageLoaded` *(function)* - callback for 
- `onComplete` *(function)* - callback for 
- `mantainZoomAspectRatio` *(option)* - This change will allow to decide if you want to decrease zoom of one of the dimensions once the other reached it's top value, or keep the aspect ratio, default behaviour still being as always, allow to continue zooming out, so it keeps retrocompatibility.



