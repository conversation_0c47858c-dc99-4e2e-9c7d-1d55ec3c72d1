# Contributing

Before sending a pull request remember to follow [jQuery Core Style Guide](http://contribute.jquery.org/style-guide/js/).

1. Fork it!
2. Use an editor that has EditorConfig support
3. Create your feature branch: `git checkout -b my-new-feature`
4. Make your changes on the `src` folder, never on the `dist` folder.
5. ENSURE to follow the EditorConfig format style (found in this project), in order to have a consistent format style. (Webstorm supports/detects EditorConfig can auto-format JavScript files by pressing Ctrl-Alt-L )
6. Commit your changes: `git commit -m 'Add some feature'`
7. Push to the branch: `git push origin my-new-feature`
8. Submit a pull request :D
