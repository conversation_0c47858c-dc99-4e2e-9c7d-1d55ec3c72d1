[jQuery ElevateZoom Plus Plugin](http://igorlino.github.io/elevatezoom-plus/)
================================
Current Version: 1.2.5

[EZ Plus](http://igorlino.github.io/elevatezoom-plus/) is an up-to-date jQuery image zoom plug-in based on [EZ](https://github.com/elevateweb/elevatezoom)

## Features

- Fully Customisable
- Coloured Tints
- [Fancybox-Plus](http://igorlino.github.io/fancybox-plus/) and [Colorbox](http://www.jacklmoore.com/colorbox/) Gallery Support
- Variable zoom on mouse scroll
- External Controls
- Window Zoom, Lens Zoom and Inner Zoom
- Touch support
- Free to use under MIT
- JQuery plug-in
- AngularJS directive available: [angular-elevatezoom-plus](https://github.com/igorlino/angular-elevatezoom-plus)

## Supported Browsers

- Chrome
- IE7+
- Firefox
- Edge
- Safari

## Installation

Via [Bower](http://bower.io/):

```bash
bower install ez-plus
```

Via [npm](https://www.npmjs.com/):

```bash
npm install ez-plus
```

In a browser:

```html

<script src="https://cdn.jsdelivr.net/gh/igorlino/elevatezoom-plus@1.2.3/src/jquery.ez-plus.js"></script>
```

## Getting Started

Include jQuery and the plug-in on a page. Include your images and initialise the plug-in.

```html
<img id="zoom_01" src='images/large/image1.png' />

<script>
    $('#zoom_01').ezPlus();
</script>
```

For more information on how to setup and customise, [check the examples](http://igorlino.github.io/elevatezoom-plus/).

## Do you have a question?

The issue tracker is for **issues**, in other words, bugs and suggestions.
If you have a *question*, please use [Stack Overflow](http://stackoverflow.com/questions/tagged/elevatezoom), your favorite search engine, or other resources.
Due to increased similar type of questions, we can no longer answer questions in the issue tracker.

## License
Licensed under MIT license.
