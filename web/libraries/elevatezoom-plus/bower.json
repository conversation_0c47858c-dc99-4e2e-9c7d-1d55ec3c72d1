{"name": "ez-plus", "title": "EZ Plus", "version": "1.2.5", "description": "A jQuery image zoom plug-in, with tints, easing and gallery integration.", "author": {"name": "<PERSON>", "url": "http://igorlino.github.io/elevatezoom-plus/"}, "license": "MIT", "main": ["src/jquery.ez-plus.js", "css/jquery.ez-plus.css"], "ignore": ["**/.*", "node_modules", "components"], "bugs": "https://github.com/igorlino/elevatezoom-plus/issues", "homepage": "http://igorlino.github.io/elevatezoom-plus/", "docs": "http://igorlino.github.io/elevatezoom-plus/examples", "demo": "http://igorlino.github.io/elevatezoom-plus/examples", "dependencies": {"jquery": ">=1.11.2"}, "keywords": ["zoom", "effects", "elements", "zooming"]}