<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="https://www.w3.org/1999/xhtml" xml:lang="en" lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <meta http-equiv="imagetoolbar" content="no"/>
    <title>jQuery ElevateZoom-Plus - An image zoom plugin</title>
    <script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/1.7/jquery.min.js"></script>
    <script type="text/javascript"
            src="https://cdnjs.cloudflare.com/ajax/libs/jquery-easing/1.3/jquery.easing.min.js"></script>
    <script type="text/javascript"
            src="https://cdn.jsdelivr.net/gh/jquery/jquery-mousewheel@3.1.12/jquery.mousewheel.js"></script>

    <script type="text/javascript"
            src="https://cdn.jsdelivr.net/gh/igorlino/fancybox-plus@1.3.6/src/jquery.fancybox-plus.js"></script>
    <link rel="stylesheet" type="text/css"
          href="https://cdn.jsdelivr.net/gh/igorlino/fancybox-plus@1.3.6/css/jquery.fancybox-plus.css" media="screen"/>

    <!--
    <script type="text/javascript" src="https://cdn.jsdelivr.net/gh/igorlino/elevatezoom-plus@1.2.1/src/jquery.ez-plus.js"></script>
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/gh/igorlino/elevatezoom-plus@1.2.1/css/jquery.ez-plus.css" media="screen" />
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/gh/igorlino/elevatezoom-plus@1.2.1/demo/css/style.css" media="screen" />
    <script type="text/javascript" src="https://cdn.jsdelivr.net/gh/igorlino/elevatezoom-plus@1.2.1/demo/js/web.js?m=20100203"></script>
    -->

    <script type="text/javascript" src="../src/jquery.ez-plus.js"></script>
    <link rel="stylesheet" type="text/css" href="../css/jquery.ez-plus.css" media="screen"/>
    <link rel="stylesheet" type="text/css" href="css/style.css" media="screen"/>
    <script type="text/javascript" src="js/web.js?m=20100203"></script>

    <!--[if IE 6]>
    <script src="https://cdn.jsdelivr.net/gh/igorlino/DD_belatedPNG@0.0.8a/DD_belatedPNG_0.0.8a.js"></script>

    <script>
        DD_belatedPNG.fix('.png_bg');
    </script>
    <![endif]-->
    <script src="https://cdn.jsdelivr.net/gh/sorccu/cufon@1.09i/js/cufon.js" type="text/javascript"></script>
    <script src="https://cdn.jsdelivr.net/gh/igorlino/fancybox-plus@1.3.6/demo/js/Museo_300_300.font.js"
            type="text/javascript"></script>
    <script type="text/javascript">
        Cufon.replace('h1', {color: '#ff6347'});
    </script>

    <script type="text/javascript"
            src="https://cdn.jsdelivr.net/gh/igorlino/snippet-helper@1.0.1/src/snippet-helper.js"></script>
    <link type="text/css" rel="stylesheet"
          href="https://cdn.jsdelivr.net/gh/igorlino/elevatezoom-plus@1.2.1/demo/css/prism.css"/>
    <script type="text/javascript"
            src="https://cdn.jsdelivr.net/gh/igorlino/elevatezoom-plus@1.2.1/demo/js/prism.js"></script>

</head>
<body>
<div id="page">
    <div id="header">
        <h1><a href="https://github.com/igorlino/elevatezoom-plus">elevateZoom-<b>Plus</b></a></h1>
        <ul>
            <li><a href="index.html">Home</a></li>
            <li><a href="api.htm">API &amp; Options</a></li>
            <li><a class="active" href="examples.htm">Examples</a></li>
        </ul>
    </div>

    <div id="col_wrap">
        <div id="col_left" class="zoom-wrapper">
            <style>
                .scripts {
                    display: none;
                }
            </style>


            <!-- NAVIGATION -->
            <div class="examplenav">
                <ul style="margin: 0;list-style-type:none;">
                    <li><a href="#basic-zoom">Basic Zoom</a></li>
                    <li><a href="#tints">Tints</a></li>
                    <li><a href="#gallery-lightbox">Gallery & Lightbox</a></li>
                    <li><a href="#window-position">Window Position</a></li>
                    <li><a href="#lens-zoom">Lens Zoom</a></li>
                    <li><a href="#inner-zoom">Inner Zoom</a></li>
                    <li><a href="#fadein-fadeout">Fade In - Fade Out</a></li>
                    <li><a href="#external-controls">External Controls</a></li>
                    <li><a href="#easing">Easing</a></li>
                    <li><a href="#mousewheel">Scroll Zoom</a></li>
                    <li><a href="#zoom-level">Zoom Level</a></li>
                    <li><a href="#zoom-window-size">Zoom Window Size</a></li>
                    <li><a href="#zoom-constrain">Zoom with Image Constrain</a></li>
                    <li><a href="#zoom-responsive">Responsive</a></li>
                </ul>
                <br/><br/>
            </div>
            <!-- END NAVIGATION-->

            <!-- MAIN -->

            <div style="margin-left:10px;float:left; width:795px;">

                <h1>Jquery Image Zoom Plugin Examples</h1>

                <br>

                <div class="zoom-wrapper">
                    <div class="zoom-left">
                        <img class="zoom-img" id="zoom_01"
                             src="images/small/image1.jpg"
                             data-zoom-image="images/large/image1.jpg"
                             width="411"/>
                    </div>
                    <div class="zoom-right">
                        <h3><a name="basic-zoom">Basic Zoom</a></h3>

                        <p>The zoom works with either one or two images. Two images are recommended for the zoom to work
                            the
                            best.
                            Most of the settings for the zoom box can be overridden.
                        </p>
                        <a href="#" rel="view_script_01" class="view_source">SHOW THE CODE</a>
                    </div>
                    <div style="clear:both;"></div>
                    <script type="text/javascript">
                        $(document).ready(function () {
                            $("#zoom_01").ezPlus();
                        });
                    </script>
                    <div class="scripts view_script_01">
                        <h6>HTML</h6>
                        <pre><code id="code-ezp-e01-html"></code></pre>
                        <h6>JAVASCRIPT</h6>
                        <pre><code id="code-ezp-e01-js"></code></pre>
                    </div>
                </div>
                <hr class="style-two">

                <div class="zoom-wrapper">
                    <div class="zoom-left">
                        <img class="zoom-img" id="zoom_02"
                             src="images/small/image2.jpg"
                             data-zoom-image="images/large/image2.jpg"
                             width="411"/>
                    </div>
                    <div class="zoom-right">
                        <h3><a name="tints">Tints</a></h3>

                        <p>
                            You can easily set tints for the zoom, you can set the colour and opacity of the tint
                            overlay to
                            be any value

                        <div style="width:100px;float:left;">
                            Red <br>
                            <img class="zoom-img" id="zoom_02a"
                                 src="images/small/image2.jpg"
                                 data-zoom-image="images/large/image2.jpg"
                                 width="100"/>
                        </div>
                        <div style="margin-left:10px;width:100px;float:left;">
                            black<br>
                            <img class="zoom-img" id="zoom_02b"
                                 src="images/small/image2.jpg"
                                 data-zoom-image="images/large/image2.jpg"
                                 width="100"/>
                        </div>
                        <div style="margin-left:10px;width:100px;float:left;">
                            Green <br>
                            <img class="zoom-img" id="zoom_02c"
                                 src="images/small/image2.jpg"
                                 data-zoom-image="images/large/image2.jpg"
                                 width="100"/>
                        </div>
                        </p>
                        <a href="#" rel="view_script_02" class="view_source">SHOW THE CODE</a>

                    </div>
                    <div style="clear:both;"></div>
                    <script type="text/javascript">
                        $(document).ready(function () {
                            $("#zoom_02").ezPlus({tint: true, tintColour: '#F90', tintOpacity: 0.5});
                            $("#zoom_02a").ezPlus({
                                tint: true,
                                tintColour: 'red',
                                tintOpacity: 0.5,
                                zoomWindowPosition: 6
                            });
                            $("#zoom_02b").ezPlus({
                                tint: true,
                                tintColour: 'black',
                                tintOpacity: 0.5,
                                zoomWindowPosition: 6
                            });
                            $("#zoom_02c").ezPlus({
                                tint: true,
                                tintColour: 'green',
                                tintOpacity: 0.5,
                                zoomWindowPosition: 6
                            });
                        });
                    </script>
                    <div class="scripts view_script_02">
                        <h6>HTML</h6>
                        <pre><code id="code-ezp-e02-html"></code></pre>
                        <h6>JAVASCRIPT</h6>
                        <pre><code id="code-ezp-e02-js"></code></pre>
                    </div>
                </div>
                <hr class="style-two">
                <style>
                    #gallery_01 img {
                        border: 2px solid white;
                        width: 96px;
                    }

                    #gallery_09 img {
                        border: 2px solid white;
                        width: 96px;
                    }

                    .active img {
                        border: 2px solid #333 !important;
                    }

                </style>

                <div class="zoom-wrapper">
                    <div class="zoom-left">
                        <img class="zoom-img" id="zoom_03"
                             src="images/small/image3.jpg"
                             data-zoom-image="images/large/image3.jpg"
                             width="411"/>

                        <div id="gallery_01" style="width:500px;float:left;">

                            <a href="#" class="elevatezoom-gallery active" data-update=""
                               data-image="images/small/image1.jpg"
                               data-zoom-image="images/large/image1.jpg">
                                <img src="images/small/image1.jpg"
                                     width="100"/></a>

                            <a href="#" class="elevatezoom-gallery"
                               data-image="images/small/image2.jpg"
                               data-zoom-image="images/large/image2.jpg"
                                ><img
                                src="images/small/image2.jpg"
                                width="100"/></a>

                            <a href="tester" class="elevatezoom-gallery"
                               data-image="images/small/image3.jpg"
                               data-zoom-image="images/large/image3.jpg">
                                <img src="images/small/image3.jpg"
                                     width="100"/>
                            </a>

                            <a href="tester" class="elevatezoom-gallery"
                               data-image="images/small/image4.jpg"
                               data-zoom-image="images/large/image4.jpg"

                               class="slide-content"
                                ><img
                                src="images/small/image4.jpg"
                                width="100"/></a>

                        </div>
                    </div>
                    <div class="zoom-right">
                        <h3><a name="gallery-lightbox">Gallery & Lightbox</a></h3>

                        <p>
                            You can attach a set of images to the zoom.
                            Also you can pass a gallery to the lightbox
                            <br>
                            <strong>NEW: </strong>The imageCrossfade option will give a simultaneous fadein / fadeout
                            effect
                            on
                            the zoom.
                        </p>
                        <a href="#" rel="view_script_03" class="view_source">SHOW THE CODE</a>
                    </div>
                    <div style="clear:both;"></div>
                    <script type="text/javascript">
                        $(document).ready(function () {
                            $("#zoom_03").ezPlus({
                                gallery: 'gallery_01',
                                cursor: 'pointer',
                                galleryActiveClass: "active",
                                imageCrossfade: true,
                                loadingIcon: "https://www.elevateweb.co.uk/spinner.gif"
                            });

                            $("#zoom_03").bind("click", function (e) {
                                var ez = $('#zoom_03').data('ezPlus');
                                ez.closeAll(); //NEW: This function force hides the lens, tint and window
                                $.fancyboxPlus(ez.getGalleryList());
                                return false;
                            });

                        });

                    </script>

                    <div class="scripts view_script_03">
                        <h6>HTML</h6>
                        <pre><code id="code-ezp-e03-html"></code></pre>
                        <h6>JAVASCRIPT</h6>
                        <pre><code id="code-ezp-e03-js"></code></pre>
                        <h6>CSS</h6>
                        Note: Don't forget to put the class name you set on the default gallery image so it shows active
                        from
                        the start.
                        <pre><code id="code-ezp-e03-css"></code></pre>
                    </div>
                </div>
                <hr class="style-two">
                <!-- END MAIN -->

                <!-- FLOATING -->
                <div class="floatingdiv" id="demo-container">
                    <div style="opacity:1;color: black;">
                        This container is to show that the zoom can be positioned into any on screen element
                    </div>
                </div>
                <!-- END FLOATING -->


                <hr class="style-two">


                <div class="zoom-wrapper">
                    <div class="zoom-left">
                        <div style="width: 190px;float:left;">
                            <div
                                style="padding: 5px 10px;width: 170px;border-top-left-radius: 10px;background-color: #333;color: white;border-top-right-radius: 10px;">
                                Position 1: Default
                            </div>
                            <div
                                style="width: 170px;padding: 10px;border: 1px solid #E8E8E6;float: left;border-bottom-left-radius: 10px;border-bottom-right-radius: 10px;">
                                <img class="zoom-img" id="zoom_04a"
                                     src="images/small/image2.jpg"
                                     data-zoom-image="images/large/image2.jpg"
                                     width="100"/>
                            </div>
                        </div>
                        <div style="width: 190px;margin-left:10px;float:left;">
                            <div
                                style="padding: 5px 10px;width: 170px;border-top-left-radius: 10px;background-color: #333;color: white;border-top-right-radius: 10px;">
                                Position 12
                            </div>
                            <div
                                style="width: 170px;padding: 10px;border: 1px solid #E8E8E6;float: left;border-bottom-left-radius: 10px;border-bottom-right-radius: 10px;">

                                <img class="zoom-img" id="zoom_04b"
                                     src="images/small/image2.jpg"
                                     data-zoom-image="images/large/image2.jpg"
                                     width="100"/>
                            </div>
                        </div>
                        <div style="margin-top:40px;width: 190px;float:left;">
                            <div
                                style="padding: 5px 10px;width: 170px;border-top-left-radius: 10px;background-color: #333;color: white;border-top-right-radius: 10px;">
                                Position in a DIV
                            </div>
                            <div
                                style="width: 170px;padding: 10px;border: 1px solid #E8E8E6;float: left;border-bottom-left-radius: 10px;border-bottom-right-radius: 10px;">

                                <img class="zoom-img" id="zoom_04c"
                                     src="images/small/image2.jpg"
                                     data-zoom-image="images/large/image2.jpg"
                                     width="100"/>
                            </div>
                        </div>
                        <div style="margin-top:40px;width: 190px;margin-left:10px;float:left;">
                            <div
                                style="padding: 5px 10px;width: 170px;border-top-left-radius: 10px;background-color: #333;color: white;border-top-right-radius: 10px;">
                                Position 1 - 10 px X-offset
                            </div>
                            <div
                                style="width: 170px;padding: 10px;border: 1px solid #E8E8E6;float: left;border-bottom-left-radius: 10px;border-bottom-right-radius: 10px;">


                                <img class="zoom-img" id="zoom_04d"
                                     src="images/small/image2.jpg"
                                     data-zoom-image="images/large/image2.jpg"
                                     width="100"/>
                            </div>
                        </div>
                    </div>

                    <div class="zoom-right">
                        <h3><a name="window-position">Window Position</a></h3>

                        <p>
                            Positioning the window can be done in by setting a default position, and then using x and y
                            offset
                            to adjust
                            <img
                                src="https://cdn.jsdelivr.net/gh/igorlino/elevatezoom-plus@1.2.1/demo/images/window-positions.png"/>
                            <br/>You can also position the window into a container
                        </p>
                        <a href="#" rel="view_script_04" class="view_source">SHOW THE CODE</a>

                    </div>
                    <div style="clear:both;"></div>
                    <script type="text/javascript">
                        $(document).ready(function () {
                            $("#zoom_04a").ezPlus({zoomWindowPosition: 1});
                            $("#zoom_04b").ezPlus({zoomWindowPosition: 12});
                            $("#zoom_04c").ezPlus({
                                zoomWindowPosition: "demo-container",
                                zoomWindowHeight: 200,
                                zoomWindowWidth: 200,
                                borderSize: 0,
                                easing: true
                            });
                            $("#zoom_04d").ezPlus({zoomWindowPosition: 1, zoomWindowOffsetX: 10});

                        });
                    </script>
                    <div class="scripts view_script_04">
                        <h6>HTML</h6>
                        <pre><code id="code-ezp-e04-html"></code></pre>
                        <h6>JAVASCRIPT</h6>
                        <pre><code id="code-ezp-e04-js"></code></pre>
                    </div>
                </div>
                <hr class="style-two">


                <div class="zoom-wrapper">
                    <div class="zoom-left">
                        <img class="zoom-img" id="zoom_05"
                             src="images/small/image2.jpg"
                             data-zoom-image="images/large/image2.jpg"
                             width="411"/>
                    </div>
                    <div class="zoom-right">
                        <h3><a name="inner-zoom">Inner Zoom</a></h3>

                        <p>
                            The zoom can be placed inside of the image
                        </p>
                        <a href="#" rel="view_script_05" class="view_source">SHOW THE CODE</a>

                    </div>
                    <div style="clear:both;"></div>
                    <script type="text/javascript">
                        $(document).ready(function () {
                            $("#zoom_05").ezPlus({
                                zoomType: "inner",
                                debug: true,
                                cursor: "crosshair",
                                zoomWindowFadeIn: 500,
                                zoomWindowFadeOut: 500
                            });
                        });
                        $("#zoom_05").bind("click", function (e) {
                            var ez = $('#zoom_05').data('ezPlus');
                            $.fancyboxPlus(ez.getGalleryList());
                            return false;
                        });

                    </script>
                    <div class="scripts view_script_05">
                        <h6>HTML</h6>
                        <pre><code id="code-ezp-e05-html"></code></pre>
                        <h6>JAVASCRIPT</h6>
                        <pre><code id="code-ezp-e05-js"></code></pre>
                    </div>
                </div>
                <hr class="style-two">


                <div class="zoom-wrapper">
                    <div class="zoom-left">
                        <img id="zoom_07"
                             src="images/small/image5.jpg"
                             data-zoom-image="images/large/image5.jpg"
                             width="411"/>
                    </div>
                    <div class="zoom-right">
                        <h3><a name="lens-zoom">Lens Zoom</a></h3>

                        <p>
                            You can use the lens zoom setting to "Magnify the image".<br>
                            The image to the lest has been constrained so it tucks underneath the image.
                        </p>
                        <a href="#" rel="view_script_07" class="view_source">SHOW THE CODE</a>

                    </div>
                    <div style="clear:both;"></div>
                    <script type="text/javascript">
                        $(document).ready(function () {
                            $("#zoom_07").ezPlus({
                                zoomType: "lens",
                                lensShape: "round",
                                containLensZoom: true,
                                lensSize: 200
                            });
                        });
                    </script>
                    <div class="scripts view_script_07">
                        <h6>HTML</h6>
                        <pre><code id="code-ezp-e07-html"></code></pre>
                        <h6>JAVASCRIPT</h6>
                        <pre><code id="code-ezp-e07-js"></code></pre>
                    </div>
                </div>
                <hr class="style-two">


                <div class="zoom-wrapper">
                    <div class="zoom-left">
                        <img class="zoom-img" id="zoom_08"
                             src="images/small/image2.jpg"
                             data-zoom-image="images/large/image2.jpg"
                             width="411"/>
                    </div>
                    <div class="zoom-right">
                        <h3><a name="fadein-fadeout">Fade in / Fade Out Settings</a></h3>

                        <p>
                            You can fade in and out on the Lens, Window and Tint
                        </p>
                        <a href="#" rel="view_script_08" class="view_source">SHOW THE CODE</a>

                    </div>
                    <div style="clear:both;"></div>
                    <script type="text/javascript">
                        $(document).ready(function () {
                            $("#zoom_08").ezPlus({
                                zoomWindowFadeIn: 500,
                                zoomWindowFadeOut: 500,
                                lensFadeIn: 500,
                                lensFadeOut: 500
                            });
                        });
                    </script>
                    <div class="scripts view_script_08">
                        <h6>HTML</h6>
                        <pre><code id="code-ezp-e08-html"></code></pre>
                        <h6>JAVASCRIPT</h6>
                        <pre><code id="code-ezp-e08-js"></code></pre>
                    </div>
                </div>
                <hr class="style-two">


                <div class="zoom-wrapper">
                    <div class="zoom-left">
                        <img class="zoom-img" id="zoom_09"
                             src="images/small/image1.jpg"
                             data-zoom-image="images/large/image1.jpg"
                             width="411"/>

                        <div id="gallery_09" style="width:500px;float:left;">

                            <a href="#" class="elevatezoom-gallery active" data-update=""
                               data-image="images/small/image1.jpg"
                               data-zoom-image="images/large/image1.jpg">
                                <img src="images/small/image1.jpg"
                                     width="100"/></a>

                            <a href="#" class="elevatezoom-gallery"
                               data-image="images/small/image2.jpg"
                               data-zoom-image="images/large/image2.jpg"
                                ><img src="images/small/image2.jpg"
                                      width="100"/></a>

                            <a href="tester" class="elevatezoom-gallery"
                               data-image="images/small/image3.jpg"
                               data-zoom-image="images/large/image3.jpg">
                                <img src="images/small/image3.jpg"
                                     width="100"/>
                            </a>

                            <a href="tester" class="elevatezoom-gallery"
                               data-image="images/small/image4.jpg"
                               data-zoom-image="images/large/image4.jpg"

                               class="slide-content"
                                ><img src="images/small/image4.jpg"
                                      width="100"/></a>

                        </div>
                    </div>
                    <div class="zoom-right">
                        <h3><a name="external-controls">External Controls</a></h3>

                        <p>
                            You can fade in and out on the Lens, Window and Tint
                            Change the image on dropdown
                            <select id="select">
                                <option value="1">Front</option>
                                <option value="2">Back</option>
                                <option value="3">Scenery</option>
                                <option value="4">Side</option>
                            </select>
                        </p>
                        <a href="#" rel="view_script_09" class="view_source">SHOW THE CODE</a>

                    </div>
                    <div style="clear:both;"></div>
                    <script type="text/javascript">
                        $(document).ready(function () {
                            $("#zoom_09").ezPlus({
                                gallery: "gallery_09",
                                galleryActiveClass: "active"
                            });


                            $("#select").change(function (e) {
                                var currentValue = $("#select").val();
                                if (currentValue == 1) {
                                    smallImage = 'images/small/image1.jpg';
                                    largeImage = 'images/large/image1.jpg';
                                }
                                if (currentValue == 2) {
                                    smallImage = 'images/small/image2.jpg';
                                    largeImage = 'images/large/image2.jpg';
                                }
                                if (currentValue == 3) {
                                    smallImage = 'images/small/image3.jpg';
                                    largeImage = 'images/large/image3.jpg';
                                }
                                if (currentValue == 4) {
                                    smallImage = 'images/small/image4.jpg';
                                    largeImage = 'images/large/image4.jpg';
                                }
                                // Example of implementing Active Class
                                $('#gallery_09 a').removeClass('active').eq(currentValue - 1).addClass('active');


                                var ez = $('#zoom_09').data('ezPlus');

                                ez.swaptheimage(smallImage, largeImage);

                            });
                        });

                    </script>
                    <div class="scripts view_script_09">
                        <h6>HTML</h6>
                        <pre><code id="code-ezp-e09-html"></code></pre>
                        <h6>JAVASCRIPT</h6>
                        <pre><code id="code-ezp-e09-js"></code></pre>
                    </div>
                </div>
                <hr class="style-two">


                <div class="zoom-wrapper">
                    <div class="zoom-left">
                        <img class="zoom-img" id="zoom_10"
                             src="images/small/image1.jpg"
                             data-zoom-image="images/large/image1.jpg"
                             width="411"/>

                    </div>
                    <div class="zoom-right">
                        <h3><a name="easing">Easing</a></h3>

                        <p>
                            You can use the default easing or a custom easing setting.
                            The amount of easing can also be altered - default is 16, set higher for more, lower for
                            less

                        </p>
                        <a href="#" rel="view_script_10" class="view_source">SHOW THE CODE</a>

                    </div>
                    <div style="clear:both;"></div>
                    <script type="text/javascript">
                        $(document).ready(function () {
                            $("#zoom_10").ezPlus({
                                easing: true
                                // true is the same as 16; try 5 or 20
                            });

                        });


                    </script>
                    <div class="scripts view_script_10">
                        <h6>HTML</h6>
                        <pre><code id="code-ezp-e10-html"></code></pre>
                        <h6>JAVASCRIPT</h6>
                        <pre><code id="code-ezp-e10-js"></code></pre>
                    </div>
                </div>
                <hr class="style-two">


                <div class="zoom-wrapper">
                    <div class="zoom-left">
                        <img class="zoom-img" id="zoom_mw"
                             src="images/small/image1.jpg"
                             data-zoom-image="images/large/image1.jpg"
                             width="411"/>

                    </div>
                    <div class="zoom-right">
                        <h3><a name="mousewheel">Mousewheel Zoom</a></h3>

                        <p>
                            You can scroll over the image to zoom in closer!

                        </p>
                        <a href="#" rel="view_script_mw" class="view_source">SHOW THE CODE</a>

                    </div>
                    <div style="clear:both;"></div>
                    <script type="text/javascript">
                        $(document).ready(function () {
                            $("#zoom_mw").ezPlus({
                                scrollZoom: true
                            });

                        });


                    </script>
                    <div class="scripts view_script_mw">
                        <h6>HTML</h6>
                        <pre><code id="code-ezp-emw-html"></code></pre>
                        <h6>JAVASCRIPT</h6>
                        <pre><code id="code-ezp-emw-js"></code></pre>
                    </div>
                </div>
                <hr class="style-two">


                <div style="display:none;" class="zoom-wrapper">
                    <div class="zoom-left">
                        <img class="zoom-img" id="zoom_11"
                             src="images/small/image1.jpg"
                             data-zoom-image="images/large/image1.jpg"
                             width="411"/>

                    </div>
                    <div class="zoom-right">
                        <h3><a name="zoom-level">Zoom Level</a></h3>

                        <p>
                            The zoomlevel by default is 1, but this can be overridden <br>
                            You can do this with the zoomLevel config option <br>
                            Zoom Level 2 would make the image twice as small<br>
                            Zoom Level 0.5 would make the image twice as big

                        </p>
                        <a href="#" rel="view_script_11" class="view_source">SHOW THE CODE</a>

                    </div>
                    <div style="clear:both;"></div>
                    <script type="text/javascript">
                        $(document).ready(function () {
                            $("#zoom_11").ezPlus({
                                zoomLevel: 0.5
                            });

                        });


                    </script>
                    <div class="scripts view_script_11">
                        <h6>HTML</h6>
                        <pre><code id="code-ezp-e11-html"></code></pre>
                        <h6>JAVASCRIPT</h6>
                        <pre><code id="code-ezp-e11-js"></code></pre>
                    </div>
                </div>
                <hr class="style-two">


                <div class="zoom-wrapper">
                    <div class="zoom-left">
                        <img class="zoom-img" id="zoom_13"
                             src="images/small/image1.jpg"
                             data-zoom-image="images/large/image1.jpg"
                             width="411"/>
                        <img class="zoom-img" id="zoom_14"
                             src="images/small/image1.jpg"
                             data-zoom-image="images/large/image1.jpg"
                             width="411"/>
                    </div>
                    <div class="zoom-right">
                        <h3><a name="zoom-window-size">Change the Zoom Window Size</a></h3>

                        <p>
                            The Zoom Window Size can be adjusted to any proportions.

                        </p>
                        <a href="#" rel="view_script_12" class="view_source">SHOW THE CODE</a>

                    </div>
                    <div style="clear:both;"></div>
                    <script type="text/javascript">
                        $(document).ready(function () {
                            $("#zoom_13").ezPlus({
                                zoomWindowWidth: 300,
                                zoomWindowHeight: 100
                            });

                            $("#zoom_14").ezPlus({
                                zoomWindowWidth: 100,
                                zoomWindowHeight: 300
                            });
                        });


                    </script>
                    <div class="scripts view_script_12">
                        <h6>HTML</h6>
                        <pre><code id="code-ezp-e12-html"></code></pre>
                        <h6>JAVASCRIPT</h6>
                        <pre><code id="code-ezp-e12-js"></code></pre>
                    </div>
                </div>
                <hr class="style-two">


                <div class="zoom-wrapper">
                    <div class="zoom-left">
                        <img class="zoom-img" id="zoom_03f"
                             src="images/small/image3.jpg"
                             data-zoom-image="images/large/image3.jpg"
                             width="411"/>

                        <div id="gallery_01f" style="width:500px;float:left;">

                            <a href="#" class="elevatezoom-gallery active" data-update=""
                               data-image="images/small/image1.jpg"
                               data-zoom-image="images/large/image1.jpg">
                                <img src="images/small/image1.jpg"
                                     width="100"/></a>

                            <a href="#" class="elevatezoom-gallery"
                               data-image="images/small/image2.jpg"
                               data-zoom-image="images/large/image2.jpg"
                                ><img src="images/small/image2.jpg"
                                      width="100"/></a>

                            <a href="tester" class="elevatezoom-gallery"
                               data-image="images/small/image3.jpg"
                               data-zoom-image="images/large/image3.jpg">
                                <img src="images/small/image3.jpg"
                                     width="100"/>
                            </a>

                            <a href="tester" class="elevatezoom-gallery"
                               data-image="images/small/image5.jpg"
                               data-zoom-image="images/large/image5.jpg"

                               class="slide-content"
                                ><img src="images/small/image5.jpg" height="68"/></a>

                        </div>
                    </div>
                    <div class="zoom-right">
                        <h3><a name="zoom-constrain">Image Constrain</a></h3>

                        <p>
                            You can attach a set of images to the zoom.
                            Also you can pass a gallery to the lightbox
                        </p>
                        <a href="#" rel="view_script_03f" class="view_source">SHOW THE CODE</a>
                    </div>
                    <div style="clear:both;"></div>
                    <script type="text/javascript">
                        $(document).ready(function () {
                            $("#zoom_03f").ezPlus({
                                constrainType: "height",
                                constrainSize: 274,
                                zoomType: "lens",
                                containLensZoom: true,
                                gallery: 'gallery_01f',
                                cursor: 'pointer',
                                galleryActiveClass: "active"
                            });

                            $("#zoom_03f").bind("click", function (e) {
                                var ez = $('#zoom_03f').data('ezPlus');
                                ez.closeAll(); //NEW: This function force hides the lens, tint and window
                                $.fancyboxPlus(ez.getGalleryList());

                                return false;
                            });

                        });

                    </script>

                    <div class="scripts view_script_03f">
                        <h6>HTML</h6>
                        <pre><code id="code-ezp-e03f-html"></code></pre>
                        <h6>JAVASCRIPT</h6>
                        <pre><code id="code-ezp-e03f-js"></code></pre>
                        <h6>CSS</h6>
                        Note: Don't forget to put the class name you set on the default gallery image so it shows active
                        from the
                        start.
                        <pre><code id="code-ezp-e03f-css"></code></pre>
                    </div>
                </div>
                <hr class="style-two">

                <div class="zoom-wrapper">

                    <link rel="stylesheet" type="text/css" href="snippets/code-ezp-e13.css" media="screen"/>
                    <div class="zoom-left">
                        <h3 ><a name="zoom-window-size">Responsive - Screen-size sensitivity</a></h3>

                        <p>
                            The example below defines 3 responsive CSS media queries and also in the 'respond' with 4 ranges.
                        <ol style="float:left;text-align: left">
                            <li>Default - tint color is #0F0(green) <span class="responsive_default">ACTIVE</span></li>
                            <li>[800px,1199px] - tint color is #00F (purple) <span class="responsive_800">ACTIVE</span></li>
                            <li>[600px,799px] - tint color is #F00(red) <span class="responsive_600">ACTIVE</span></li>
                            <li>[100px,599px] - tint color is disabled <span class="responsive_100">ACTIVE</span></li>
                        </ol>


                        </p>

                        <p>
                            <span id="status"></span>
                            <span id="imgWidth"></span>
                        </p>

                        <a href="#" rel="view_script_13" class="view_source">SHOW THE CODE</a>

                    </div>
                    <div class="zoom-left">
                        <img id="responsive_img"
                             width="411"
                             src="images/small/image1.jpg" data-zoom-image="images/large/image1.jpg"/>
                    </div>
                    <div style="clear:both;"></div>
                    <script type="text/javascript">
                        $(function () {

                            initEZPlus();

                            //Triggered when window width is changed.
                            $( window ).on( "resize", function() {
                                var windowWidth = $( window ).width(), // get window width
                                    imgWidth = $( "#responsive_img").width(); // get image width
                                //Init elevateZoom
                                initEZPlus();
                                //display status
                                $( "#status" ).html("Status: Window resized!.");
                                //display image and window width
                                $( "#imgWidth" ).html("Image width: " + imgWidth + "px" + "<br />" + "Window width: " + windowWidth + "px");
                            });

                            function initEZPlus() {
                                $("#responsive_img").ezPlus({
                                    responsive : true,
                                    scrollZoom : false,
                                    showLens: true,

                                    tint: true,
                                    tintColour: '#0F0',
                                    tintOpacity: 0.5,
                                    respond: [
                                        {
                                            range: '600-799',
                                            tintColour: '#F00',
                                            zoomWindowHeight: 100,
                                            zoomWindowWidth: 100
                                        },
                                        {
                                            range: '800-1199',
                                            tintColour: '#00F',
                                            zoomWindowHeight: 200,
                                            zoomWindowWidth: 200
                                        },
                                        {
                                            range: '100-599',
                                            enabled: false,
                                            showLens: false
                                        }
                                    ]
                                });
                            }
                        });
                    </script>
                    <div class="scripts view_script_13">
                        <h6>HTML</h6>
                        <pre><code id="code-ezp-e13-html"></code></pre>
                        <h6>JAVASCRIPT</h6>
                        <pre><code id="code-ezp-e13-js"></code></pre>
                        <h6>CSS</h6>
                        <pre><code id="code-ezp-e13-css"></code></pre>
                    </div>
                </div>
                <hr class="style-two">


                <div class="clear"></div>
            </div>
            <script type="text/javascript">
                var snippets = [
                    {code: "code-ezp-e01", ext: "html,js"},
                    {code: "code-ezp-e02", ext: "html,js"},
                    {code: "code-ezp-e03", ext: "html,js,css"},
                    {code: "code-ezp-e04", ext: "html,js"},
                    {code: "code-ezp-e05", ext: "html,js"},
                    {code: "code-ezp-e07", ext: "html,js"},
                    {code: "code-ezp-e08", ext: "html,js"},
                    {code: "code-ezp-e09", ext: "html,js"},
                    {code: "code-ezp-e03f", ext: "html,js,css"},
                    {code: "code-ezp-emw", ext: "html,js"},
                    {code: "code-ezp-e10", ext: "html,js"},
                    {code: "code-ezp-e11", ext: "html,js"},
                    {code: "code-ezp-e12", ext: "html,js"},
                    {code: "code-ezp-e13", ext: "html,js,css"}
                ];
                $(document).ready(function () {
                    snippetHelper.loadSnippets(snippets);
                });
            </script>


            <div id="footer">
                <p>Contact: info <span>[at]</span> <a href="https://igorlino.github.io/elevatezoom-plus/">issues</a>
                    &nbsp;&nbsp; /please, don`t send emails for help, use <a
                        href="https://github.com/igorlino/elevatezoom-plus/issues">issues</a> instead</p>
            </div>
        </div>
    </div>
</div>
</body>
</html>
