<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "https://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="https://www.w3.org/1999/xhtml" xml:lang="en" lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <meta http-equiv="imagetoolbar" content="no"/>
    <title>jQuery ElevateZoom-Plus - An image zoom plugin</title>
    <script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/1.7/jquery.min.js"></script>
    <script type="text/javascript"
            src="https://cdnjs.cloudflare.com/ajax/libs/jquery-easing/1.3/jquery.easing.min.js"></script>
    <script type="text/javascript"
            src="https://cdn.jsdelivr.net/gh/jquery/jquery-mousewheel@3.1.12/jquery.mousewheel.js"></script>

    <script type="text/javascript"
            src="https://cdn.jsdelivr.net/gh/igorlino/fancybox-plus@1.3.6/src/jquery.fancybox-plus.js"></script>
    <link rel="stylesheet" type="text/css"
          href="https://cdn.jsdelivr.net/gh/igorlino/fancybox-plus@1.3.6/css/jquery.fancybox-plus.css" media="screen"/>

    <!--
    <script type="text/javascript" src="https://cdn.jsdelivr.net/gh/igorlino/elevatezoom-plus@1.2.1/src/jquery.ez-plus.js"></script>
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/gh/igorlino/elevatezoom-plus@1.2.1/css/jquery.ez-plus.css" media="screen" />
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/gh/igorlino/elevatezoom-plus@1.2.1/demo/css/style.css" media="screen" />
    <script type="text/javascript" src="https://cdn.jsdelivr.net/gh/igorlino/elevatezoom-plus@1.2.1/demo/js/web.js?m=20100203"></script>
    -->

    <script type="text/javascript" src="../src/jquery.ez-plus.js"></script>
    <link rel="stylesheet" type="text/css" href="../css/jquery.ez-plus.css" media="screen"/>
    <link rel="stylesheet" type="text/css" href="css/style.css" media="screen"/>
    <script type="text/javascript" src="js/web.js?m=20100203"></script>

    <!--[if IE 6]>
    <script src="https://cdn.jsdelivr.net/gh/igorlino/DD_belatedPNG@0.0.8a/DD_belatedPNG_0.0.8a.js"></script>

    <script>
        DD_belatedPNG.fix('.png_bg');
    </script>
    <![endif]-->
    <script src="https://cdn.jsdelivr.net/gh/sorccu/cufon@1.09i/js/cufon.js" type="text/javascript"></script>
    <script src="https://cdn.jsdelivr.net/gh/igorlino/fancybox-plus@1.3.6/demo/js/Museo_300_300.font.js"
            type="text/javascript"></script>
    <script type="text/javascript">
        Cufon.replace('h1', {color: '#ff6347'});
    </script>

    <script type="text/javascript" src="https://cdn.jsdelivr.net/gh/igorlino/snippet-helper@1.0.1/src/snippet-helper.js"></script>
    <link type="text/css" rel="stylesheet" href="https://cdn.jsdelivr.net/gh/igorlino/elevatezoom-plus@1.2.1/demo/css/prism.css"/>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/gh/igorlino/elevatezoom-plus@1.2.1/demo/js/prism.js"></script>
</head>
<body>
<div id="page" class="home">
    <div id="header">
        <h1><a href="https://github.com/igorlino/elevatezoom-plus">elevateZoom-<b>Plus</b></a></h1>
        <ul>
            <li><a class="active" href="index.html">Home</a></li>
            <li><a href="api.htm">API &amp; Options</a></li>
            <li><a href="examples.htm">Examples</a></li>
        </ul>
    </div>

    <div id="col_wrap">
        <div id="col_left" class="zoom-wrapper">


            <h1>What is it?</h1>

            <p>
                <a href="https://github.com/igorlino/elevatezoom-plus"><strong>ElevateZoom-Plus</strong></a>  is a jquery plugin for zooming images within a container or also in a
                "lens"
                that floats overtop of web page.
                <br/>
                It was built using the <a href="https://jquery.com/">jQuery library</a>.
                Licensed under <a href="https://docs.jquery.com/Licensing">MIT license</a>
            </p>


            <h1>Features</h1>

            <ul class="list">
                <li>Fully Customisable</li>
                <li>Coloured Tints</li>
                <li><a href="https://igorlino.github.io/fancybox-plus/">Fancybox-Plus</a> and <a
                    href="https://www.jacklmoore.com/colorbox/">Colorbox</a> Gallery Support
                </li>
                <li>Variable zoom on mouse scroll</li>
                <li>External Controls</li>
                <li>Window Zoom, Lens Zoom and Inner Zoom</li>
                <li>AngularJS directive available: <a href="https://github.com/igorlino/angular-elevatezoom-plus">angular-elevatezoom-plus</a>
                </li>
                <li>Free to use under MIT license</li>

            </ul>

            <div class="zoom-left">
                <img style="width:411px" id="img_01"
                     src="https://cdn.jsdelivr.net/gh/igorlino/elevatezoom-plus@1.1.6/demo/images/small/image3.jpg"
                     data-zoom-image="https://cdn.jsdelivr.net/gh/igorlino/elevatezoom-plus@1.1.6/demo/images/large/image3.jpg"
                    />

                <div id="gal1" style="width:500px;float:left;">

                    <a href="#" class="elevatezoom-gallery" data-update=""
                       data-image="https://cdn.jsdelivr.net/gh/igorlino/elevatezoom-plus@1.1.6/demo/images/small/image1.jpg"
                       data-zoom-image="https://cdn.jsdelivr.net/gh/igorlino/elevatezoom-plus@1.1.6/demo/images/large/image1.jpg">
                        <img id="img_01"
                             src="https://cdn.jsdelivr.net/gh/igorlino/elevatezoom-plus@1.1.6/demo/images/small/image1.jpg"
                             width="100"/></a>

                    <a href="#" class="elevatezoom-gallery"
                       data-image="https://cdn.jsdelivr.net/gh/igorlino/elevatezoom-plus@1.1.6/demo/images/small/image2.jpg"
                       data-zoom-image="https://cdn.jsdelivr.net/gh/igorlino/elevatezoom-plus@1.1.6/demo/images/large/image2.jpg"
                        ><img id=""
                              src="https://cdn.jsdelivr.net/gh/igorlino/elevatezoom-plus@1.1.6/demo/images/small/image2.jpg"
                              width="100"/></a>

                    <a href="tester" class="elevatezoom-gallery"
                       data-image="https://cdn.jsdelivr.net/gh/igorlino/elevatezoom-plus@1.1.6/demo/images/small/image3.jpg"
                       data-zoom-image="https://cdn.jsdelivr.net/gh/igorlino/elevatezoom-plus@1.1.6/demo/images/large/image3.jpg">
                        <img id=""
                             src="https://cdn.jsdelivr.net/gh/igorlino/elevatezoom-plus@1.1.6/demo/images/small/image3.jpg"
                             width="100"/>
                    </a>

                    <a href="tester" class="elevatezoom-gallery"
                       data-image="https://cdn.jsdelivr.net/gh/igorlino/elevatezoom-plus@1.1.6/demo/images/small/image4.jpg"
                       data-zoom-image="https://cdn.jsdelivr.net/gh/igorlino/elevatezoom-plus@1.1.6/demo/images/large/image4.jpg"

                       class="slide-content"
                        ><img id=""
                              src="https://cdn.jsdelivr.net/gh/igorlino/elevatezoom-plus@1.1.6/demo/images/small/image4.jpg"
                              width="100"/></a>

                </div>
            </div>
            <div style="clear:both;"></div>
            <script type="text/javascript">
                $(document).ready(function () {
                    $("#zoom_06").ezPlus({
                        zoomType: "inner",
                        debug: true,
                        cursor: "crosshair"
                    });
                });
            </script>

            <div class="scripts view_script_05">
                <h6>HTML</h6>
                <pre><code id="code-ezp-01-html" ></code></pre>
                <h6>JAVASCRIPT</h6>
                <pre><code id="code-ezp-01-js" ></code></pre>
            </div>

            <hr class="style-two">

            <div style="clear:both;"></div>

            <div>
                <h1 style="font-size: 19px;margin-top:30px;margin-bottom:10px;">Installation and usage</h1>

                <p>No need to download anything, you only need to include the jquery and elevatezoom plugin files into
                    your project as show below:</p>

<pre><code id="code-ezp-02-html"></code></pre>


                <h1 style="font-size: 19px;margin-top:30px;margin-bottom:10px;">Include your Image</h1>

                <p>ElevateZoom-Plus works best using two images, one low resolution for the visible image, and one
                    high
                    resolution for the zoomed image.
                    If you only have one image available, Elevate Zoom will still work if you scale down the
                    image,
                    although this is not recommended as your page load time will increase if
                    you are loading larger images</p>

                <p>Please ensure your small image is proportionally scaled down from the large image.

                <p>

                <pre><code id="code-ezp-03-html"></code></pre>

                <h1 style="font-size: 19px;margin-top:30px;margin-bottom:10px;">Activate the zoom - Basic
                    Example</h1>

                <pre><code id="code-ezp-03-js"></code></pre>

                <h1 style="font-size: 19px;margin-top:30px;margin-bottom:10px;">More Examples</h1>

                See More examples of the image zoom <a
                href="examples.htm">here</a>


            </div>

            <!--<div id="col_right">
                <div id="col_sep">

                    <p><b>Current Release</b><br/><a href="">Version</b> 1.3.5 (20.06.2015)</a></p>

                    <p><a href="https://github.com/igorlino/fancybox-plus/blob/master/CHANGELOG.md">Changelog</a></p>

                </div>

            </div>-->

            <div class="clear"></div>
        </div>
        <script type="text/javascript">
            var snippets = [
                {code:"code-ezp-01", ext:"html,js"},
                {code:"code-ezp-02", ext:"html"},
                {code:"code-ezp-03", ext:"html,js"}
            ];
            $(document).ready(function () {
                snippetHelper.loadSnippets(snippets);
            });
        </script>


        <div id="footer">
            <p>Contact: info <span>[at]</span> <a href="https://igorlino.github.io/elevatezoom-plus/">issues</a>
                &nbsp;&nbsp; /please, don`t send emails for help, use <a
                    href="https://github.com/igorlino/elevatezoom-plus/issues">issues</a> instead</p>
        </div>
    </div>
</div>
</body>
</html>
