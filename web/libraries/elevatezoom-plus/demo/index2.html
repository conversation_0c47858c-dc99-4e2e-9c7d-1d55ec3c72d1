<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="<PERSON> Lino">
    <title>jQuery EZ Plus Demo</title>

    <!-- Bootstrap core CSS -->
    <link href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.4/css/bootstrap.min.css" rel="stylesheet">

    <!-- Custom styles for this template -->
    <style>
        body {
            padding-top: 50px;
        }

        .starter-template {
            padding: 40px 15px;
            text-align: center;
        }

        /*Default*/
        #responsive_img {
            width:800px ;
        }

        .responsive_default {
            display:inline-block;
            color: #159957;
        }
        .responsive_100, .responsive_600, .responsive_800 {
            display:none;
            color: #159957;
        }

        /*Target devices 100-599px*/
        @media only screen and  (min-width: 100px) and (max-width: 599px) {

            #responsive_img {
                width:300px ;
            }

            .responsive_100 {
                display:inline-block;
            }
            .responsive_default {
                display:none;
            }

        }
        /*Target devices 600-799 width*/
        @media only screen and (min-width: 600px) and (max-width: 799px) {

            #responsive_img {
                width:450px;
            }

            .responsive_600 {
                display:inline-block;
            }
            .responsive_default {
                display:none;
            }
        }

        /*Target devices 800-1199px*/
        @media only screen and  (min-width: 800px) and (max-width: 1199px) {

            #responsive_img {
                width:600px ;
            }

            .responsive_800 {
                display:inline-block;
            }
            .responsive_default {
                display:none;
            }

        }

    </style>

    <!-- HTML5 shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
    <script src="https://oss.maxcdn.com/html5shiv/3.7.2/html5shiv.min.js"></script>
    <script src="https://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->

    <link rel="stylesheet" href="../css/jquery.ez-plus.css"/>
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.3/jquery.min.js"></script>
    <script src='../src/jquery.ez-plus.js'></script>
</head>

<body>

<div class="navbar navbar-inverse navbar-fixed-top" role="navigation">
    <div class="container">
        <div class="navbar-header">
            <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target=".navbar-collapse">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
            </button>
            <a class="navbar-brand" href="#">EZ Plus</a>
        </div>
        <div class="collapse navbar-collapse">
            <ul class="nav navbar-nav">
                <li class="active"><a href="#">Home</a></li>
                <li><a href="#about">About</a></li>
                <li><a href="#contact">Contact</a></li>
            </ul>
        </div>
        <!--/.nav-collapse -->
    </div>
</div>

<div class="container">
    <div class="starter-template">
    </div>
    <div class="starter-template">
        <h1>Basic EZ Plus Example</h1>

        <p class="lead">Use these examples as a way to quickly start any new web project that requires image zooming.</p>


        <h1>Basic Zoom Example</h1>

        <img id="zoom_01" src="images/large/image1.jpg" width="400"/>
        <script>$("#zoom_01").ezPlus();</script>

        <hr/>

        <h1>Tints</h1>
        <img id="zoom_02" src="images/small/image1.jpg" data-zoom-image="images/large/image1.jpg"/>
        <script>$("#zoom_02").ezPlus({tint: true, tintColour: '#F90', tintOpacity: 0.5});</script>

        <hr/>

        <h1>Gallery & Lightbox</h1>


        <img id="zoom_03" src="images/small/image1.jpg" data-zoom-image="images/large/image1.jpg"/>

        <div id="gallery_01">
            <a href="#" data-image="images/small/image1.jpg" data-zoom-image="images/large/image1.jpg">
                <img id="img_01" src="images/thumb/image1.jpg"/>
            </a>
            <a href="#" data-image="images/small/image2.jpg" data-zoom-image="images/large/image2.jpg">
                <img id="img_01" src="images/thumb/image2.jpg"/>
            </a>
            <a href="#" data-image="images/small/image3.jpg" data-zoom-image="images/large/image3.jpg">
                <img id="img_01" src="images/thumb/image3.jpg"/>
            </a>
            <a href="#" data-image="images/small/image4.jpg" data-zoom-image="images/large/image4.jpg">
                <img id="img_01" src="images/thumb/image4.jpg"/>
            </a>
            <a href="#" data-image="images/small/image5.jpg" data-zoom-image="images/large/image5.jpg">
                <img id="img_01" src="images/thumb/image5.jpg"/>
            </a>
        </div>

        <script type="text/javascript">
            $(document).ready(function () {
                $("#zoom_03").ezPlus({
                    gallery: 'gallery_01',
                    cursor: 'pointer',
                    galleryActiveClass: "active",
                    imageCrossfade: true,
                    loadingIcon: "images/spinner.gif"
                });

                $("#zoom_03").bind("click", function (e) {
                    var ez = $('#zoom_03').data('ezPlus');
                    ez.closeAll(); //NEW: This function force hides the lens, tint and window
                    $.fancybox(ez.getGalleryList());
                    return false;
                });

            });

        </script>

        <hr/>

        <h1>Window Position</h1>

        <p>
            Zoom on the right: <br/>
            <img id="zoom_04a" src="images/small/image1.jpg" data-zoom-image="images/large/image1.jpg"/>
        </p>

        <p>
            Zoom on the bottom: <br/>
            <img id="zoom_04b" src="images/small/image5.jpg" data-zoom-image="images/large/image5.jpg"/>
        </p>

        <p>
            Remote div zoom: <br/>
            <img id="zoom_04c" src="images/small/image1.jpg" data-zoom-image="images/large/image1.jpg"/>
        </p>


        <style>
            #demo-container {
                width: 100px;
                height: 100px;
                border: solid 3px black;
            }
        </style>

        <p>The zoom will go into this div:</p>

        <div id="demo-container">

        </div>

        <script>
            $("#zoom_04a").ezPlus({zoomWindowPosition: 1});
            $("#zoom_04b").ezPlus({zoomWindowPosition: 6});
            $("#zoom_04c").ezPlus({
                zoomWindowPosition: "demo-container",
                zoomWindowHeight: 200,
                zoomWindowWidth: 200,
                borderSize: 0,
                easing: true
            });
        </script>

        <hr/>

        <h1>Zoom in Modal</h1>
        <button>Launch Modal</button>
        <div class="ui modal" id="zoom_modal">
            <img id="zoom_01" src='images/small/image1.jpg' data-zoom-image="images/large/image1.jpg"/>
        </div>
        <script>
            $("button").click(function(){
                $(".ui.modal")
                    .modal({
                        onShow: function() {
                            $('#zoom_01').ezPlus({
                                z_index: 1004 //semantic-ui default modal's z-index is 1001
                            });
                        },
                        onHide: function() {
                            //destroy or remove elevateZoom object here.
                            $(".zoomContainer").remove();
                        }
                    })
                    .modal('show');
            });
        </script>
        <hr/>

        <h1>Responsive - Screen-size sensitivity</h1>

        <p>
            The example below defines 3 responsive CSS media queries and also in the 'respond' with 4 ranges.
        <ol style="float:left;text-align: left">
            <li>Default - tint color is #0F0(green) <span class="responsive_default">ACTIVE</span></li>
            <li>[800px,1199px] - tint color is #00F (purple) <span class="responsive_800">ACTIVE</span></li>
            <li>[600px,799px] - tint color is #F00(red) <span class="responsive_600">ACTIVE</span></li>
            <li>[100px,599px] - tint color is disabled <span class="responsive_100">ACTIVE</span></li>
        </ol>


        </p>
        <p>
            <span id="status"></span>
            <span id="imgWidth"></span>
        </p>

        <div style="clear: both"/>
        <p>
            <img id="responsive_img" src="images/small/image1.jpg" data-zoom-image="images/large/image1.jpg"/>
        </p>

        <script>
            $(function () {

                initEZPlus();

                //Triggered when window width is changed.
                $( window ).on( "resize", function() {
                    var windowWidth = $( window ).width(), // get window width
                        imgWidth = $( "#responsive_img").width(); // get image width
                    //Init elevateZoom
                    initEZPlus();
                    //display status
                    $( "#status" ).html("Status: Window resized!.");
                    //display image and window width
                    $( "#imgWidth" ).html("Image width: " + imgWidth + "px" + "<br />" + "Window width: " + windowWidth + "px");
                });

                function initEZPlus() {
                    $("#responsive_img").ezPlus({
                        responsive : true,
                        scrollZoom : false,
                        showLens: true,

                        tint: true,
                        tintColour: '#0F0',
                        tintOpacity: 0.5,
                        respond: [
                            {
                                range: '600-799',
                                tintColour: '#F00',
                                zoomWindowHeight: 100,
                                zoomWindowWidth: 100
                            },
                            {
                                range: '800-1199',
                                tintColour: '#00F',
                                zoomWindowHeight: 200,
                                zoomWindowWidth: 200
                            },
                            {
                                range: '100-599',
                                enabled: false,
                                showLens: false
                            }
                        ]
                    });
                }
            })
        </script>

    </div>

</div>
<!-- /.container -->

<!-- Bootstrap core JavaScript
================================================== -->
<!-- Placed at the end of the document so the pages load faster -->

<!--<script src="https://ajax.googleapis.com/ajax/libs/jquery/1.11.1/jquery.min.js"></script>-->
<script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.4/js/bootstrap.min.js"></script>
<!-- Add fancyBox main JS and CSS files -->
<script type="text/javascript" src="https://fancyapps.com/fancybox/source/jquery.fancybox.pack.js?v=2.1.5"></script>
<link rel="stylesheet" type="text/css" href="https://fancyapps.com/fancybox/source/jquery.fancybox.css?v=2.1.5" media="screen" />

</body>
</html>
