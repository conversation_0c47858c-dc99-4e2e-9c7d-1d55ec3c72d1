<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=euc-jp" />
<title>jQuery Simple Color</title>
<script type="text/javascript" src="jquery-1.9.1.min.js"></script>
<script type="text/javascript" src="../src/jquery.simple-color.js"></script>

<script  type="text/javascript">
$(document).ready(function(){

  $('.close_button').click(function(event) {
    $('input').closeChooser();
  });

  $('.set_color_button').click(function(event) {
    $('input').setColor('#cc3333');
  });

  $('.simple_color').simpleColor();

  $('.simple_color_show_input').simpleColor({ hideInput: false, inputCSS: { 'border-style': 'dashed', 'width': '111px', 'margin-bottom': '2px' } });

  $('.simple_color_color_code').simpleColor({ displayColorCode: true });

  $('.simple_color_custom_chooser_css').simpleColor({ chooserCSS: { 'background-color': 'black', 'opacity': '0.8' } });

  $('.simple_color_custom_display_css').simpleColor({ displayCSS: { 'border': '1px solid red' } });

  $('.simple_color_custom_cell_size').simpleColor({ cellWidth: 30, cellHeight: 10 });

  $('.simple_color_live_preview').simpleColor({ livePreview: true });

  $('.simple_color_callback').simpleColor({
    onSelect: function(hex, element) {
      alert("You selected #" + hex + " for input #" + element.attr('class'));
    }
  });

  $('.simple_color_mouse_enter').simpleColor({
    onCellEnter: function(hex, element) {
      console.log("You just entered #" + hex + " for input #" + element.attr('class'));
    }
  });

  $('.simple_color_kitchen_sink').simpleColor({
    boxHeight: 40,
    cellWidth: 20,
    cellHeight: 20,
    chooserCSS: { 'border': '1px solid #660033' },
    displayCSS: { 'border': '1px solid red' },
    displayColorCode: true,
    livePreview: true,
    onSelect: function(hex, element) {
      alert("You selected #" + hex + " for input #" + element.attr('class'));
    },
    onCellEnter: function(hex, element) {
      console.log("You just entered #" + hex + " for input #" + element.attr('class'));
    },
    onClose: function(element) {
      alert("color chooser closed for input #" + element.attr('class'));
    }
  });

});
</script>
</head>

<body>
  <button class='close_button'>Close all color choosers</button>
  <button class='set_color_button'>Set all to red</button>

  <h3>Basic</h3>
  <input class='simple_color' value='#cc3333'/>

  <h3>Display input field and customize field CSS</h3>
  <input class='simple_color_show_input' value='#cc3333'/>

  <h3>Display Color Code</h3>
  <input class='simple_color_color_code' value='#33cc33'/>

  <h3>Custom Color Chooser CSS</h3>
  <input class='simple_color_custom_chooser_css' value='#ff6633'/>

  <h3>Custom Color Display CSS</h3>
  <input class='simple_color_custom_display_css' value='#ccffff'/>

  <h3>Custom Cell Size</h3>
  <input class='simple_color_custom_cell_size' value='#cc9966'/>

  <h3>With Live Preview</h3>
  <input class='simple_color_live_preview' value='#ff00ff'/>

  <h3>With Callback</h3>
  <input class='simple_color_callback' value='#cc3333'/>

  <h3>With Mouse Entry Events (see javascript console)</h3>
  <input class='simple_color_mouse_enter' value='#336600'/>

  <h3>Kitchen Sink</h3>
  <input class='simple_color_kitchen_sink' value='#993300'/>
</body>
</html>
