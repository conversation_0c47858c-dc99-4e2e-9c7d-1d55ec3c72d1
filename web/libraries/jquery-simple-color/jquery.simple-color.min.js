!function(r){function n(c,f,o){var t,i=o.colorCodeColor||(!(t=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(f))||.213*parseInt(t[1],16)/255+.715*parseInt(t[2],16)/255+.072*parseInt(t[3],16)/255<.5?"#FFF":"#000");c.data("color",f).css({color:i,textAlign:o.colorCodeAlign,backgroundColor:f}),!0===o.displayColorCode&&c.text(f)}r.fn.simpleColor=function(e){var l=this;return(e=r.extend({defaultColor:this.attr("defaultColor")||"#FFF",cellWidth:this.attr("cellWidth")||10,cellHeight:this.attr("cellHeight")||10,cellMargin:this.attr("cellMargin")||1,boxWidth:this.attr("boxWidth")||"115px",boxHeight:this.attr("boxHeight")||"20px",columns:this.attr("columns")||16,insert:this.attr("insert")||"after",colors:this.attr("colors")||["990033","ff3366","cc0033","ff0033","ff9999","cc3366","ffccff","cc6699","993366","660033","cc3399","ff99cc","ff66cc","ff99ff","ff6699","cc0066","ff0066","ff3399","ff0099","ff33cc","ff00cc","ff66ff","ff33ff","ff00ff","cc0099","990066","cc66cc","cc33cc","cc99ff","cc66ff","cc33ff","993399","cc00cc","cc00ff","9900cc","990099","cc99cc","996699","663366","660099","9933cc","660066","9900ff","9933ff","9966cc","330033","663399","6633cc","6600cc","9966ff","330066","6600ff","6633ff","ccccff","9999ff","9999cc","6666cc","6666ff","666699","333366","333399","330099","3300cc","3300ff","3333ff","3333cc","0066ff","0033ff","3366ff","3366cc","000066","000033","0000ff","000099","0033cc","0000cc","336699","0066cc","99ccff","6699ff","003366","6699cc","006699","3399cc","0099cc","66ccff","3399ff","003399","0099ff","33ccff","00ccff","99ffff","66ffff","33ffff","00ffff","00cccc","009999","669999","99cccc","ccffff","33cccc","66cccc","339999","336666","006666","003333","00ffcc","33ffcc","33cc99","00cc99","66ffcc","99ffcc","00ff99","339966","006633","336633","669966","66cc66","99ff99","66ff66","339933","99cc99","66ff99","33ff99","33cc66","00cc66","66cc99","009966","009933","33ff66","00ff66","ccffcc","ccff99","99ff66","99ff33","00ff33","33ff33","00cc33","33cc33","66ff33","00ff00","66cc33","006600","003300","009900","33ff00","66ff00","99ff00","66cc00","00cc00","33cc00","339900","99cc66","669933","99cc33","336600","669900","99cc00","ccff66","ccff33","ccff00","999900","cccc00","cccc33","333300","666600","999933","cccc66","666633","999966","cccc99","ffffcc","ffff99","ffff66","ffff33","ffff00","ffcc00","ffcc66","ffcc33","cc9933","996600","cc9900","ff9900","cc6600","993300","cc6633","663300","ff9966","ff6633","ff9933","ff6600","cc3300","996633","330000","663333","996666","cc9999","993333","cc6666","ffcccc","ff3333","cc3333","ff6666","660000","990000","cc0000","ff0000","ff3300","cc9966","ffcc99","ffffff","cccccc","999999","666666","333333","000000","000000","000000","000000","000000","000000","000000","000000","000000"],displayColorCode:this.attr("displayColorCode")||!1,colorCodeAlign:this.attr("colorCodeAlign")||"center",colorCodeColor:this.attr("colorCodeColor")||!1,hideInput:this.attr("hideInput")||!0,onSelect:null,onCellEnter:null,onClose:null,livePreview:!1},e||{})).totalWidth=e.columns*(e.cellWidth+2*e.cellMargin),e.chooserCSS=r.extend({border:"1px solid #000",margin:"0 0 0 5px",width:e.totalWidth,height:e.totalHeight,top:0,left:e.boxWidth,position:"absolute","background-color":"#fff"},e.chooserCSS||{}),e.displayCSS=r.extend({"background-color":e.defaultColor,border:"1px solid #000",width:e.boxWidth,height:e.boxHeight,"line-height":e.boxHeight+"px",cursor:"pointer"},e.displayCSS||{}),e.inputCSS=r.extend({},e.inputCSS||{}),e.hideInput?this.hide():this.css(e.inputCSS),-1!=navigator.userAgent.indexOf("MSIE")&&(e.totalWidth+=2),e.totalHeight=Math.ceil(e.colors.length/e.columns)*(e.cellHeight+2*e.cellMargin),r.simpleColorOptions=e,this.each(function(c){e=r.simpleColorOptions;var f=r("<div class='simpleColorContainer' />");f.css("position","relative");var o=this.value&&""!=this.value?this.value:e.defaultColor,i=r("<div class='simpleColorDisplay' />");i.css(e.displayCSS),n(i,o,e),f.append(i);var t={input:this,container:f,displayBox:i};i.bind("click",t,function(o){if(r("html").bind("click.simpleColorDisplay",function(c){r("html").unbind("click.simpleColorDisplay"),r(".simpleColorChooser").hide();var f=r(c.target);!1!==f.is(".simpleColorCell")&&!1!==r.contains(r(o.target).closest(".simpleColorContainer")[0],f[0])||n(i,i.data("color"),e),e.onClose&&e.onClose(l)}),o.data.container.chooser)o.data.container.chooser.toggle();else{var c=r("<div class='simpleColorChooser'/>");c.css(e.chooserCSS),o.data.container.chooser=c,o.data.container.append(c);for(var f=0;f<e.colors.length;f++){var t=r("<div class='simpleColorCell' id='"+e.colors[f]+"'/>");t.css({width:e.cellWidth+"px",height:e.cellHeight+"px",margin:e.cellMargin+"px",cursor:"pointer",lineHeight:e.cellHeight+"px",fontSize:"1px",float:"left","background-color":"#"+e.colors[f]}),c.append(t),(e.onCellEnter||e.livePreview)&&t.bind("mouseenter",function(c){e.onCellEnter&&e.onCellEnter(this.id,l),e.livePreview&&n(i,"#"+this.id,e)}),t.bind("click",{input:o.data.input,chooser:c,displayBox:i},function(c){var f="#"+this.id;c.data.input.value=f,r(c.data.input).change(),n(i,f,e),c.data.chooser.hide(),e.displayColorCode&&c.data.displayBox.text(f),e.onSelect&&e.onSelect(this.id,l)})}}}),r(this).after(f),r(this).data("container",f)}),r(".simpleColorDisplay").each(function(){r(this).click(function(c){c.stopPropagation()})}),this},r.fn.closeChooser=function(){return this.each(function(c){r(this).data("container").find(".simpleColorChooser").hide()}),this},r.fn.setColor=function(o){return this.each(function(c){var f=r(this).data("container").find(".simpleColorDisplay");n(f,o,{displayColorCode:f.data("displayColorCode")})}),this}}(jQuery);