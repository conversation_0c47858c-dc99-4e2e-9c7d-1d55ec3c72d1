<!doctype html>
<html>
<head>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <title>Spectrum - The No Hassle jQuery Colorpicker</title>

    <meta name="description" content="Spectrum is a JavaScript colorpicker plugin using the jQuery framework.  It is highly customizable, but can also be used as a simple input type=color polyfill">
    <meta name="author" content="<PERSON> and Spectrum contributors">

    <link rel="stylesheet" type="text/css" href="../spectrum.css">
    <link rel="stylesheet" type="text/css" href="../docs/bootstrap.css">
    <link rel="stylesheet" type="text/css" href="../docs/docs.css">
    <script type="text/javascript" src="../docs/jquery-1.9.1.js"></script>
    <script type="text/javascript" src="../spectrum.js"></script>
	<script type="text/javascript" src="../i18n/jquery.spectrum-fi.js"></script>
    <script type='text/javascript' src='example.js'></script>

    <style>

    .example-container {
        margin-top: 10px;
        text-align: center;
        background: #333;
        background: linear-gradient(to bottom, #eee, #ccc);
        padding: 3px;
        padding-top: 0;
        border-radius: 5px;
    }
    .example-controls {
        background: #999;
        margin: 0 -3px;
        padding: 10px 0;
        margin-bottom: 15px;

    }
    label {
        display: inline-block;
        font-weight: bold;

    }
    #palettes .sp-palette {
        max-width: 500px;
    }


    .newGmail .sp-palette-row-0, .newGmail .sp-palette-row-1 {
        margin-bottom: 5px;
    }

    .newGmail .sp-palette .sp-thumb-el {
        width: 20px;
        height: 20px;
        margin: 1px 1px;
    }

    .newGmail .sp-palette .sp-thumb-el:hover, .newGmail  .sp-palette .sp-thumb-el.sp-thumb-active {
        border-color: #000;
    }
    </style>
</head>
<body>
<div id='header'>
    <h1><a href='http://bgrins.github.com/spectrum'>Spectrum</a></h1> <h2><em>The No Hassle jQuery Colorpicker</em></h2>
    <div id='links'>
        View the <a href='http://github.com/bgrins/spectrum'>Source code</a>.
        Spectrum is a project by <a href='http://twitter.com/bgrins'>@bgrins</a>.
    </div>
    <br style='clear:both;' />
</div>

<div class="container">

    <h2>Spectrum Colorpicker Crazy Configurator</h2>
    <div class='alert'>NOTE: this page is currently in development.  Please refer to the <a href='http://github.com/bgrins/spectrum'>Home Page</a> for demos and documentation instead.
    </div>
    <p>
        Spectrum can be customized to show and hide different portions of the colorpicker.  Try clicking some of the buttons below to see how it can change.
    </p>

    <div class="example-container">
        <div class="example-controls">
            <div class='btn-group'>
                <button class='btn toggleBtn' data-rule='showPalette'>Show Palette</button>
                <button class='btn toggleBtn' data-rule='showInput'>Show Input</button>
                <button class='btn toggleBtn' data-rule='showInitial'>Show Initial</button>
                <button class='btn toggleBtn' data-rule='showAlpha'>Show Alpha</button>
                <button class='btn toggleBtn' data-rule='showPaletteOnly'>Show Palette Only</button>
                <button class='btn toggleBtn' data-rule='togglePaletteOnly'>Show Picker Toggle Button</button>
                <button class='btn toggleBtn' data-rule='showButtons'>Show Buttons</button>
            </div>
            <br />
            <br />
            <p>
            <label>Draggable Size <input type='range' value='172' id='size' max='500' min='50' /></label>

            <label>Hue Size <input type='range' value='16' id='huesize' max='90' min='5' /></label>
            </p>
        </div>

        <input id="full">

    </div>

    <hr />
    <h2>Spectrum Colorpicker Localization</h2>
    <div class='alert'>
        <p>
            This page has loaded the German localization.  Here is a list of all <a href='https://github.com/bgrins/spectrum/tree/master/i18n'>spectrum localizations</a>.  <strong>Please help expand our localizations</strong>  if you know a language that isn't represented!  You can copy and paste one of the files, and update the text for 'cancel' and 'choose', then submit a pull request at: <a href'https://github.com/bgrins/spectrum'>https://github.com/bgrins/spectrum</a>.
        </p>
    </div>

	<input id="langdemo" />

    <hr />
    <h2>Spectrum Colorpicker Sample Palettes</h2>
    <div class='alert'>

        <p>
        NOTE: these palettes are also a work in progress.  Ideally the site will eventually allow you to choose between a few options and download them.
        </p>

        <p>
        The <code>newGmail</code> palette below is an example of customizing the palette size and layout with CSS.
        </p>

    </div>
    <div id="palettes" class="example-container">

    </div>

</div>


<script type="text/javascript" src="../docs/prettify.js"></script>
<script type="text/javascript">

  var _gaq = _gaq || [];
  _gaq.push(['_setAccount', 'UA-8259845-4']);
  _gaq.push(['_trackPageview']);

  (function() {
    var ga = document.createElement('script'); ga.type = 'text/javascript'; ga.async = true;
    ga.src = ('https:' == document.location.protocol ? 'https://ssl' : 'http://www') + '.google-analytics.com/ga.js';
    var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(ga, s);
  })();

</script>
</body>
</html>
