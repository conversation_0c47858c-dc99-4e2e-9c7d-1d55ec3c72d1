{"name": "spectrum-colorpicker", "description": "Spectrum: the no hassle jQ<PERSON>y colorpicker", "version": "1.8.1", "main": "spectrum.js", "license": "MIT", "keywords": ["jquery-plugin", "ecosystem:jquery", "color", "colorpicker", "ui"], "homepage": "http://bgrins.github.com/spectrum", "repository": {"type": "git", "url": "https://bgrins.github.com/spectrum"}, "author": {"name": "<PERSON>", "email": "brian<PERSON><PERSON><PERSON>@gmail.com", "url": "http://briangrinstead.com/"}, "devDependencies": {"grunt": "^1.1.0", "grunt-contrib-jshint": "^2.1.0", "grunt-contrib-qunit": "^3.1.0", "grunt-contrib-uglify": "^4.0.1"}}