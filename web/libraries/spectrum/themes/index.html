<!doctype html>
<html>
<head>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <title>Spectrum - The No Hassle jQuery Colorpicker</title>

    <meta name="description" content="Spectrum is a JavaScript colorpicker plugin using the jQuery framework.  It is highly customizable, but can also be used as a simple input type=color polyfill">
    <meta name="author" content="<PERSON> and Spectrum contributors">

    <link rel="stylesheet" type="text/css" href="../spectrum.css">
    <link rel="stylesheet" type="text/css" href="../docs/bootstrap.css">
    <link rel="stylesheet" type="text/css" href="../docs/docs.css">
    <script type="text/javascript" src="../docs/jquery-1.9.1.js"></script>
    <script type="text/javascript" src="../spectrum.js"></script>


    <link rel="stylesheet" type="text/css" href="sp-dark.css">

</head>
<body>
<div id='header'>
    <h1><a href='http://bgrins.github.com/spectrum'>Spectrum</a></h1> <h2><em>The No Hassle jQuery Colorpicker</em></h2>
    <div id='links'>
        View the <a href='http://github.com/bgrins/spectrum'>Source code</a>.
        Spectrum is a project by <a href='http://twitter.com/bgrins'>@bgrins</a>.
    </div>
    <br style='clear:both;' />
</div>

<div class="container">
    <h2>Themes</h2>

    <div class="alert">
        This page is in development.
    </div>

    <div id="theme-gallery">
        <h3>Gallery of existing themes</h3>

        <div class="theme" id="sp-light">
            <h4>sp-light</h4>
            <p>This is the default theme that you know and love.</p>

            <div class='example'>
                <input type='text' />
            </div>
        </div>

        <div class="theme" id="sp-dark">
            <h4>sp-dark</h4>
            <p>Similar to sp-light, only ... darker</p>

            <div class='example'>
                <input type='text' />
            </div>
        </div>
    </div>

    <div id="theme-instructions">
        <h3>Instructions for building themes</h3>
        <p>
            You can change most any property on spectrum using CSS.  Anything from  borders and colors, to the size of the draggable areas, to the layout of the colorpicker can be changed with plain CSS.
        </p>
        <h4>Playing friendly with other themes</h4>
        <p>
            Please prefix all of your rules with <code>.theme-name</code>.  The exception is for changes to <code>.sp-container</code> and <code>.sp-replacer</code>, which will have your theme name applied.
        </p>
        <p>
            See a basic scaffold for a super simple theme.  See <a href='sp-dark.css'>sp-dark.css</a> for a slightly more advanced example.
        </p>
        <pre>
.theme-name.sp-container {

}
.theme-name.sp-replacer {

}
.theme-name .sp-preview {

}
        </pre>
        <h3>Submitting a theme</h3>
        <p>
            If you have made some customizations that you would like to share, please open a <a href="http://bgrins.github.com/spectrum/pulls">pull request</a> with the theme file inside of this themes/ directory in the project.  Or <a href="http://bgrins.github.com/spectrum/issues">open an issue</a> with a link to the theme.
        </p>
    </div>
</div>


<script>
    $("#sp-light input").spectrum({
        theme: "sp-light"
    });
    $("#sp-dark input").spectrum({
        theme: "sp-dark"
    });
</script>
<script type="text/javascript" src="../docs/prettify.js"></script>
<script type="text/javascript">

  var _gaq = _gaq || [];
  _gaq.push(['_setAccount', 'UA-8259845-4']);
  _gaq.push(['_trackPageview']);

  (function() {
    var ga = document.createElement('script'); ga.type = 'text/javascript'; ga.async = true;
    ga.src = ('https:' == document.location.protocol ? 'https://ssl' : 'http://www') + '.google-analytics.com/ga.js';
    var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(ga, s);
  })();

</script>
</body>
</html>
