/* Container */
.sp-dark.sp-container {
    background-color: #333;
    border: solid 1px #555;
}

/* Replacer (the little preview div that shows up instead of the <input>) */
.sp-dark.sp-replacer {
    border: solid 1px #fff;
    background: #333;
    color: #eee;
    vertical-align: middle;
}
.sp-replacer:hover, .sp-replacer.sp-active {
    border-color: #F0C49B;
    color: #fff;
}
.sp-replacer.sp-disabled {
    border-color: silver;
    color: silver;
}
.sp-dark .sp-preview {
    border: solid 1px #999;
}
.sp-dark .sp-cancel {
    color: #f99f9f !important;
}

.sp-dark, .sp-dark button, .sp-dark input, .sp-color, .sp-hue {

}

/* Input */
.sp-dark .sp-input-container {

}
.sp-dark .sp-initial-disabled .sp-input-container {

}
.sp-dark .sp-input {

}
.sp-dark .sp-input:focus  {

}
.sp-dark .sp-input.sp-validation-error {

}

.sp-dark .sp-picker-container , .sp-dark .sp-palette-container {

}
.sp-dark .sp-picker-container {

}

/* Palettes */
.sp-dark .sp-palette-container {

}

.sp-dark .sp-palette .sp-thumb-el {

}
.sp-dark .sp-palette .sp-thumb-el:hover, .sp-dark .sp-palette .sp-thumb-el.sp-thumb-active {

}
.sp-dark .sp-thumb-el {
}

/* Initial */
.sp-dark .sp-initial {

}
.sp-dark .sp-initial span {

}

/* Buttons */
.sp-dark .sp-button-container {

}

/* Replacer (the little preview div that shows up instead of the <input>) */
.sp-dark.sp-replacer {

}
.sp-dark.sp-replacer:hover, .sp-dark.sp-replacer.sp-active {
    border-color: #F0C49B;
    color: #111;
}
.sp-dark.sp-replacer.sp-disabled {

}
.sp-dark .sp-dd {

}



.sp-dark .sp-preview {

}
.sp-dark .sp-palette {

}
.sp-dark .sp-palette .sp-thumb-el {

}

.sp-dark button {

}
.sp-dark button:hover {

}
.sp-dark button:active {

}
.sp-dark .sp-cancel {

}
.sp-dark .sp-cancel:hover {

}
.sp-dark .sp-palette span:hover, .sp-dark .sp-palette span.sp-thumb-active {

}
