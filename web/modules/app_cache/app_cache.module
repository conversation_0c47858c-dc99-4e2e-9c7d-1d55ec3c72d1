<?php

/**
 * @file
 * Contains app_cache.module.
 */

use Drupal\Core\Routing\RouteMatchInterface;

/**
 * Implements hook_help().
 */
function app_cache_help($route_name, RouteMatchInterface $route_match) {
  switch ($route_name) {
    // Main module help for the app_cache module.
    case 'help.page.app_cache':
      $output = '';
      $output .= '<h3>' . t('About') . '</h3>';
      $output .= '<p>' . t('A module to control special caching for the mobile web views.') . '</p>';
      return $output;

    default:
  }
}

