<?php

namespace Drupal\app_cache\CacheContext;

use <PERSON><PERSON><PERSON>\Core\Cache\CacheableMetadata;
use <PERSON><PERSON>al\Core\Cache\Context\CacheContextInterface;
use Symfony\Component\HttpFoundation\RequestStack;

/**
 * Class UserAgentCacheContext.
 * Defines the UserAgentCacheContext service, for "per User Agent" caching.
 *
 * Cache context ID: 'user_agent'.
 */
class UserAgentCacheContext implements CacheContextInterface {

  /**
   * Symfony\Component\HttpFoundation\RequestStack definition.
   *
   * @var RequestStack
   */
  protected $requestStack;


  /**
   * Constructs a new UserAgentCacheContext object.
   */
  public function __construct(RequestStack $request_stack) {
    $this->requestStack = $request_stack;
  }

  /**
   * {@inheritdoc}
   */
  public static function getLabel() {
    \Drupal::messenger()->addMessage('Label of cache context');
    return t('App Cache User Agent Context');
  }
  /**
   * {@inheritdoc}
   */
  public function getContext() {
    // Actual logic of context variation will lie here.
    $user_agent = $this->requestStack->getCurrentRequest()->headers->get('User-Agent', '');
    if (!empty($user_agent) && str_contains($user_agent, 'ISApp/1.0.0')) {
      return 'app';
    }
    return 'not_app';
  }

  /**
   * {@inheritdoc}
   */
  public function getCacheableMetadata() {
    return new CacheableMetadata();
  }

}
