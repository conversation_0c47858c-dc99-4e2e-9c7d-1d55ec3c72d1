<?php
namespace Drupal\app_cache\PageCache;

use Symfony\Component\HttpFoundation\Request;
use Drupal\Core\PageCache\RequestPolicyInterface;
use Drupal\Core\Routing\RouteMatchInterface;

class denyappcache implements RequestPolicyInterface {
  /**
   * {@inheritdoc}
   */
  public function check(Request $request) {
    $ua = $request->headers->get('User-Agent', '');
    if (!empty($ua) && str_contains($ua, 'ISApp/1.0.0')) {
      return static::DENY;
    }
  }
}
