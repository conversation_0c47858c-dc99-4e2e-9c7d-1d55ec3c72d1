<?php

/**
 * @file
 * Contains ckeditor_entity_link.module.
 */

use Drupal\Core\Routing\RouteMatchInterface;

/**
 * Implements hook_help().
 */
function ckeditor_entity_link_help($route_name, RouteMatchInterface $route_match) {
  switch ($route_name) {
    // Main module help for the ckeditor_entity_link module.
    case 'help.page.ckeditor_entity_link':
      $output = '';
      $output .= '<h3>' . t('About') . '</h3>';
      $output .= '<p>' . t('Allows to insert entity links in CKEditor.') . '</p>';
      return $output;

    default:
  }
}

/**
 * Implements hook_theme().
 */
function ckeditor_entity_link_theme() {
  $theme = [];

  return $theme;
}
