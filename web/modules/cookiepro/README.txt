INTRODUCTION
------------

CookiePro provides purpose-built tools to help website owners and agencies simplify compliance with global privacy laws including GDPR, CCPA, and ePrivacy and is powered by the OneTrust platform. OneTrust was named a leader in the Forrester New Wave™: GDPR and Privacy Management Software, Q4 2018.

REQUIREMENTS
------------

The CookiePro module requires a CookiePro account. To sign up for a free or paid account, visit CookiePro.com and select the edition that fits your business needs.

INSTALLATION
------------

 * Install as you would normally install a contributed Drupal module. Visit:
   https://www.drupal.org/docs/8/extend/installing-modules
   for further information.

CONFIGURATION
-------------

 * Configure user permissions in Administration » People » Permissions

   - Access CookiePro by OneTrust

     Users in roles with Add Scripts all over the site permission will
     add / remove the  scripts from any position.

 * Add CookiePro Header scripts in settings on the Administer ->
   Configuration -> Development -> CookiePro by OneTrust.

FAQ
---
Q: I enabled Aggregate and compress CSS files, but admin_menu.css is still
   there. Is this normal?

A: Yes, this is the intended behavior. the administration menu module only loads
   its stylesheet as needed (i.e., on page requests by logged-on, administrative
   users).

Q: What is included in the Free Edition of CookiePro?
A: The free edition of CookiePro includes key features businesses need to comply with cookie consent requirements across privacy regulations such as the GDPR and ePrivacy for a single domain with up to 100 subpages.

Q: How do I upgrade my Free Edition of CookiePro?
A: If you would like to upgrade to a Starter, Standard or Enterprise subscription, simply select the edition you would like to purchase on the CookiePro pricing page and click “Buy Now”. Once you enter the email associated with your existing free account, simply follow the checkout process, enter your payment details and you will have access to your new edition.

Q: Can I add several small websites to 1 subscription?
A: Prices are per domain. Each domain requires one subscription. Cross sub-domain consent is available.


MAINTAINERS
-----------

Current maintainers:
 * CookiePro

