<?php

/**
 * @file
 * Add cookiepro script.
 */

use <PERSON><PERSON>al\Core\Routing\RouteMatchInterface;

/**
 * Implements hook_help().
 */
function cookiepro_help($route_name, RouteMatchInterface $route_match) {
  switch ($route_name) {
    case 'help.page.cookiepro':
      $output = '<h2>' . t('About') . '</h2>';
      $output .= '<p>' . t('CookiePro provides purpose-built tools to help website owners and agencies simplify compliance with global privacy laws including GDPR, CCPA, and ePrivacy and is powered by the OneTrust platform. <a href="https://www.onetrust.com/resources/onetrust-named-a-leader-forrester-new-wave-2018/" target="_blank">OneTrust</a> was named a leader in the Forrester New Wave™: GDPR and Privacy Management Software, Q4 2018.') . '</p>';
      $output .= '<h2>' . t('Get Help') . '</h2>';
      $output .= '<p>' . t('Email us at <a href="mailto:<EMAIL>" target="_top"><EMAIL></a> </br> Browse <a href="https://community.cookiepro.com" target="_blank">CookiePro Community</a> for knowledgebase articles, product updates and privacy news.') . '</p>';

      $output .= '<h2>' . t('FAQs') . '</h2>';
      $output .= '<p>' . t('<strong>What is included in the Free Edition of CookiePro? </strong>') . '</p>';
        $output .= '<p>' . t('The free edition of CookiePro includes key features businesses need to comply with cookie consent requirements across privacy regulations such as the GDPR and ePrivacy for a single domain with up to 100 subpages.') . '</p>';
        $output .= '<p>' . t('</br><strong>How do I upgrade my Free Edition of CookiePro? </strong>') . '</p>';
          $output .= '<p>' . t('If you would like to upgrade to a Starter, Standard or Enterprise subscription, simply select the edition you would like to purchase on the <a href="https://www.cookiepro.com/pricing/?referral=drupmod" target="_blank">CookiePro</a> pricing page and click “Buy Now”. Once you enter the email associated with your existing free account, simply follow the checkout process, enter your payment details and you will have access to your new edition.') . '</p>';
          $output .= '<p>' . t('</br><strong>Can I add several small websites to 1 subscription? </strong>') . '</p>';
            $output .= '<p>' . t('Prices are per domain. Each domain requires one subscription. Cross sub-domain consent is available.') . '</p>';



      return $output;
  }
}


/**
 * Implements hook_page_attachments_alter().
 *
 * Addes JS scripts as needed.
 * which are defined on the settings page.
 */
function cookiepro_page_attachments_alter(array &$attachments) {
  $header_section = \Drupal::config('cookiepro.header.settings')->get();
  if (isset($header_section['scripts']) && !empty($header_section['scripts'])) {
    $output_scripts = preg_split("/(<\/script>|<\/noscript>)/", preg_replace('/<!--(.|\s)*?-->/', '', $header_section['scripts']));
    $i = 1;
    $i = count($attachments['#attached']['html_head']) + 1;
    foreach ($output_scripts as $row) {

      if (empty($row)) {
        continue;
      }

      $script_tag  = 'script';
      $script_attr = [];
      $value       = '';

      $script_attributes = preg_replace('/(<script|<noscript)/', '', $row, 1);
      $get_script_attr   = preg_split('/(>)/', $script_attributes, 2);

      if (isset($get_script_attr[1])) {
        $value = $get_script_attr[1];
      }

      $get_script_tag = preg_split('/<noscript/', $row, 2);

      if (isset($get_script_tag[1])) {
        $script_tag = 'noscript';
      }

      if (isset($get_script_attr[0]) && !empty($get_script_attr[0])) {
        $get_attr   = preg_replace('/(\'|\")/', '', $get_script_attr[0]);
        $get_attr   = preg_replace('/\s+/', ',', $get_attr);
        $get_attr   = preg_replace('/(,=,|,=|=,)/', '=', $get_attr);
        $fetch_attr = explode(',', $get_attr);

        foreach ($fetch_attr as $attr) {
          if (empty($attr)) {
            continue;
          }

          $attr_key_value = explode('=', $attr);
          if (isset($attr_key_value[0]) && isset($attr_key_value[1])) {
            $script_attr[$attr_key_value[0]] = $attr_key_value[1];
          }
          else {
            $script_attr[$attr_key_value[0]] = $attr_key_value[0];
          }
        }
      }

      $attachments['#attached']['html_head'][$i] = [
        [
          '#type'  => 'html_tag',
          '#tag'   => $script_tag,
          '#value' => $value,
        ],
        'cookiepro-' . $i,
      ];
      if (!empty($script_attr)) {
        $attachments['#attached']['html_head'][$i][0]['#attributes'] = $script_attr;
      }
      $i++;
    }
  }
}
