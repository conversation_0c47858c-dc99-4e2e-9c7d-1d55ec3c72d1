<?php

use \Drupal\Core\File\HtaccessWriter;

/**
 * @file
 * Installation Process.
 */

function run_htaccess_writer($service) {
  \Drupal::service('cache.discovery')->invalidateAll();
  $htaccessWriter = \Drupal::service($service);
  $htaccessWriter->ensure();
}

/**
 * Implements hook_install().
 */
function dhw_install() {
  run_htaccess_writer("file.htaccess_writer");
	\Drupal::messenger()->addMessage(t('The Disable Htaccess Writer module was successfully enabled.'), 'status');
}

/**
 * Implements hook_uninstall().
 */
function dhw_uninstall() {
  run_htaccess_writer("file.htaccess_writer_core");
	\Drupal::messenger()->addMessage(t('The Disable Htaccess Writer module was successfully disable.'), 'status');
}
