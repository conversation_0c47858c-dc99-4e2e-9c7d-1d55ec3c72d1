<?php

use Drupal\informed_batch\ProductCorrectionRule;

/**
 * @file
 * Install, update, and uninstall functions for the Locale module.
 */

/**
 * Implements hook_install().
 */
function informed_batch_install()
{
    (new ProductCorrectionRule())->initFromFile();
}

/**
 * Implements hook_schema().
 */
function informed_batch_schema()
{
    $schema = [];
    $schema['informed_product_correction_rules'] = [
        'fields' => [
            'id' => [
                'type' => 'serial',
                'unsigned' => TRUE,
                'not null' => TRUE
            ],
            'original_manufacturer' => [
                'type' => 'varchar',
                'length' => 255,
            ],
            'original_product' => [
                'type' => 'varchar',
                'length' => 255,
            ],
            'original_flavour' => [
                'type' => 'varchar',
                'length' => 255,
            ],
            'correct_manufacturer' => [
                'type' => 'varchar',
                'length' => 255,
            ],
            'correct_product' => [
                'type' => 'varchar',
                'length' => 255,
            ],
            'correct_flavour' => [
                'type' => 'varchar',
                'length' => 255,
            ],
            'effective' => [
                'type' => 'int',
                'not null' => TRUE,
                'size' => 'tiny',
                'default' => 1
            ],
            'created' => [
                'type' => 'int',
                'not null' => TRUE,
                'size' => 'normal',
                'default' => '0'
            ],
            'changed' => [
                'type' => 'int',
                'not null' => TRUE,
                'size' => 'normal',
                'default' => '0'
            ]
        ],
        'primary key' => [
            'id'
        ]
    ];

    return $schema;
}
