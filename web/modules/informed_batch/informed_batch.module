<?php

/**
 * Implements hook_cron().
 */
function informed_batch_cron()
{
    \Drupal::service('informed_batch.import_manager')->importFromFtp();
}

/**
 * Implements hook_mail()
 */
function informed_batch_mail($key, &$message, $params)
{
    if ($key == 'batch_imported') {
        $message['subject'] = $params['subject'];
        $message['body'] = $params['body'];
        $message['headers']['Content-Type'] = 'text/html; charset=UTF-8; format=flowed';
        if (isset($params['attachments'])) {
            $message['attachments'] = $params['attachments'];
        }
    }
}
