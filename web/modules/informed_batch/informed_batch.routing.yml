informed_batch.import_cron:
  path: '/admin/config/informed_batch/import-cron'
  defaults:
    _controller: '\Drupal\system\Controller\SystemController::systemAdminMenuBlockPage'
    _title: 'Batch Importing Cron'
  requirements:
    _permission: 'access administration pages'
  options:
    _admin_route: TRUE
informed_batch.import_cron_configuration:
  path: '/admin/config/informed_batch/import-cron/configuration'
  defaults:
    _form: '\Drupal\informed_batch\Form\ImportCronConfiguration'
    _title: 'Batch Importing Cron Configuration'
  requirements:
    _permission: 'access administration pages'
  options:
    _admin_route: TRUE
informed_batch.product_correction_rule:
  path: '/admin/config/informed_batch/import-cron/rule'
  defaults:
    _controller: '\Drupal\informed_batch\Controller\ProductCorrectionRuleController::index'
    _title: 'Product Correction Rules'
  requirements:
    _permission: 'access administration pages'
  options:
    _admin_route: TRUE
informed_batch.product_correction_rule_create:
  path: '/admin/config/informed_batch/import-cron/rule/create'
  defaults:
    _form: '\Drupal\informed_batch\Form\ProductCorrectionRuleForm'
    _title: 'Create a Product Correction Rule'
  requirements:
    _permission: 'access administration pages'
  options:
    _admin_route: TRUE
informed_batch.product_correction_rule_edit:
  path: '/admin/config/informed_batch/import-cron/rule/{id}/edit'
  defaults:
    _form: '\Drupal\informed_batch\Form\ProductCorrectionRuleForm'
    _title: 'Edit Product Correction Rule'
  requirements:
    _permission: 'access administration pages'
  options:
    _admin_route: TRUE
informed_batch.product_correction_rule_delete:
  path: '/admin/config/informed_batch/import-cron/rule/{id}/delete'
  defaults:
    _controller: '\Drupal\informed_batch\Controller\ProductCorrectionRuleController::ajaxDestroy'
    _title: 'Delete a Product Correction Rule'
  requirements:
    _permission: 'access administration pages'
  options:
    _admin_route: TRUE
informed_batch.product_correction_rule_disable:
  path: '/admin/config/informed_batch/import-cron/rule/{id}/disable'
  defaults:
    _controller: '\Drupal\informed_batch\Controller\ProductCorrectionRuleController::ajaxDisable'
    _title: 'Disable a Product Correction Rule'
  requirements:
    _permission: 'access administration pages'
  options:
    _admin_route: TRUE