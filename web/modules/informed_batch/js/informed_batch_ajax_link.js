/**
 * @file
 * Customizable link behaviors.
 */

(function ($, <PERSON><PERSON><PERSON>) {

    'use strict';

    Drupal.behaviors.informed_batch_ajax_link = {
        attach: function (context, settings) {

            $("a.op-ajax-confirm", context).on("click", function (e) {
                var message = $(this).attr("confirm-message") || Drupal.t('Are you sure you want to do this?');
                if (confirm(message)) {
                    $.post($(this).attr("href"), function (data) {
                        if (data.code == 1) {
                            window.location.reload();
                        } else {
                            alert(data.msg);
                            return false;
                        }
                    });
                }
                e.preventDefault();
            });
        }
    };
})(j<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>);