<?php

namespace Drupal\informed_batch\Controller;

use <PERSON><PERSON>al\Core\Ajax\AjaxResponse;
use <PERSON>upal\Core\Controller\ControllerBase;
use Symfony\Component\HttpFoundation\Request;
use Drupal\Core\Url;

/**
 * Class ProductCorrectionRuleController.
 */
class ProductCorrectionRuleController extends ControllerBase
{
    /**
     * View rule list
     * @param Request $request
     * @return array
     */
    public function index(Request $request)
    {
        $keywords = trim($request->get('keywords') ?? '');

        $build = [];
        $build['filters'] = [
            '#type' => 'fieldset',
            '#title' => $this->t('Filter'),
            '#attributes' => ['class' => ['container-inline']],
        ];
        $build['filters']['form'] = \Drupal::formBuilder()->getForm('Drupal\informed_batch\Form\ProductCorrectionRuleFilterForm');

        $header = [
            ['data' => $this->t('ID'), 'field' => 'id', 'sort' => 'desc'],
            ['data' => $this->t('Original Manufacturer'), 'field' => 'original_manufacturer'],
            ['data' => $this->t('Original Product'), 'field' => 'original_product'],
            ['data' => $this->t('Original Flavour'), 'field' => 'original_flavour'],
            ['data' => $this->t('Correct Manufacturer'), 'field' => 'correct_manufacturer'],
            ['data' => $this->t('Correct Product'), 'field' => 'correct_product'],
            ['data' => $this->t('Correct Flavour'), 'field' => 'correct_flavour'],
            ['data' => $this->t('Status')],
            ['data' => $this->t('Operations')],
        ];

        // query
        $query = \Drupal::database()->select('informed_product_correction_rules', 'r');
        $query->fields('r');
        if ($keywords) {
            $orGroup = $query->orConditionGroup()
                ->condition('original_manufacturer', "%{$keywords}%", 'LIKE')
                ->condition('original_product', "%{$keywords}%", 'LIKE')
                ->condition('original_flavour', "%{$keywords}%", 'LIKE')
                ->condition('correct_manufacturer', "%{$keywords}%", 'LIKE')
                ->condition('correct_product', "%{$keywords}%", 'LIKE')
                ->condition('correct_flavour', "%{$keywords}%", 'LIKE');
            $query->condition($orGroup);
        }
        $table_sort = $query->extend('Drupal\Core\Database\Query\TableSortExtender')->orderByHeader($header);
        $pager = $table_sort->extend('Drupal\Core\Database\Query\PagerSelectExtender')->limit(20);
        $result = $pager->execute();

        // table
        $rows = [];
        foreach ($result as $idx => $item) {
            $data = [
                'id' => $item->id,
                'original_manufacturer' => $item->original_manufacturer,
                'original_product' => $item->original_product,
                'original_flavour' => $item->original_flavour,
                'correct_manufacturer' => $item->correct_manufacturer,
                'correct_product' => $item->correct_product,
                'correct_flavour' => $item->correct_flavour,
                'status' => $item->effective ? 'Active' : 'Inactive'
            ];

            $links = [];
            if ($item->effective) {
                $links['disable'] = [
                    'title' => $this->t('Disable'),
                    'url' => Url::fromRoute('informed_batch.product_correction_rule_disable', ['id' => $item->id], ['query' => ['disable' => 1]]),
                    'attributes' => ['class' => ['op-ajax-confirm'], 'confirm-message' => $this->t('Are you sure you want to disable this data?')]
                ];
            } else {
                $links['enable'] = [
                    'title' => $this->t('Enable'),
                    'url' => Url::fromRoute('informed_batch.product_correction_rule_disable', ['id' => $item->id], ['query' => ['disable' => 0]]),
                    'attributes' => ['class' => ['op-ajax-confirm'], 'confirm-message' => $this->t('Are you sure you want to enable this data?')]
                ];
                $links['edit'] = [
                    'title' => $this->t('Edit'),
                    'url' => Url::fromRoute('informed_batch.product_correction_rule_edit', ['id' => $item->id]),
                    'query' => \Drupal::destination()->getAsArray(),
                ];
                $links['delete'] = [
                    'title' => $this->t('Delete'),
                    'url' => Url::fromRoute('informed_batch.product_correction_rule_delete', ['id' => $item->id]),
                    'attributes' => ['class' => ['op-ajax-confirm'], 'confirm-message' => $this->t('Are you sure you want to delete this data? This operation cannot be undone!')]
                ];
            }
            $data['operations']['data'] = [
                '#type' => 'operations',
                '#links' => $links,
            ];

            $rows[] = ['data' => $data];
        }

        $build['rule_table'] = [
            '#theme' => 'table',
            '#header' => $header,
            '#empty' => $this->t('There are no items yet'),
            '#rows' => $rows,
            '#attributes' => ['class' => []]
        ];
        $build['pager'] = [
            '#type' => 'pager'
        ];
        $build['#attached']['library'][] = 'informed_batch/informed_batch_ajax_link';

        return $build;
    }

    /**
     * Delete a rule
     * @param $id
     * @return \Drupal\Core\Ajax\AjaxResponse
     */
    public function ajaxDestroy($id)
    {
        $response = new AjaxResponse();
        $output = ['code' => 1, 'msg' => $this->t('Success.')];

        try {
            \Drupal::database()->delete('informed_product_correction_rules')
                ->condition('id', $id)
                ->execute();
        } catch (\Exception $e) {
            $output = ['code' => 0, 'msg' => 'Delete failed: ' . $e->getMessage()];
        }

        $response->setData($output);

        return $response;
    }

    /**
     * Disable/Enable a rule
     * @param $id
     * @return \Drupal\Core\Ajax\AjaxResponse
     */
    public function ajaxDisable($id, Request $request)
    {
        $response = new AjaxResponse();
        $output = ['code' => 1, 'msg' => $this->t('Success.')];

        $disable = $request->get('disable');
        try {
            \Drupal::database()->update('informed_product_correction_rules')->fields(['effective' => $disable ? 0 : 1, 'changed' => \Drupal::time()->getRequestTime()])
                ->condition('id', $id)
                ->execute();
        } catch (\Exception $e) {
            $output = ['code' => 0, 'msg' => 'Operation failed: ' . $e->getMessage()];
        }

        $response->setData($output);

        return $response;
    }
}
