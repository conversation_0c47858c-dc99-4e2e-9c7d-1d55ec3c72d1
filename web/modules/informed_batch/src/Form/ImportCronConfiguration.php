<?php

namespace Drupal\informed_batch\Form;

use Drupal\Core\Form\ConfigFormBase;
use <PERSON>upal\Core\Form\FormStateInterface;

/**
 * Class ImportCronConfiguration.
 */
class ImportCronConfiguration extends ConfigFormBase
{

    /**
     * {@inheritdoc}
     */
    protected function getEditableConfigNames()
    {
        return [
            'informed_batch.import_cron_configuration',
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function getFormId()
    {
        return 'informed_batch_import_cron_configuration';
    }

    /**
     * {@inheritdoc}
     */
    public function buildForm(array $form, FormStateInterface $form_state)
    {
        $config = $this->config('informed_batch.import_cron_configuration');

        $form['notification'] = [
            '#type' => 'details',
            '#title' => $this->t('Notification'),
            '#open' => TRUE,
            '#weight' => 0,
        ];
        $form['notification']['notification_email'] = [
            '#type' => 'textfield',
            '#title' => $this->t('Email address'),
            '#default_value' => $config->get('notification_email'),
        ];

        $form['ftp'] = [
            '#type' => 'details',
            '#title' => $this->t('Ftp'),
            '#open' => TRUE,
            '#weight' => 1,
        ];
        $form['ftp']['ftp_server'] = [
            '#type' => 'textfield',
            '#title' => $this->t('Server'),
            '#required' => true,
            '#default_value' => $config->get('ftp_server'),
        ];
        $form['ftp']['ftp_username'] = [
            '#type' => 'textfield',
            '#title' => $this->t('Username'),
            '#required' => true,
            '#default_value' => $config->get('ftp_username'),
        ];
        $form['ftp']['ftp_password'] = [
            '#type' => 'textfield',
            '#title' => $this->t('Password'),
            '#required' => true,
        ];
        $form['ftp']['ftp_directory'] = [
            '#type' => 'textfield',
            '#title' => $this->t('Directory'),
            '#default_value' => $config->get('ftp_directory'),
        ];

        return parent::buildForm($form, $form_state);
    }

    /**
     * {@inheritdoc}
     */
    public function submitForm(array &$form, FormStateInterface $form_state)
    {
        parent::submitForm($form, $form_state);

        $this->config('informed_batch.import_cron_configuration')
            ->set('notification_email', $form_state->getValue('notification_email'))
            ->set('ftp_username', $form_state->getValue('ftp_username'))
            ->set('ftp_password', $form_state->getValue('ftp_password'))
            ->set('ftp_server', $form_state->getValue('ftp_server'))
            ->set('ftp_directory', $form_state->getValue('ftp_directory'))
            ->save();
    }
}
