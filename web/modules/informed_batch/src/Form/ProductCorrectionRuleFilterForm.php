<?php

namespace Drupal\informed_batch\Form;

use <PERSON>upal\Core\Form\FormBase;
use <PERSON>upal\Core\Form\FormStateInterface;
use Drupal\Core\Url;

class ProductCorrectionRuleFilterForm extends FormBase
{

    /**
     * {@inheritdoc}
     */
    public function getFormId()
    {
        return 'informed_batch_product_correction_rule_filter';
    }

    /**
     * {@inheritdoc}
     */
    public function buildForm(array $form, FormStateInterface $form_state)
    {
        $form['keywords'] = [
            '#type' => 'textfield',
            '#title' => $this->t('Keywords'),
            '#default_value' => $this->getRequest()->get('keywords')
        ];

        $form['actions'] = [
            '#type' => 'actions',
        ];
        $form['actions']['search'] = [
            '#type' => 'submit',
            '#value' => 'Search',
            '#button_type' => 'primary',
        ];
        $form['actions']['create'] = [
            '#type' => 'markup',
            '#markup' => t('<a href="@add-url">Add a new rule</a>', [
                '@add-url' => Url::fromRoute('informed_batch.product_correction_rule_create', [], ['query' => \Drupal::destination()->getAsArray()])->toString()
            ]),
        ];

        return $form;
    }

    /**
     * {@inheritdoc}
     */
    public function validateForm(array &$form, FormStateInterface $form_state)
    {
        parent::validateForm($form, $form_state);
    }

    /**
     * {@inheritdoc}
     */
    public function submitForm(array &$form, FormStateInterface $form_state)
    {
        $keywords = $form_state->getValue('keywords');
        $redirectOptions = ['query' => ['keywords' => $keywords]];
        $form_state->setRedirect($this->getRouteMatch()->getRouteName(), [], $redirectOptions);
    }
}
