<?php

namespace Drupal\informed_batch\Form;

use Drupal\Core\Form\FormBase;
use Drupal\Core\Form\FormStateInterface;

class ProductCorrectionRuleForm extends FormBase
{

    /**
     * {@inheritdoc}
     */
    public function getFormId()
    {
        return 'informed_batch_product_correction_rule';
    }

    /**
     * {@inheritdoc}
     */
    public function buildForm(array $form, FormStateInterface $form_state, int $id = 0)
    {
        $rule = $id ? \Drupal::database()->select('informed_product_correction_rules', 'r')->fields('r')->condition('id', $id)->execute()->fetchObject() : null;
        $form['#correction_rule'] = $rule;

        $form['original_manufacturer'] = [
            '#type' => 'textfield',
            '#title' => $this->t('Original Manufacturer'),
            '#required' => true,
            '#default_value' => $rule ? $rule->original_manufacturer : ''
        ];
        $form['original_product'] = [
            '#type' => 'textfield',
            '#title' => $this->t('Original Product'),
            '#required' => true,
            '#default_value' => $rule ? $rule->original_product : ''
        ];
        $form['original_flavour'] = [
            '#type' => 'textfield',
            '#title' => $this->t('Original Flavour'),
            '#required' => true,
            '#default_value' => $rule ? $rule->original_flavour : ''
        ];
        $form['correct_manufacturer'] = [
            '#type' => 'textfield',
            '#title' => $this->t('Correct Manufacturer'),
            '#required' => true,
            '#default_value' => $rule ? $rule->correct_manufacturer : ''
        ];
        $form['correct_product'] = [
            '#type' => 'textfield',
            '#title' => $this->t('Correct Product'),
            '#required' => true,
            '#default_value' => $rule ? $rule->correct_product : ''
        ];
        $form['correct_flavour'] = [
            '#type' => 'textfield',
            '#title' => $this->t('Correct Flavour'),
            '#required' => true,
            '#default_value' => $rule ? $rule->correct_flavour : ''
        ];

        $form['actions'] = [
            '#type' => 'actions',
        ];
        $form['actions']['submit'] = [
            '#type' => 'submit',
            '#value' => $this->t('Save'),
            '#button_type' => 'primary',
        ];
        $form['actions']['cancel'] = [
            '#type' => 'button',
            '#value' => 'Cancel',
            '#executes_submit_callback' => FALSE,
            '#attributes' => ['onclick' => "history.go(-1); return false;", 'class' => ['cancel-btn']],
        ];

        return $form;
    }

    /**
     * {@inheritdoc}
     */
    public function validateForm(array &$form, FormStateInterface $form_state)
    {
        parent::validateForm($form, $form_state);

        $rule = $form['#correction_rule'];
        $manufacturer = trim($form_state->getValue('original_manufacturer'));
        $product = trim($form_state->getValue('original_product'));
        $flavour = trim($form_state->getValue('original_flavour'));
        $exist_rule = \Drupal::database()->select('informed_product_correction_rules', 'r')->fields('r')
            ->condition('original_manufacturer', $manufacturer)->condition('original_product', $product)->condition('original_flavour', $flavour)->execute()->fetchObject();
        if ((empty($rule) && $exist_rule) || ($exist_rule && $rule && $exist_rule->id != $rule->id)) {
            $form_state->setErrorByName('original_manufacturer', $this->t('Rule already exists.'));
        }
    }

    /**
     * {@inheritdoc}
     */
    public function submitForm(array &$form, FormStateInterface $form_state)
    {
        $rule = $form['#correction_rule'];
        $data = $form_state->getUserInput();

        $fields = [
            'original_manufacturer' => trim($data['original_manufacturer']),
            'original_product' => trim($data['original_product']),
            'original_flavour' => trim($data['original_flavour']),
            'correct_manufacturer' => trim($data['correct_manufacturer']),
            'correct_product' => trim($data['correct_product']),
            'correct_flavour' => trim($data['correct_flavour']),
        ];
        if ($rule) {
            \Drupal::database()->update('informed_product_correction_rules')->fields($fields + ['changed' => \Drupal::time()->getRequestTime()])->condition('id', $rule->id)->execute();
            \Drupal::messenger()->addStatus($this->t('The rule has been updated.'));
        } else {
            \Drupal::database()->insert('informed_product_correction_rules')->fields($fields + ['effective' => 1, 'created' => \Drupal::time()->getRequestTime(), 'changed' => \Drupal::time()->getRequestTime()])->execute();
            \Drupal::messenger()->addStatus($this->t('A rule has been created.'));
        }
    }
}
