<?php

namespace Drupal\informed_batch;

use <PERSON><PERSON>al\Core\Config\ConfigFactory;
use Drupal\Core\File\FileSystemInterface;
use Drupal\Core\Url;
use Drupal\informed_module\Form\BatchImporter;

class ImportManager
{

    protected $settings;

    protected $filesystem;

    /**
     * Constructs a ImportManager object.
     */
    public function __construct(ConfigFactory $config, FileSystemInterface $filesystem)
    {
        $this->settings = $config->get('informed_batch.import_cron_configuration');
        $this->filesystem = $filesystem;
    }

    public function importFromFtp()
    {
        set_time_limit(3600);
        $messages = [];
        $start_time = date('Y/m/d H:i:s');

        // download files
        $downloaded_files = [];
        try {
            $downloaded_files = $this->downloadFiles($messages);
            $messages[] = 'Downloaded ' . count($downloaded_files) . ' files from ftp.';
            \Drupal::logger('informed_batch')->info('Downloaded @count files from ftp.<br/>', ['@count' => count($downloaded_files)]);
        } catch (\Throwable $th) {
            $messages[] = "Failed to download files, error: {$th->getMessage()}";
            \Drupal::logger('informed_batch')->error('Failed to download files, error: @error<br/>', ['@error' => $th->getMessage()]);
        }

        // import files
        $failed_data = [];
        foreach ($downloaded_files as $file_path) {
            $name = basename($file_path, '.csv');
            try {
                $failed_rows = $this->importBatches($file_path, $messages);
                if ($failed_rows) {
                    $failed_data[$name] = $failed_rows;
                }
            } catch (\Throwable $th) {
                $messages[] = "Failed to import file {$file_path}, error: {$th->getMessage()}";
                \Drupal::logger('informed_batch')->error('Failed to import file @file, error: @error<br/>', ['@file' => $file_path, '@error' => $th->getMessage()]);
            }
        }

        // generate failed files
        $failed_files = [];
        try {
            $failed_files = $this->generateFailedFiles($failed_data);
        } catch (\Throwable $th) {
            $messages[] = "Failed to save rows to file, error: {$th->getMessage()}";
            \Drupal::logger('informed_batch')->notice('Failed to save rows to file, error: @error<br/>', ['@error' => $th->getMessage()]);
        }

        // send email
        $mail_to = $this->settings->get('notification_email');
        if ($mail_to) {
            $correction_rules = array_reverse(ProductCorrectionRule::getAllCorrectProducts(1));
            $mail_manager = \Drupal::service('plugin.manager.mail');
            $mail_params = [];
            $mail_params['subject'] = 'Batch import result ' . date('Y/m/d');
            $mail_params['body'][] = "<p>Execute start time: {$start_time}, end time: " . date('Y/m/d H:i:s') . '.</p>';
            $mail_params['body'][] = '<p>Results:</p>';
            if ($messages) {
                foreach ($messages as $message) {
                    $mail_params['body'][] = "<p>&nbsp;&nbsp;&nbsp;&nbsp;{$message}</p>";
                }
            }
            $mail_params['body'][] = '<p>You can click <a href="' . Url::fromRoute('entity.ultimate_cron_job.logs', ['ultimate_cron_job' => 'informed_batch_cron'], ['absolute' => true])->toString() . '" target="_blank">here</a> to view logs.<p>';
            $mail_params['body'][] = '<p>Product Corrections:</p>';
            $rule_table = '<table border="1" cellspacing="0" width="100%"><thead><tr><th>Original Manufacturer</th><th>Original Product</th><th>Original Flavour</th><th>Correct Manufacturer</th><th>Correct Product</th><th>Correct Flavour</th></tr></thead><tbody>';
            foreach ($correction_rules as $rule) {
                $rule_table .= "<tr><td>{$rule->original_manufacturer}</td><td>{$rule->original_product}</td><td>{$rule->original_flavour}</td><td>{$rule->correct_manufacturer}</td><td>{$rule->correct_product}</td><td>{$rule->correct_flavour}</td></tr>";
            }
            $rule_table .= '</tbody></table>';
            $mail_params['body'][] = $rule_table;
            foreach ($failed_files as $file_path) {
                $mail_params['attachments'][] = $file_path;
            }
            $mail_result = $mail_manager->mail('informed_batch', 'batch_imported', $mail_to, 'en', $mail_params);
            if (isset($mail_result['result']) && $mail_result['result']) {
                \Drupal::logger('informed_batch')->info('Mail sent to @email.', ['@email' => $mail_to]);
            }
        }
    }

    private function importBatches($file_path, &$messages)
    {
        $data = $this->getFileData($file_path);
        $data = $this->formBatchRecords($data);
        $failed_data = [];
        $succeed_cnt = 0;
        $skiped_cnt = 0;
        foreach ($data as $batch_item) {
            if (!BatchImporter::array_keys_exists(['title', 'field_brand', 'field_product', 'field_flavour', 'field_formulation', 'field_expiration', 'field_test_date'], $batch_item)) {
                $skiped_cnt++;
                continue;
            }
            // correct product
            $correction_rule = ProductCorrectionRule::getCorrectProduct($batch_item['field_brand'], $batch_item['field_product'], $batch_item['field_flavour']);
            if ($correction_rule) {
                $batch_item['field_brand'] = $correction_rule->correct_manufacturer;
                $batch_item['field_product'] = $correction_rule->correct_product;
                $batch_item['field_flavour'] = $correction_rule->correct_flavour;
            }
            try {
                BatchImporter::processBatchItem($batch_item);
                $succeed_cnt++;
            } catch (\Throwable $th) {
                $failed_data[] = $batch_item;
                \Drupal::logger('informed_batch')->error('Failed to process batch item @id, error: @error<br/>', ['@id' => $batch_item['ROW_ID'], '@error' => $th->getMessage()]);
            }
        }
        $messages[] = "Imported {$succeed_cnt} batchs from {$file_path}, failed " . count($failed_data) . ", skiped {$skiped_cnt}.";
        \Drupal::logger('informed_batch')->info('Imported @succeed_cnt batchs from @file, failed @failed_cnt, skiped @skiped_cnt.<br/>', ['@succeed_cnt' => $succeed_cnt, '@file' => $file_path, '@failed_cnt' => count($failed_data), '@skiped_cnt' => $skiped_cnt]);
        return $failed_data;
    }

    private function formBatchRecords($data)
    {
        $data_bundle = [];
        foreach ($data as $record) {
            $row = [];
            $row['ROW_ID'] = array_shift($record);
            if (isset($record[0])) {
                $row['field_brand'] = trim($record[0]);
            }
            if (isset($record[1])) {
                $row['field_product'] = trim($record[1]);
            }
            if (isset($record[2])) {
                $row['field_flavour'] = trim($record[2]);
            }
            if (isset($record[3])) {
                $row['field_formulation'] = trim($record[3]);
            }
            if (isset($record[4])) {
                $row['title'] = trim($record[4]);
            }
            if (isset($record[5])) {
                $row['field_expiration'] = trim($record[5]);
            }
            if (isset($record[6])) {
                $row['field_test_date'] = trim($record[6]);
            }
            //all fields from the record are converted into UTF-8 charset
            $data_bundle[] = $row;
        }
        array_shift($data_bundle);
        return $data_bundle;
    }

    private function getFileData($uri)
    {
        $ext = pathinfo($uri)['extension'] ?? false;
        $inputFileType = ucfirst($ext);
        // the different exts it will take
        //    $inputFileType = 'Xlsx';
        //    $inputFileType = 'Xls';
        //    $inputFileType = 'Xml';
        //    $inputFileType = 'Ods';
        //    $inputFileType = 'Slk';
        //    $inputFileType = 'Gnumeric';
        //    $inputFileType = 'Csv';
        $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader($inputFileType);
        $reader->setReadDataOnly(true);
        $reader->setReadEmptyCells(false);
        /**  Load $inputFileName to a Spreadsheet Object  **/
        $spreadsheet = $reader->load($uri);
        $worksheet = $spreadsheet->getActiveSheet();
        $data = $worksheet->toArray();
        $data = array_map('array_filter', $data);
        $data = array_filter($data);
        $data = array_values($data);
        $data = array_map('array_values', $data);
        // keep ID column
        // if ($data[0][0] == 'ID') {
        //   $data = array_map('self::array_remove_first', $data);
        // }
        return $data;
    }

    private function generateFailedFiles($data)
    {
        $failed_files = [];
        $file_dir = 'public://csv-ftp/' . date('Y/m');
        $this->filesystem->prepareDirectory($file_dir, FileSystemInterface::CREATE_DIRECTORY);
        foreach ($data as $name => $failed_rows) {
            $file_name = 'Failed-' . $name . '-' . date('YmdHis') . '.csv';
            $file_path = $this->filesystem->realpath("{$file_dir}/{$file_name}");

            $rows = [['ID', 'Manufacturer', 'Product', 'Flavour', 'Formulation', 'Batch ID', 'Batch Expiry', 'Test Date']];
            foreach ($failed_rows as $row) {
                $rows[] = [$row['ROW_ID'], $row['field_brand'], $row['field_product'], $row['field_flavour'], $row['field_formulation'], $row['title'], $row['field_expiration'], $row['field_test_date']];
            }
            $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
            $worksheet = $spreadsheet->getActiveSheet();
            $worksheet->fromArray($rows);
            $writer = new \PhpOffice\PhpSpreadsheet\Writer\Csv($spreadsheet);
            $writer->save($file_path);

            $failed_files[] = $file_path;
            \Drupal::logger('informed_batch')->info('Failed rows are saved in @file.<br/>', ['@file' => $file_path]);
        }
        return $failed_files;
    }

    /**
     * Download csv files from ftp
     */
    private function downloadFiles(&$messages)
    {
        $ftp_server = $this->settings->get('ftp_server');
        $ftp_username = $this->settings->get('ftp_username');
        $ftp_password = $this->settings->get('ftp_password');
        $ftp_directory = $this->settings->get('ftp_directory') ? $this->settings->get('ftp_directory') : '.';

        // set up a connection
        $ftp = ftp_connect($ftp_server);
        if (empty($ftp)) {
            throw new \Exception("Couldn't connect to {$ftp_server}.");
        }

        // login with username and password
        $login_result = @ftp_login($ftp, $ftp_username, $ftp_password);
        if (empty($login_result)) {
            ftp_close($ftp);
            throw new \Exception("Couldn't connect as {$ftp_username}.");
        }

        // turn on passive mode
        $pasv = ftp_pasv($ftp, true);
        if (empty($pasv)) {
            ftp_close($ftp);
            throw new \Exception("Couldn't turn on passive mode.");
        }

        // get csv files of the directory
        $remote_files = ftp_nlist($ftp, "{$ftp_directory}/*.csv");
        if ($remote_files === false) {
            ftp_close($ftp);
            throw new \Exception("Couldn't list files under {$ftp_directory}.");
        }

        // download csv files
        $csv_files = [];
        $file_dir = 'public://csv-ftp/' . date('Y/m');
        $this->filesystem->prepareDirectory($file_dir, FileSystemInterface::CREATE_DIRECTORY);
        foreach ($remote_files as $remote_file) {
            $local_file_name = basename($remote_file);
            $local_file = $this->filesystem->realpath("{$file_dir}/{$local_file_name}");
            if (!ftp_get($ftp, $local_file, $remote_file)) {
                ftp_close($ftp);
                throw new \Exception("Failed to download remote file {$remote_file}.");
            }
            \Drupal::logger('informed_batch')->info('Downloaded @file from ftp.<br/>', ['@file' => $remote_file]);
            $csv_files[] = $local_file;
        }

        // delete csv files
        foreach ($remote_files as $remote_file) {
            if (!ftp_delete($ftp, $remote_file)) {
                $messages[] = "Failed to delete remote file {$remote_file}.";
                \Drupal::logger('informed_batch')->notice("Failed to delete remote file {$remote_file}.<br/>");
            }
        }

        // close the connection
        ftp_close($ftp);

        return $csv_files;
    }
}
