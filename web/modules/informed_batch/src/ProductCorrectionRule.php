<?php

namespace Drupal\informed_batch;

use PhpOffice\PhpSpreadsheet\Reader\Xlsx;

class ProductCorrectionRule
{

    public static function getAllCorrectProducts($effective = null)
    {
        $query = \Drupal::database()->select('informed_product_correction_rules', 't')->fields('t');
        if ($effective !== null) {
            $query->condition('t.effective', $effective);
        }
        $query->orderBy('id');
        $results = $query->execute()->fetchAll();
        return $results;
    }

    public static function getCorrectProduct($original_manufacturer, $original_product, $original_flavour)
    {
        static $st_product_correction_rules = [];
        if (empty($product_correction_rules)) {
            $results = self::getAllCorrectProducts(1);
            foreach ($results as $item) {
                $st_product_correction_rules[strtolower($item->original_manufacturer) . '_' . strtolower($item->original_product) . '_' . strtolower($item->original_flavour)] = $item;
            }
        }

        $key = strtolower($original_manufacturer) . '_' . strtolower($original_product) . '_' . strtolower($original_flavour);
        if (isset($st_product_correction_rules[$key]) && $st_product_correction_rules[$key]) {
            return $st_product_correction_rules[$key];
        }

        return null;
    }

    /**
     * Initialize reference data
     */
    public function initFromFile()
    {
        $reader = new Xlsx();
        $reader->setReadDataOnly(true);

        $file_path = \Drupal::service('extension.list.module')->getPath('informed_batch') . DIRECTORY_SEPARATOR . 'file' . DIRECTORY_SEPARATOR . 'Product Corrections.xlsx';
        $spreadsheet = $reader->load($file_path);
        if (\Drupal::config('system.site')->get('name') == 'Informed Sport') {
            $worksheet = $spreadsheet->getSheetByName('SPORT');
        } else {
            $worksheet = $spreadsheet->getSheetByName('CHOICE');
        }

        for ($row = 2; $row <= $worksheet->getHighestRow('A'); $row++) {
            $original_manufacturer = trim($worksheet->getCellByColumnAndRow(2, $row)->getValue());
            $original_product = trim($worksheet->getCellByColumnAndRow(3, $row)->getValue());
            $original_flavour = trim($worksheet->getCellByColumnAndRow(4, $row)->getValue());
            if (empty($original_manufacturer) || empty($original_product) || empty($original_flavour)) continue;

            \Drupal::database()->insert('informed_product_correction_rules')->fields([
                'original_manufacturer' => $original_manufacturer,
                'original_product' => $original_product,
                'original_flavour' => $original_flavour,
                'correct_manufacturer' => trim($worksheet->getCellByColumnAndRow(5, $row)->getValue()),
                'correct_product' => trim($worksheet->getCellByColumnAndRow(6, $row)->getValue()),
                'correct_flavour' => trim($worksheet->getCellByColumnAndRow(7, $row)->getValue()),
                'effective' => 1,
                'created' => \Drupal::time()->getRequestTime(),
                'changed' => \Drupal::time()->getRequestTime()
            ])->execute();
        }
    }
}
