<?php

function edit_entities($item_chunks, $total_count, $class, $method, &$context){

   if(!isset($context['sandbox']['items'])){
      $context['sandbox']['items'] = $item_chunks;
   }

   if(!isset($context['sandbox']['start'])){
    $context['sandbox']['start'] = 0;
    }


   if(isset($context['sandbox']['end'])){
      $context['sandbox']['start'] = $context['sandbox']['end'];
    }



  $item_chunk = array_pop($context['sandbox']['items']);

  if(!isset($context['sandbox']['end'])){
    $context['sandbox']['end'] = count($item_chunk);
  }
   foreach ($item_chunk as $item) {
     call_user_func_array([$class, $method], [$item]);
     $context['sandbox']['end']++;
   }

  $context['message'] = t('Running Batches @start - @end out of @total', [
    '@start' => $context['sandbox']['start'],
    '@end' => $context['sandbox']['end'],
    '@total' => $total_count,
  ]);

  if ($context['sandbox']['items']){
      $context['finished'] = 0;
   }
}

function delete_nodes($item_chunks, $total_count, &$context){

  if(!isset($context['sandbox']['items'])){
    $context['sandbox']['items'] = $item_chunks;
  }

  if(!isset($context['sandbox']['start'])){
    $context['sandbox']['start'] = 0;
  }


  if(isset($context['sandbox']['end'])){
    $context['sandbox']['start'] = $context['sandbox']['end'];
  }



  $item_chunk = array_pop($context['sandbox']['items']);

  if(!isset($context['sandbox']['end'])){
    $context['sandbox']['end'] = count($item_chunk);
  }
  $storage = \Drupal::entityTypeManager()->getStorage('node');
  $nodes = $storage->loadMultiple($item_chunk);
  $storage->delete($nodes);
  $context['sandbox']['end'] = $context['sandbox']['end'] + count($nodes);

  $context['message'] = t('Running Batches @start - @end out of @total', [
    '@start' => $context['sandbox']['start'],
    '@end' => $context['sandbox']['end'],
    '@total' => $total_count,
  ]);

  if ($context['sandbox']['items']){
    $context['finished'] = 0;
  }
}




function update_finished($success, $results, $operations) {
   // The 'success' parameter means no fatal PHP errors were detected. All
   // other error management should be handled using 'results'.
   if ($success) {
     $message = \Drupal::translation()
       ->formatPlural(count($results), 'One batch processed.', '@count posts processed.');
   }
   else {
     $message = t('Finished with an error.');
   }
  \Drupal::service('messenger')->addMessage($message);
   // Providing data for the redirected page is done through $_SESSION.
   foreach ($results as $result) {
     $items[] = t('Loaded Batch %title.', array(
       '%title' => $result,
     ));
   }
   $_SESSION['batch_results'] = $items;
}
