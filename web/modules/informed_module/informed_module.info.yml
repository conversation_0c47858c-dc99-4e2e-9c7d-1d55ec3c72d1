name: 'informed_module'
type: module
description: 'A place to hold custom logic and functionality.'
core_version_requirement: ^8 || ^9 || ^10
package: 'Custom'
dependencies:
  - csp:csp
  - entity_browser
  - entity_browser_block
  - blazy
  - block_list_override
  - entityqueue
  - twig_tweak
  - slick
  - slick_views
  - slick_lightbox
  - elevatezoomplus
  - better_exposed_filters
  - menu_item_fields
  - colorbutton
  - unique_entity_title
  - wkhtmltopdf
  - language
  - search_api
  - content_translation
