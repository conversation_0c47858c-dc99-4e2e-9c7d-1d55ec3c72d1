one_column:
  label: 1 Column
  category: Informed Layouts
  class: '\Drupal\informed_module\Plugin\Layout\FoundationLayout'
  template: layouts/one-column
  regions:
    column_one:
      label: Column 1
  icon_map:
    - [square_one]
six_column:
  label: 6 Column
  category: Informed Layouts
  class: '\Drupal\informed_module\Plugin\Layout\FoundationLayout'
  template: layouts/six-column
  regions:
    column_one:
      label: Column 1
    column_two:
      label: Column 2
    column_three:
      label: Column 3
    column_four:
      label: Column 4
    column_five:
      label: Column 5
    column_six:
      label: Column 6
  icon_map:
    - [square_one, square_two, square_three, square_four, square_five, square_six]
five_column:
  label: 5 Column
  category: Informed Layouts
  class: '\Drupal\informed_module\Plugin\Layout\FoundationLayout'
  template: layouts/five-column
  regions:
    column_one:
      label: Column 1
    column_two:
      label: Column 2
    column_three:
      label: Column 3
    column_four:
      label: Column 4
    column_five:
      label: Column 5
  icon_map:
    - [square_one, square_two, square_three, square_four, square_five]
four_column:
  label: 4 Column
  category: Informed Layouts
  class: '\Drupal\informed_module\Plugin\Layout\FoundationLayout'
  template: layouts/four-column
  regions:
    column_one:
      label: Column 1
    column_two:
      label: Column 2
    column_three:
      label: Column 3
    column_four:
      label: Column 4
  icon_map:
    - [square_one, square_two, square_three, square_four]
three_column:
  label: 3 Column
  category: Informed Layouts
  class: '\Drupal\informed_module\Plugin\Layout\FoundationLayout'
  template: layouts/three-column
  regions:
    column_one:
      label: Column 1
    column_two:
      label: Column 2
    column_three:
      label: Column 3
  icon_map:
    - [square_one, square_two, square_three]
two_column:
  label: 2 Column
  category: Informed Layouts
  class: '\Drupal\informed_module\Plugin\Layout\FoundationLayout'
  template: layouts/two-column
  regions:
    column_one:
      label: Column 1
    column_two:
      label: Column 2
  icon_map:
    - [square_one, square_two]
global_layout:
  label: Global Layout
  category: Informed Layouts
  class: '\Drupal\informed_module\Plugin\Layout\FoundationLayout'
  template: layouts/global-layout
  icon_map:
    - [square_one]
three_nine_column:
  label: Three Nine Column (Left Sidebar)
  category: Informed Layouts
  class: '\Drupal\informed_module\Plugin\Layout\FoundationLayout'
  template: layouts/three-nine-column
  regions:
    column_three:
      label: Column 3
    column_nine:
      label: Column 9
  icon_map:
    - [square_one, rectangle_horizontal, rectangle_horizontal, rectangle_horizontal]
nine_three_column:
  label: Nine Three Column (Right Sidebar)
  category: Informed Layouts
  class: '\Drupal\informed_module\Plugin\Layout\FoundationLayout'
  template: layouts/nine-three-column
  regions:
    column_nine:
      label: Column 9
    column_three:
      label: Column 3
  icon_map:
    - [ rectangle_horizontal, rectangle_horizontal, rectangle_horizontal, square_one ]
two_ten_column:
    label: Two Ten Column
    category: Informed Layouts
    class: '\Drupal\informed_module\Plugin\Layout\FoundationLayout'
    template: layouts/two-ten-column
    regions:
        column_two:
            label: Column 2
        column_ten:
            label: Column 10
    icon_map:
        - [square_one, rectangle_horizontal, rectangle_horizontal, rectangle_horizontal, rectangle_horizontal, rectangle_horizontal, rectangle_horizontal]
one_eleven_column:
  label: One Eleven Column
  category: Informed Layouts
  class: '\Drupal\informed_module\Plugin\Layout\FoundationLayout'
  template: layouts/one-eleven-column
  regions:
    column_one:
      label: Column 1
    column_eleven:
      label: Column 11
  icon_map:
    - [square_one, rectangle_horizontal, rectangle_horizontal, rectangle_horizontal, rectangle_horizontal, rectangle_horizontal, rectangle_horizontal]
mobile_eight_four_column:
  label: Mobile Eight Four Column
  description: "This template is geared toward a mobile screen size and therefore the columns are set with small for their initial cell sizes and its not really meant to be overridden."
  category: Informed Layouts
  class: '\Drupal\informed_module\Plugin\Layout\FoundationLayout'
  template: layouts/mobile-eight-four-column
  regions:
    column_eight:
      label: Column 8
    column_four:
      label: Column 4
  icon_map:
    - [rectangle_horizontal, rectangle_horizontal, square_one]



