informed_module.admin_index:
  title: Informed Settings
  description: 'Configuring Settings.'
  parent: system.admin
  route_name: informed_module.admin_index
informed_module.color_configuration_form:
  title: 'Color Configuration'
  route_name: informed_module.color_configuration_form
  description: 'A place to assign the colors to be usable by the theme'
  parent: informed_module.admin_index
  weight: 99
informed_module.batch_importer:
  title: 'Batch Importer'
  route_name: informed_module.batch_importer
  description: 'A place to upload a csv to update batch info.'
  parent: informed_module.admin_index
  weight: 99
informed_module.term_search_path:
  title: 'Term Search Path'
  route_name: informed_module.term_search_path
  description: 'Choose taxonomies to change the output of the view link to a search path with a query string.'
  parent: informed_module.admin_index
  weight: 99
informed_module.certified_product_list_generation:
  title: 'Certified Product List Generation'
  route_name: informed_module.certified_product_list_generation
  description: 'A place to set a page to generate a pdf with.'
  parent: informed_module.admin_index
  weight: 99
informed_module.product_upc_import:
  title: 'Product Upc Import'
  route_name: informed_module.product_upc_import
  description: 'Upload a csv file with upc codes to attach to products'
  parent: informed_module.admin_index
  weight: 99
