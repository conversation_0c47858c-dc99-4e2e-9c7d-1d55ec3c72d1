<?php

/**
 * @file
 * Contains informed.module.
 */

use <PERSON><PERSON>al\Component\Utility\Html;
use Drupal\Component\Utility\Xss;
use <PERSON><PERSON>al\Core\Routing\RouteMatchInterface;
use <PERSON><PERSON>al\Core\Form\FormStateInterface;
use <PERSON><PERSON><PERSON>\block\BlockInterface;
use <PERSON><PERSON>al\informed_module\Modules\BlockFormStyles;
use <PERSON><PERSON>al\search_api\Entity\Index;
use Drupal\views\Plugin\views\query\QueryPluginBase;
use Drupal\views\ViewExecutable;
use Drupal\views\Views;
use Drush\Drush;

// @todo require twig tweak
function informed_module_preprocess_item_list__layouts(array &$vars){
  unset($vars['items']['layout_fourcol_section']);
  unset($vars['items']['layout_onecol']);
  unset($vars['items']['layout_threecol_section']);
  unset($vars['items']['layout_twocol_section']);
}


/**
 * Implements hook_ENTITY_TYPE_presave().
 */
function informed_module_block_presave(BlockInterface $entity) {
  if (empty($entity->getThirdPartySetting('informed_module', 'informed_block_styles'))) {
    $entity->unsetThirdPartySetting('informed_module', 'informed_block_styles');
  }
}

/**
 * Implements hook_form_FORM_ID_alter().
 */
function informed_module_form_alter(&$form, FormStateInterface $form_state, $form_id) {
  if (strpos($form_id, 'layout_builder_form')){
    $form['hide_layout_builder_ui'] = [
      '#type' => 'checkbox',
      '#title' => 'Hide Layout Builder UI',
      '#description' => 'Hiding the layout builder ui allows you to get a better idea of how your layout looks without saving.',
      '#default_value' => 0,
    ];
  }
}

function informed_module_form_views_exposed_form_alter(&$form, \Drupal\Core\Form\FormStateInterface $form_state, $form_id) {
  $tids = [
    'brand',
    'category',
    'formulation',
    'goal',
    'region',
  ];
  $user_input = $form_state->getUserInput();
  if (empty(\Drupal::request()->get('view_type'))) {
	  $product_search_view = 'grid_layout';
  } else {
	  $product_search_view = Xss::filter(\Drupal::request()->get('view_type'));
	  $product_search_view = Html::escape($product_search_view);
	  $product_search_view = empty($product_search_view) ? 'grid_layout' : $product_search_view;
  }

  // @todo setUserInput to get the view_type var intact
  $form['view_type'] = [
    '#type' => 'hidden',
    '#value' =>  $product_search_view,
  ];
  foreach ($tids as $tid){
    _remove_unused_terms($form, $tid);
    if (!empty($user_input[$tid])){
      _prepend_selected_items($form, $tid, $user_input[$tid]);
      _generate_clear_button($form, $tid);
    }
  }

  if ( $form['#id'] === "views-exposed-form-search-product-search"){
    $form['#attached']['library'][] = 'informed_module/informed_module_auto_submit';
    $form['#attached']['library'][] = 'informed_module/informed_module_view_more_toggle';
  }
}

function _generate_clear_button (&$form, $tid){
	$exists = $form[$tid] ?? false;
	if ($exists){
		$request = \Drupal::request();
		$args = $request->query->all();
		$url = \Drupal\Core\Url::fromRoute('<current>');
		if ($url->getInternalPath() === 'views/ajax') {
			$previousUrl = \Drupal::request()->server->get('HTTP_REFERER');
			$fake_request = $request::create($previousUrl);
			$url = \Drupal::service('path.validator')->getUrlIfValid($fake_request->getRequestUri());
		}
		unset($args[$tid]);

		if (isset($args['ajax_page_state']))
			unset($args['ajax_page_state']);

		$url->setOptions(array('query' => $args));
		$url = $url->toString();
		$form[$tid]['clear_link'] = [
			'#markup' => '<div class="ultra-small-bottom-margin"><a class="clear-button anchor-color-black display-flex align-items-center" href="'.$url.'"><div class="informed-stacked-icons icon-size-16 ultra-small-right-margin"><i class="informed-icon informed-circle color-primary"></i><i class="informed-icon informed-caret-left color-black"></i></div> clear</a></div>',
		];
	}
}

function _prepend_selected_items (&$form, $tid, $selected_items){
	$exists = $form[$tid] ?? false;
	if ($selected_items && $exists && isset($exists['#options'])){
		if(is_array($selected_items)) {
			foreach ($selected_items as $key => $item) {
				$form[$tid]['#options'] = [$key => $form[$tid]['#options'][$key]] + $form[$tid]['#options'];
			}
		} else {
			$form[$tid]['#options'] = [$selected_items => $form[$tid]['#options'][$selected_items]] + $form[$tid]['#options'];
		}
	}
}

function _remove_unused_terms (&$form, $tid){
    $exists = $form[$tid] ?? false;
    if ($exists && isset($exists['#options'])){
      foreach ($form[$tid]['#options'] as $key => $value) {
        // Limit to numeric keys to exclude the 'All' option.
          if (is_numeric($key)) {
            // Start database query.
            $query = \Drupal::database()->select('taxonomy_index', 'ti');
            $query->fields('ti', ['nid']);
            $query->condition('ti.tid', $key);
            $nodes = $query->execute()->fetchAssoc();
            // Remove option if term has no instance.
            if (!$nodes) {
              unset($form[$tid]['#options'][$key]);
            }
          }
      }
    }
}

/**
 * Implements hook_form_FORM_ID_alter().
 */
function informed_module_form_block_form_alter(&$form, FormStateInterface $form_state, $form_id) {
    /** @var \Drupal\block\BlockInterface $block */
    $block = $form_state->getFormObject()->getEntity();
    if (empty($form['settings']['third_party_settings'])){
      $form['settings']['#weight'] = 0;
      $form['visibility']['#weight'] = 10;
      $form['region']['#weight'] = 20;
      // This will automatically be saved in the third party settings.
      $form['third_party_settings']['#tree'] = TRUE;
      $config = $block->getThirdPartySettings('informed_module');
      $form['third_party_settings']['informed_module'] = BlockFormStyles::styleOptions($config);
      $form['third_party_settings']['#weight'] = 5;
    }
}
//
function informed_module_editor_js_settings_alter(array &$settings) {
    foreach (array_keys($settings['editor']['formats']) as $text_format_id) {
        $colors = $settings['editor']['formats'][$text_format_id]['editorSettings']['colorButton_colors'] ?? false;
        if ($colors){
          $theme_default_colors = [];
          $color_config = \Drupal::config('informed_module.color_configuration')->get();
          foreach ($color_config as $setting_name => $color) {
            if (strpos($setting_name, 'general_') !== false){
              $theme_default_colors[] = str_replace('#', '', $color);
            }
          }
          $colors = explode(',', $colors);
          $colors = array_unique(array_merge($theme_default_colors, $colors));
          $colors = $colors ? implode(',', $colors) : '';
          $settings['editor']['formats'][$text_format_id]['editorSettings']['colorButton_colors'] = $colors;
        }
    }
}


/**
 * Implements hook_help().
 */
function informed_module_help($route_name, RouteMatchInterface $route_match) {
  switch ($route_name) {
    // Main module help for the informed module.
    case 'help.page.informed_module':
      $renderable = [
        '#theme' => 'help',
      ];
      $rendered = \Drupal::service('renderer')->render($renderable);
      return $rendered;

    default:
  }
}

function informed_module_theme($existing,$type,$theme,$path){
  //module_load_include('inc', 'informed_module', 'theme.vars');
  \Drupal::moduleHandler()->loadInclude('informed_module', 'inc', 'theme.vars');
  return theme_vars();
}

/**
 * Implements hook_page_attachments_alter().
 */
function informed_module_page_attachments_alter(array &$page) {
  if (!\Drupal::service('router.admin_context')->isAdminRoute()) {
    $page['#attached']['library'][] = 'informed_module/informed_toolbar';
//    $page['#attached']['library'][] = 'csp/nonce';
//
    $config = \Drupal::config('informed_module.color_configuration');
    $stylesheet_url = $config->get('stylesheet_url');
//
//    if (\Drupal::moduleHandler()->moduleExists('csp')) {
//      $nonce_service = \Drupal::service('csp.nonce');
//      $placeholderKey = Drupal::service('csp.nonce_builder')->getPlaceholderKey();
//      $nonce_value = $placeholderKey;
//    }

    // Add nonce to stylesheet link
    $page['#attached']['html_head_link'][] = [
      [
        'href' => $stylesheet_url,
        'rel' => 'stylesheet',
        'media' => 'all',
        //'nonce' => $nonce_value?? '',
      ],
      'informed_module'
    ];
  }
}

function informed_module_cron() {

  // delete batches when their field expiration date hits
  $query = \Drupal::entityQuery('node');
  $query->accessCheck(true);
  $query->condition('field_expiration', date("Y-m-d"), '<');
  $query->condition('type', 'batch');
  $batch_ids = $query->execute();
  $batches = \Drupal::entityTypeManager()->getStorage('node')->loadMultiple($batch_ids);
  foreach ($batches as $batch) {
    $batch->delete();
  }

	$date = new DateTime('NOW', new DateTimeZone('America/New_York'));
	if( $date->format( 'H') == 0 && $date->format( 'i') <= 5) {
		// generate the pdf a page
		$config = \Drupal::config('informed_module.pdf_page_generation');
		$host = \Drupal::request()->getScheme() . '://' . \Drupal::request()->getHost();
		if ($config->get('path')) {
			$pdf_generate = \Drupal::service('informed_module.generate_pdf')->manuallyGeneratePdf($config->get('path'));
		}
	}

	$moduleHandler = \Drupal::service('module_handler');
	if ($moduleHandler->moduleExists('feeds')) {
		//run feed import, if module enabled
		\Drupal::logger('informed_module')->notice('Attempting Drush import');
		$alias_manager = Drush::service('site.alias.manager');
		Drush::drush($alias_manager->getSelf(), 'feeds-import-all')->run();
	}
 }

function informed_module_views_query_alter(ViewExecutable $view, QueryPluginBase $query) {
  $search_string = $view->getExposedInput()['search'] ?? false;
  $view_display_machine_name = $view->getDisplay()->display['id'] ?? false;
  if ($search_string && $view_display_machine_name === 'product_search'){
    $search_string = Xss::filter($search_string);
    $search_string = preg_replace('/[\x00-\x1F\x80-\xFF]/', '', $search_string);
	$batch_query = \Drupal::entityQuery('node')
	  ->condition('type', 'batch')
	  ->condition('title', $search_string)
	  ->condition('status', true)
	  ->accessCheck(TRUE);
	$batch_results = $batch_query->execute();
	$batches = $batch_results ? \Drupal\node\Entity\Node::loadMultiple($batch_results) : false;
    if ($batches){
      $product_ids = [];
      foreach ($batches as $batch){
        $product_ids[] = $batch->field_product->target_id;
      }

      if ($product_ids){
        $search_api_index = Index::load('products');
        if ($search_api_index){
          $new_query = $search_api_index->query();
          $new_query->addCondition('nid', $product_ids, 'IN');
          //        $conditions = $query->createConditionGroup('OR');
          $query->setSearchApiQuery($new_query);
        }
      }
    }
  }
}

/**
 * Pardot integration
 * Loads submission values and inserts them into an iframe to support Pardot tracking
 **/
function informed_module_preprocess_confirmation(&$vars) {
  if($vars['webform']->id() == 'contact') {
    $request = \Drupal::request()->query;
    $vars['message']['markup'] = print_r($request);
    //$sid = $request->get('sid');
  }
}

/**
 * Implements hook_views_pre_view().
 */
//function informed_module_views_pre_view(ViewExecutable $view, $display_id, array &$args) {
//  $deleted_products = Views::getView('entity_delete_log');
//  $deleted_products->setDisplay('deleted_products');
//  // contextual relationship filter
//  $rendered_view = $deleted_products->render();
//
//  $results = $deleted_products->result; // deleted products rest API view display output from Deleted Entity Log view
//}
