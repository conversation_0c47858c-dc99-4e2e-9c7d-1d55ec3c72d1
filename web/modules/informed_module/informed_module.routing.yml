informed_module.admin_index:
  path: '/admin/config/informed_module/settings'
  defaults:
    _controller: '\Drupal\system\Controller\SystemController::systemAdminMenuBlockPage'
    _title: 'Informed Settings'
  requirements:
    _permission: 'access administration pages'
informed_module.color_configuration_form:
  path: '/admin/config/informed_module/colorconfiguration'
  defaults:
    _form: '\Drupal\informed_module\Form\ColorConfigurationForm'
    _title: 'ColorConfigurationForm'
  requirements:
    _permission: 'access administration pages'
  options:
    _admin_route: TRUE
informed_module.batch_importer:
  path: '/admin/config/informed_module/batch-importer'
  defaults:
    _form: '\Drupal\informed_module\Form\BatchImporter'
    _title: 'Batch Importer'
  requirements:
    _permission: 'access administration pages'
  options:
    _admin_route: TRUE
informed_module.classes.help:
  path: '/admin/help/informed_module/classes'
  defaults:
    _title: 'Classes Help'
  requirements:
    _permission: 'access administration pages'
  options:
    _admin_route: TRUE

informed_module.term_search_path:
  path: '/admin/config/informed_module/terms-search-path'
  defaults:
    _form: '\Drupal\informed_module\Form\TermSearchPath'
    _title: 'TermSearchPath'
  requirements:
    _permission: 'access administration pages'
  options:
    _admin_route: TRUE
informed_module.certified_product_list_generation:
  path: '/admin/config/informed_module/pdf_page_generation'
  defaults:
    _form: '\Drupal\informed_module\Form\PdfPageGeneration'
    _title: 'Pdf Page Generation'
  requirements:
    _permission: 'access administration pages'
  options:
    _admin_route: TRUE
informed_module.pdf_page_generation:
  path: '/admin/config/informed_module/pdf_page_generation'
  defaults:
    _form: '\Drupal\informed_module\Form\PdfPageGeneration'
    _title: 'Pdf Page Generation'
  requirements:
    _permission: 'access administration pages'
  options:
    _admin_route: TRUE
informed_module.product_upc_import:
  path: '/admin/config/informed_module/product_upc_import'
  defaults:
    _form: '\Drupal\informed_module\Form\ProductUpcImport'
    _title: 'Product Upc Import'
  requirements:
    _permission: 'access administration pages'
  options:
    _admin_route: TRUE
