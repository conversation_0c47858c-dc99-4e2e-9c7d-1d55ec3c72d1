/**
 * @file
 * Customizable Form behaviors.
 */

(function ($, window, Drupal) {

  'use strict';

  /**
   * Provides the summary information for the condition vertical tabs.
   *
   * @type {Drupal~behavior}
   *
   * @prop {Drupal~behaviorAttach} attach
   *   Attaches the behavior for the customizable forms.
   */
  Drupal.behaviors.informed_module_auto_submit = {
    attach: function (context, settings) {
      // .once('productListFilters')
      var form = $('#views-exposed-form-search-product-search');
      if (form.length > 0 && $('[id*="edit-submit-search"]').length > 0){
        $(once('informed_module_auto_submit', form.find('input, select').not('#edit-submit-search'))).each(function () {
          $(this).on('change', function() {
            if (Foundation.MediaQuery.current !== 'small' && Foundation.MediaQuery.current !== 'medium'){
              $('[id*="edit-submit-search"]').trigger('click');
            }
          });
        });
        $('[id*="edit-submit-search"]').click(()=>{
          $('body').removeClass('noscroll');
        });
      }
    },
  };
})(jQuery, window, Drupal);
