/**
 * @file
 * Customizable Form behaviors.
 */

(function ($, window, Drupal) {

  'use strict';

  /**
   * Provides the summary information for the condition vertical tabs.
   *
   * @type {Drupal~behavior}
   *
   * @prop {Drupal~behaviorAttach} attach
   *   Attaches the behavior for the customizable forms.
   */
  Drupal.behaviors.informed_module_view_more_toggle = {
    attach: function (context, settings) {
      // .once('productListFilters')
      // var form = $('#views-exposed-form-search-product-search');
      // if (form.length > 0){
      // var elem = new Foundation.Toggler(element, options);
      $(once('informed_module_view_more_toggle', '#views-exposed-form-search-product-search', context)).each(function (i, el) {
        const element = $(el).find('[data-toggle]');
        let toggle_els = [];
        element.each(function(i, el){
          toggle_els.push('#' + $(el).data('toggle').split(' ').join(', #'));
        });
        toggle_els.join(', ').split(',').forEach(function(item){
          var elem = new Foundation.Toggler($(item));
        });
      });
      // }
    },
  };
})(jQuery, window, Drupal);
