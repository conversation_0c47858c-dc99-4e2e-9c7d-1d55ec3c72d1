<?php

namespace Drupal\informed_module\Controller;
use <PERSON><PERSON><PERSON>\wkhtmltopdf\Controller\WkhtmltopdfController;

use Symfony\Component\HttpFoundation\RedirectResponse;
use Drupal\Core\Config\ConfigFactory;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\HttpFoundation\Request;
use Drupal\Core\File\FileSystemInterface;
use Symfony\Component\HttpFoundation\RequestStack;

/**
 * a little extention of the wkhtmltopdf controller to create a service for and hit to generate a single pdf.
 */
class InformedPdfController extends WkhtmltopdfController {
  /**
   * {@inheritdoc}
   */
  public function __construct(ConfigFactory $config, RequestStack $request, FileSystemInterface $filesystem) {
    parent::__construct($config ,$request->getCurrentRequest(), $filesystem);
  }

  /**
   * Generate pdf file.
   *
   * @return RedirectResponse
   *   Redirect
   */
  public function manuallyGeneratePdf($url) {
	  $binary = $this->settings->get('wkhtmltopdf_bin');
	  $path = $this->filesystem->realpath('public://wkhtmltopdf');
	  $parameters = '--javascript-delay 5000';
	  $parameters .= $this->settings->get('wkhtmltopdf_zoom') ? ' --zoom ' . $this->settings->get('wkhtmltopdf_zoom') : '';

	  $file_path = "public://wkhtmltopdf/" . urlencode($url) . '.pdf';
	  if (!$this->filesystem->prepareDirectory($path)) {
		  $this->filesystem->mkdir($path);
	  }

	  $host = \Drupal::request()->getScheme() . '://' . \Drupal::request()->getHost();

	  $filename = urlencode($url) . '.pdf';
	  $file_path_physical = $path . '/' . $filename;
	  $public_url = $host . '/' . $url;
	  $command = $binary . ' ' . $parameters . ' ' . $public_url . ' ' . $file_path_physical;

	  $lock = \Drupal::lock();
	  if ($lock->acquire(__FILE__) !== FALSE) {
		  shell_exec($command);
		  $lock->release(__FILE__);
	  }
	  else {
		  while ($lock->acquire(__FILE__) === FALSE) {
			  $lock->wait(__FILE__, 3);
		  }
		  if ($lock->acquire(__FILE__) !== FALSE) {
			  shell_exec($command);
			  $lock->release(__FILE__);
		  }
	  }

	  $file_url= \Drupal::service('file_url_generator')->generateAbsoluteString($file_path);
	  $fileSystem = \Drupal::service('file_system');
	  $uri = 'public://generated-pdf/';
	  $fileSystem->deleteRecursive($uri);
	  $fileSystem->prepareDirectory($uri, 777);
	  $filename = $fileSystem->createFilename('file.pdf', $uri);

	  $file = \Drupal::service('file.repository')->writeData(file_get_contents($file_path_physical), $filename, FileSystemInterface::EXISTS_REPLACE);

	  return new RedirectResponse($file_url);
  }

}
