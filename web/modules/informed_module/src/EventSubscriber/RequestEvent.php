<?php

namespace Drupal\informed_module\EventSubscriber;

use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpKernel\KernelEvents;

/**
 * Class QueryParamAlter.
 */
class RequestEvent implements EventSubscriberInterface {

  /**
   * {@inheritdoc}
   */
  public static function getSubscribedEvents() {
    $events[KernelEvents::REQUEST][] = ['QueryAlter'];

    return $events;
  }

  /**
   * This method is called when the KernelEvents::REQUEST is dispatched.
   *
   * @param \Symfony\Component\HttpKernel\Event\RequestEvent|null $event
   *   The dispatched event.
   */
  public function QueryAlter(\Symfony\Component\HttpKernel\Event\RequestEvent $event = NULL) {
    // i wonder if there is a better way to do this. like is this hurting performance?
    $request = $event->getRequest();
    $search = $request->query->get('search');
    if ($search){
      $request->query->set('search', strtolower($search));
    }
  }

}
