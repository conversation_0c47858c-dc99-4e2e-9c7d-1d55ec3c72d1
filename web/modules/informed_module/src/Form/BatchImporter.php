<?php

namespace Drupal\informed_module\Form;

use <PERSON><PERSON>al\Component\Utility\NestedArray;
use Drupal\Core\Database\Log;
use Drupal\Core\Entity\EntityTypeManager;
use Dr<PERSON>al\Core\File\FileSystemInterface;
use <PERSON><PERSON>al\Core\Form\ConfigFormBase;
use Drupal\Core\Form\FormStateInterface;
use Drupal\Core\Url;
use Drupal\node\Entity\Node;
use League\Csv\Reader;
use League\Csv\Statement;
use League\Csv\Writer;
use Symfony\Component\HttpFoundation\RedirectResponse;

/**
 * Class BatchImporter.
 */
class BatchImporter extends ConfigFormBase {

  /**
   * {@inheritdoc}
   */
  protected function getEditableConfigNames() {
    return [
      'informed_module.batch_importer',
    ];
  }

  /**
   * {@inheritdoc}
   */
  public function getFormId() {
    return 'batch_importer';
  }

  /**
   * {@inheritdoc}
   */
  public function buildForm(array $form, FormStateInterface $form_state) {
    $config = $this->config('informed_module.batch_importer');
    $form = parent::buildForm($form, $form_state);
    $query = \Drupal::entityQuery('node');
    $query->accessCheck(true);
    $query->condition('type', 'batch');
    $query->count();
    $batch_count = $query->execute();

    $query = \Drupal::entityQuery('node');
    $query->accessCheck(true);
    $query->condition('type', 'batch');
    $query->condition('field_unmatched', 1);
    $query->count();
    $unmatched_ids = $query->execute();

    $query = \Drupal::entityQuery('node');
    $query->accessCheck(true);
    $query->condition('type', 'batch');
    $query->condition('field_unmatched', 0);
    $query->count();
    $matched_ids = $query->execute();
    $form['notes']['#markup'] = "<p>Batch Count: $batch_count</p><p>Unmatched: $unmatched_ids</p><p>Matched: $matched_ids</p>";
//      '<p>
//          <strong>Notes:</strong> Date format should be "d-M-Y". Dates with "/" will be assumed to be american "m/d/Y" and will be automatically converted.
//        </p>
//        <p>
//            <strong>Columns supported (order is important):</strong>
//            <ul>
//                <li>Manufacturer</li>
//                <li>Product</li>
//                <li>Flavour</li>
//                <li>Formulation</li>
//                <li>Batch ID</li>
//                <li>Batch Expiry (will be checked for date format)</li>
//                <li>Test Date (will be checked for date format)</li>
//            </ul>
//        </p>';
//
    $form['unmerged_us_files'] = [
      '#type' => 'managed_file',
      '#title' => $this->t('Unmerged US Csv\'s'),
      '#description' => $this->t('Upload csv\'s to run thru "macro" script.'),
      '#multiple' => TRUE,
      '#upload_validators' => [
        'file_validate_extensions' => ['csv xls'],
        'file_validate_size' => [25600000],
      ],
      '#default_value' => $config->get('unmerged_us_files'),
      '#upload_location' => 'public://csv/',
    ];
//
    $form['unmerged_eu_files'] = [
      '#type' => 'managed_file',
      '#title' => $this->t('Unmerged EU Csv\'s'),
      '#description' => $this->t('Upload csv\'s to run thru "macro" script.'),
      '#multiple' => TRUE,
      '#upload_validators' => [
        'file_validate_extensions' => ['csv xls'],
        'file_validate_size' => [25600000],
      ],
      '#default_value' => $config->get('unmerged_eu_files'),
      '#upload_location' => 'public://csv/',
    ];

    $form['master_csv'] = [
      '#type' => 'managed_file',
      '#title' => $this->t('Master Product Csv'),
      '#description' => $this->t('Upload a csv with records that reflect the products on certified for this site. '),
      '#upload_validators' => [
        'file_validate_extensions' => ['csv xls'],
        'file_validate_size' => [25600000],
      ],
      '#default_value' => $config->get('master_csv'),
      '#upload_location' => 'public://csv/',
    ];
////
    if ($config->get('downloadable_file')){
      $downloadable_file =  \Drupal::entityTypeManager()->getStorage('file')->load($config->get('downloadable_file'));
      if ($downloadable_file){
        $uri = $downloadable_file->getFileUri();
        $created_time = $downloadable_file->getCreatedTime();
        $form['downloadable_file'] = [
          '#type' => 'html_tag',
          '#tag' => 'a',
          '#value' => 'Download Merged File',
          '#attributes' => [
            'href' => \Drupal::service('file_url_generator')->generateAbsoluteString($uri),
            'class' => 'button button--primary',
            'style' => 'margin-left: 0;',
          ],
        ];
        $form['details'] = [
          '#type' => 'html_tag',
          '#tag' => 'div',
          '#value' => 'File Created:'. date('d-M-Y',$created_time) .'',
          '#attributes' => [
            'class' => 'description',
          ],
        ];
      }
    }

    $form['csv'] = [
      '#type' => 'managed_file',
      '#title' => $this->t('Import Csv'),
      '#description' => $this->t('Upload a csv with final batch information.'),
      '#default_value' => $config->get('csv'),
      '#upload_validators' => [
        'file_validate_extensions' => ['csv'],
        'file_validate_size' => [25600000],
      ],
      '#upload_location' => 'public://csv/',
    ];

    $form['chuck_size'] = [
      '#type' => 'select',
      '#title' => $this->t('Batch Chunk Size'),
      '#description' => $this->t('Select the size of chunking you would like to use for batch imports.'),
      '#default_value' => $config->get('chuck_size'),
      '#options' => [
        25 => '25',
        50 => '50',
        100 => '100',
        150 => '150',
        200 => '200',
        250 => '250',
        500 => '500',
      ],
    ];

    $form['actions']['run_macro'] = [
      '#type' => 'submit',
      '#button_type' => "primary",
      '#value' => 'Run Macro',
      '#name' => 'run_macro',
      '#submit' => array([$this, 'RunMacro']),
    ];

    $form['actions']['import_batches'] = [
      '#type' => 'submit',
      '#button_type' => "primary",
      '#value' => 'Import Batches',
      '#name' => 'import_batches',
      '#submit' => array([$this, 'ImportBatches']),
      '#attributes' => [
        'style' => 'margin:0;',
      ],
    ];

    $form['actions']['prune_unmatched_batches'] = [
      '#type' => 'submit',
      '#button_type' => "primary",
      '#value' => 'Prune Unmatched Batches',
      '#name' => 'prune_unmatched_batches',
      '#submit' => array([$this, 'PruneUnmatchedBatches']),
      '#attributes' => [
        'style' => 'margin:0;',
      ],
    ];

    $form['actions']['delete_all_batches'] = [
      '#type' => 'submit',
      '#button_type' => "primary",
      '#value' => 'Delete All Batches',
      '#name' => 'delete_all_batches',
      '#submit' => array([$this, 'DeleteAllBatches']),
      '#attributes' => [
        'style' => 'margin:0;',
      ],
    ];



    return $form;
  }

  public function PruneUnmatchedBatches (){
    $config = $this->config('informed_module.batch_importer');
    $chunk_size = $config->get('chuck_size') ?? 50;
    $batch = [
      'title' => t('Prune Batches'),
      'operations' => [],
      'finished' => 'Batches Pruned',
      'file' => \Drupal::service('extension.path.resolver')->getPath('module', 'informed_module') . '/informed_module.entity_import.update.batch.inc',
    ];
    $query = \Drupal::entityQuery('node');
    $query->accessCheck(true);
    $query->condition('type', 'batch');
    $query->condition('field_unmatched', 1);
    $batch_ids = $query->execute();
    $item_chunks = array_chunk($batch_ids, $chunk_size, false);
    $batch['operations'][] = ['delete_nodes', [$item_chunks, count($batch_ids)]];
    batch_set($batch);
  }

  public function DeleteAllBatches (){
    $config = $this->config('informed_module.batch_importer');
    $chunk_size = $config->get('chuck_size') ?? 50;
    $batch = [
      'title' => t('Clearing All Batches'),
      'operations' => [],
      'finished' => 'Batches Cleared',
      'file' => \Drupal::service('extension.path.resolver')->getPath('module', 'informed_module') . '/informed_module.entity_import.update.batch.inc',
    ];
    $query = \Drupal::entityQuery('node');
    $query->accessCheck(true);
    $query->condition('type', 'batch');
    $batch_ids = $query->execute();
    $item_chunks = array_chunk($batch_ids, $chunk_size, false);
    $batch['operations'][] = ['delete_nodes', [$item_chunks, count($batch_ids)]];
    batch_set($batch);
  }

//  public static function DeleteBatch($id){
//    //@todo below is an example of how to delete more than one at a time
////    $storage = \Drupal::entityTypeManager()->getStorage('node');
////    foreach (array_chunk($nids, 50) as $chunk) {
////      $nodes = $storage->loadMultiple($chunk);
////      $storage->delete($nodes);
////    }
//    $batch = \Drupal::entityTypeManager()->getStorage('node')->load($id);
//    if ($batch){
//      $batch->delete();
//    }
//  }

  public function RunMacro (array &$form, FormStateInterface $form_state){
    $master_csv = \Drupal::entityTypeManager()->getStorage('file')->load($form_state->getValue('master_csv')[0]);
    $unmerged_eu_files =  \Drupal::entityTypeManager()->getStorage('file')->loadMultiple($form_state->getValue('unmerged_eu_files'));
    $unmerged_us_files =  \Drupal::entityTypeManager()->getStorage('file')->loadMultiple($form_state->getValue('unmerged_us_files'));
    $csv_group = array_merge($unmerged_eu_files, $unmerged_us_files);
    if ($master_csv && $csv_group){
      $master_csv_uri = $master_csv->getFileUri();
      $master_records = self::getData($master_csv_uri);
      $master_csv_data = [];
      foreach ($master_records as $key => $record){
        $master_csv_data[] = [
          'Manufacturer' =>  $record[0],
          'Product' =>  $record[1],
        ];
      }
      //init new data and set headers
      $filter_data = [
        [
          'ID',
          'Manufacturer',
          'Product',
          'Flavour',
          'Formulation',
          'Batch ID',
          'Batch Expiry',
          'Test Date',
        ]
      ];
      $i = 1;
      self::addRecords($filter_data, $master_csv_data, $unmerged_eu_files, 'eu', $i);
      self::addRecords($filter_data, $master_csv_data, $unmerged_us_files, 'us', $i);
      $fileSystem = \Drupal::service('file_system');
      $uri = 'public://merged_csv/';
      $fileSystem->deleteRecursive($uri);
      $fileSystem->prepareDirectory($uri, 777);
      $filename = $fileSystem->createFilename('post_macro_data.csv', $uri);
	  $file = \Drupal::service('file.repository')->writeData('', $filename, FileSystemInterface::EXISTS_REPLACE);
      if ($file){
        $reader = new \PhpOffice\PhpSpreadsheet\Reader\Csv();
        $spreadsheet = $reader->load($file->getFileUri());
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->fromArray($filter_data, NULL, 'A1');
        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Csv($spreadsheet);
        $writer->save($file->getFileUri());

        $this->config('informed_module.batch_importer')
          ->set('downloadable_file', $file->id())
          ->save();
      }
    }
  }

  public static function addRecords(&$filter_data, $master_csv_data, $files, $region, $i){
    foreach ($files as $id => $file){
      $file_uri = $file->getFileUri();
      $group_records =  self::getData($file_uri, $id);
      array_shift($group_records);
      foreach ($group_records as $key => $record) {
//        $product = $master_csv_data[$record[1]] ?? false;
        $product_index = array_search(['Manufacturer' => $record[0], 'Product' => $record[1]], $master_csv_data) ?? false;
        $product = $product_index ? $master_csv_data[$product_index] : false;
        if ($product){
          if (is_float($record[6])){
            $record[6] = date('d-M-Y', \PhpOffice\PhpSpreadsheet\Shared\Date::excelToTimestamp($record[6]));
          }
          self::formatRecordDates($record, $region);
          // insure the columns are in the right order
          // encode the batch id because who knows why but otherwise some values come out wrong
          $batch_id = 'ID '. utf8_encode($record[4]);
          $filter_data[] = [
            $i,
            $record[0],
            $record[1],
            $record[2],
            $record[3],
            $batch_id,
            $record[5],
            $record[6],
          ];
        }
        $i++;
      }

    }
  }

  public function ImportBatches (array &$form, FormStateInterface $form_state){
    $file = \Drupal::entityTypeManager()->getStorage('file')->load($form_state->getValue('csv')[0]);
    $config = $this->config('informed_module.batch_importer');
    $chunk_size = $config->get('chuck_size') ?? [];
    if ($file){
      $uri = $file->getFileUri();
      $data = self::getData($uri);
      $data = self::formBatchRecords($data);
      self::queueBatchImport($data, $chunk_size);
    }
  }

  /**
   * {@inheritdoc}
   */
  public function submitForm(array &$form, FormStateInterface $form_state) {
    parent::submitForm($form, $form_state);

    $csv = \Drupal::entityTypeManager()->getStorage('file')->load($form_state->getValue('csv')[0]);
    if ($csv){
      $csv->setPermanent();
      $csv->save();
    }
//
    $master_csv = \Drupal::entityTypeManager()->getStorage('file')->load($form_state->getValue('master_csv')[0]);
    if ($master_csv){
      $master_csv->setPermanent();
      $master_csv->save();
    }
//
    $unmerged_eu_files =  \Drupal::entityTypeManager()->getStorage('file')->loadMultiple($form_state->getValue('unmerged_eu_files'));
    $unmerged_us_files =  \Drupal::entityTypeManager()->getStorage('file')->loadMultiple($form_state->getValue('unmerged_us_files'));
    $files = array_merge($unmerged_eu_files, $unmerged_us_files);
    if ($files){
      foreach ($files as $file){
        if ($file){
          $file->setPermanent();
          $file->save();
        }
      }
    }


    $this->config('informed_module.batch_importer')
      ->set('master_csv', $form_state->getValue('master_csv'))
      ->set('unmerged_eu_files', $form_state->getValue('unmerged_eu_files'))
      ->set('unmerged_us_files', $form_state->getValue('unmerged_us_files'))
      ->set('csv', $form_state->getValue('csv'))
      ->set('chuck_size', $form_state->getValue('chuck_size'))
      ->save();

  }

  public static function getData($uri){
    $url = \Drupal::service('file_url_generator')->generateAbsoluteString($uri);
    $ext = pathinfo($url)['extension'] ?? false;
    $inputFileType = ucfirst($ext);
    // the different exts it will take
//    $inputFileType = 'Xlsx';
//    $inputFileType = 'Xls';
//    $inputFileType = 'Xml';
//    $inputFileType = 'Ods';
//    $inputFileType = 'Slk';
//    $inputFileType = 'Gnumeric';
//    $inputFileType = 'Csv';
    $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader($inputFileType);
    $reader->setReadDataOnly(true);
    $reader->setReadEmptyCells(false);
    /**  Load $inputFileName to a Spreadsheet Object  **/
    $spreadsheet = $reader->load($uri);
    $worksheet = $spreadsheet->getActiveSheet();
    $data = $worksheet->toArray();
    $data = array_map('array_filter', $data);
    $data = array_filter($data);
    $data = array_values($data);
    $data = array_map('array_values', $data);
    if ($data[0][0] == 'ID'){
      $data = array_map('self::array_remove_first', $data);
    }
    return $data;
  }

  public static function array_remove_first(array $array){
    array_shift($array);
    return $array;
  }

  public static function formBatchRecords($data){
    $data_bundle = [];
    foreach ($data as $record) {
      $row = [];
      if (isset($record[0])){
        $row['field_brand'] = trim($record[0]);
      }
      if (isset($record[1])){
        $row['field_product'] = trim($record[1]);
      }
      if (isset($record[2])){
        $row['field_flavour'] = trim($record[2]);
      }
      if (isset($record[3])){
        $row['field_formulation'] = trim($record[3]);
      }
      if (isset($record[4])){
        $row['title'] = trim($record[4]);
      }
      if (isset($record[5])){
        $row['field_expiration'] = trim($record[5]);
      }
      if (isset($record[6])){
        $row['field_test_date'] = trim($record[6]);
      }
      //all fields from the record are converted into UTF-8 charset
      $data_bundle[] = $row;
    }
    array_shift($data_bundle);
    return $data_bundle;
  }

  public static function formatRecordDates(&$record, $region){
    $white_list_columns_as_dates = [5, 6];
    foreach ($record as $key => $data){
      if (strpos($data, '/') !== false && in_array($key, $white_list_columns_as_dates)) {
        $date_sections = count(explode('/',$data));

        if ($date_sections === 2){
          $partial_date = explode('/',$data);
          array_splice($partial_date,1,0, '01');
          $record[$key] = date('t-M-Y', strtotime(implode('/',$partial_date)));
        } else if ($region === 'eu' && $date_sections > 2) {
			/** @noinspection PhpArrayAccessCanBeReplacedWithForeachValueInspection */
			$record[$key] = str_replace('/', '-', $record[$key]);
        }

        $record[$key] = date('d-M-Y', strtotime($record[$key]));
      }
    }
  }

  public static function queueBatchImport(array $batch_items = [], $chunk_size = 50){
    if (empty($batch_items)){
      return;
    } else {
      $batch = [
        'title' => t('Importing Batch Items'),
        'operations' => [],
        'finished' => 'Batch Items Imported',
        'file' => \Drupal::service('extension.path.resolver')->getPath('module', 'informed_module') . '/informed_module.entity_import.update.batch.inc',
      ];
      $batch_item_chunks = array_chunk($batch_items, $chunk_size, true);
      $batch['operations'][] = ['edit_entities', [$batch_item_chunks, count($batch_items), __NAMESPACE__ . '\BatchImporter' ,'processBatchItem']];
      batch_set($batch);
    }

  }

  /**
   * use the data coming from the csv row to create/update a batch item and fill out its fields or write error log notices
   *
   * @param array $batch_item this is coming from the actual batch queue and is an item from the incoming csv file
   * @throws \Drupal\Component\Plugin\Exception\InvalidPluginDefinitionException
   * @throws \Drupal\Component\Plugin\Exception\PluginNotFoundException
   * @throws \Drupal\Core\Entity\EntityStorageException
   */
  public static function processBatchItem(array $batch_item){
    // check if all the fields are present
    if (!self::array_keys_exists(['title', 'field_brand', 'field_product', 'field_flavour', 'field_formulation', 'field_expiration', 'field_test_date'],$batch_item)){
      return;
    }

    $node = NULL;

    //Strip unprintable characters
    $title = self::prepareString($batch_item['title']);

    //Load existing batches with matching ID (title)
    $batch_matches = \Drupal::entityTypeManager()->getStorage('node')->loadByProperties(['title' => $title, 'type' => 'batch']);

    //Check for direct product name matches
    foreach ($batch_matches as $batch_match){
      // @todo may be worth also checking if the brand matches?
      if (self::hasProduct($batch_match, $batch_item['field_product'])){
        $node = $batch_match;
      }
    }

    /** Disable fingerprinting for the moment
    //If no direct product name matches, try to match by fingerprint
    if ($node === NULL){
      foreach ($batch_matches as $batch_match){
        if (self::matchesFingerPrint($batch_match, $batch_item) === TRUE){
          $node = $batch_match;
          break;
        }
      }
    }
     **/

    //Client requested flavors have their own batches, so if the flavour doesn't match, unset so that a new node is created
    if($node != NULL && $node->field_flavour->value != $batch_item['field_flavour']) {
      $node = NULL;
    }

    //If we still have no matches, create a new batch node
    if ($node === NULL){
      $node = Node::create(['type' => 'batch', 'title' => $title]);
    }

    $node->set('field_unmatched', false);
    self::set_taxonomy_term_reference_field($node, 'brand', 'field_brand', $batch_item['field_brand']);
    //self::setFlavour($node, 'field_flavour', $batch_item['field_flavour']);
    $node->set('field_flavour', $batch_item['field_flavour']);
    self::set_taxonomy_term_reference_field($node, 'formulation', 'field_formulation', $batch_item['field_formulation']);
    self::set_node_reference_field($node, 'product', 'field_product', $batch_item['field_product'], $batch_item['field_brand']);
    self::set_date_field($node,'field_expiration', $batch_item['field_expiration']);
    self::set_date_field($node,'field_test_date', $batch_item['field_test_date']);

    $node->save();
  }

  /**
   * Checks if an array of keys $keys exists in array $arr
   * @param array $keys
   * @param array $arr
   * @return bool
   */
  public static function array_keys_exists(array $keys, array $arr) {
    return !array_diff_key(array_flip($keys), $arr);
  }

  /**
   * Returns a clean, lowercase string for comparison
   * @param string $string
   * @return string
   */
  public static function prepareString(string $string) {
    return strtoupper(preg_replace('/[\x00-\x1F\x80-\xFF]/', '', $string));
  }

  /**
   * Appends flavour to list of flavours and sets field value for batch
   * @param object $node
   * @param string $field
   * @param string $value
   */
  public static function setFlavour($node, $field, $value){
    $current_value = $node->$field->value;
    if ($current_value){
      $new_value = $current_value . ', '. $value;
    } else {
      $new_value = $value;
    }
    $new_value = explode(', ', $new_value);
    $new_value = array_unique($new_value);
    $new_value = implode(', ', $new_value);
    $node->set('field_flavour', $new_value);
  }

  /**
   * try and set the term to specific field
   *
   * @param $node Node the current node (batch) being processed
   * @param $vid string the vocabulary id we are targeting to find the term
   * @param $field string the current field on the node being processed
   * @param $value string the appropriate value being set to the field
   * @throws \Drupal\Component\Plugin\Exception\InvalidPluginDefinitionException
   * @throws \Drupal\Component\Plugin\Exception\PluginNotFoundException
   */
  public static function set_taxonomy_term_reference_field(Node &$node, string $vid, string $field, string $value){
    $matched_terms = \Drupal::entityTypeManager()->getStorage('taxonomy_term')->loadByProperties(['name' => trim(strtolower($value), ' \0'), 'vid' => $vid]);
    $matched_term = array_shift($matched_terms);
    if ($matched_term){
      //single value
      //$node->$field->target_id = $matched_term->id();
      $node->$field = self::setUniqueTargetIds($node, $field, $matched_term);
    } else {
      self::set_unmatched($node);
      self::set_unmatched_data($node, $field, $value);
    }
  }

  /**
   *
   * try and set a node to specific field
   *
   * @param $node object the current node (batch) being processed
   * @param $type string the "type" of node we are going to try and match
   * @param $field string the current field on the node being processed
   * @param $product string the appropriate value being set to the field
   * @param $brand string
   * @throws \Drupal\Component\Plugin\Exception\InvalidPluginDefinitionException
   * @throws \Drupal\Component\Plugin\Exception\PluginNotFoundException
   */
  public static function set_node_reference_field(object &$node, string $type, string $field, string $product, string $brand){
    $brand = self::prepareString($brand);
    $product = self::prepareString($product);
    $matched_terms = \Drupal::entityTypeManager()->getStorage('taxonomy_term')->loadByProperties(['name' => strtolower($brand), 'vid' => 'brand']);
    $matched_term = array_shift($matched_terms);
    $matched_nodes = \Drupal::entityTypeManager()->getStorage('node')->loadByProperties(['title' => strtolower($product), 'type' => $type]);
    $unmatched = TRUE;
    foreach ($matched_nodes as $matched_node){
      $product_brand_id = $matched_node->field_brand->target_id;

      if ($matched_term && $product_brand_id === $matched_term->id()){
        $node->set($field, $matched_node->id());
        $unmatched = FALSE;
        //$node->$field = self::setUniqueTargetIds($node, $field, $matched_node);
      }
    }

    if ($unmatched) {
      self::set_unmatched($node);
      self::set_unmatched_data($node, $field, $product);
    }

  }

  /**
   * @param $node object the current node (batch) being processed
   * @param $field string the current field on the node being processed
   * @param $value string that appropriate value being set to the field
   */
  public static function set_date_field(&$node, $field, $value){
    if (date('Y', strtotime($value)) === '1970'){
      self::set_unmatched($node);
      self::set_unmatched_data($node, $field, $value);
    } else {
      $old_date = date('Ymd', strtotime($node->$field->value));
      $new_date = date('Ymd', strtotime($value));
      if ($old_date !== '1970' && $old_date && $old_date < $new_date){
        $node->set($field, date('Y-m-d', strtotime($value)));
      }
    }
  }

  /**
   * set whether a batch item has all fields matching or not to give opportunity to check whats not matched. batches can function with only the product tagged.
   *
   * @param $node
   */
  public static function set_unmatched(&$node){
    $node->set('field_unmatched', 1);
  }

  /**
   * this is used to write basically an error log to a batch item
   *
   * @param $node object the current node (batch) being processed
   * @param $field string the current field on the node being processed
   * @param $value string the string value being added
   */
  public static function set_unmatched_data(&$node, $field, $value){
    $unmatched_data = $node->field_unmatched_data->value;
    if ($unmatched_data){
      $unmatched_data .= "\n";
    } else {
      $unmatched_data = '';
    }
    $node->set('field_unmatched_data',$unmatched_data . $field . ' | ' . $value . ' | ' . date(DATE_RFC2822));
  }


  /**
   * grabs all data in a field and makes sure there are only unique items tagged to it.
   *
   * @param $node object the current node (batch) being processed
   * @param $field string the current field on the node being processed
   * @param $matched_entity object the entity that has been matched from a string to add to array of target ids for a multifield entity reference field
   * @return array collection of arrays containing target ids
   */
  public static function setUniqueTargetIds($node, $field, $matched_entity){
    $current_values = array_column($node->$field->getValue(), 'target_id');
    $current_values[] = $matched_entity->id();
    $current_values = array_unique($current_values);
    $values = [];
    foreach ($current_values as $value){
      $values[] = ['target_id' => $value];
    }

    return $values;
  }

  public static function hasProduct($node, $product_title){
    $batch_product_id = $node->field_product->target_id;

    if ($batch_product_id) {
      $product = \Drupal::entityTypeManager()->getStorage('node')->load($batch_product_id);

      if ($product) {
        $product_title = self::prepareString($product_title);
        $node_product_title = self::prepareString($product->label());

        if($product_title == $node_product_title) {
          return TRUE;
        }
      }
    }

    return FALSE;
  }

  /**
   * Match batch by formulation, brand, and dates
   *
   * @param $batch_match object batch node
   * @param $batch_item array queue item from CSV
   * @return bool
   * @throws \Drupal\Component\Plugin\Exception\InvalidPluginDefinitionException
   * @throws \Drupal\Component\Plugin\Exception\PluginNotFoundException
   */
  public static function matchesFingerPrint($batch_match, $batch_item){
    $matching = FALSE;
	$node_formulation_names = [];

    //Match formulations
    $formulation = self::prepareString($batch_item['field_formulation']);
    $node_formulations = array_column($batch_match->field_formulation->getValue(), 'target_id');
    $node_formulations = $node_formulations ? \Drupal::entityTypeManager()->getStorage('taxonomy_term')->loadMultiple($node_formulations) : FALSE;

    if ($node_formulations){
      foreach ($node_formulations as $node_formulation){
        $node_formulation_names[] = strtolower($node_formulation->label());
      }
      if (in_array(strtolower($formulation),$node_formulation_names)){
        $matching = TRUE;
      }
    }

    //Match brand
    $brand = self::prepareString($batch_item['field_brand']);
    $node_brand = $batch_match->field_brand->target_id;
    $node_brand = $node_brand ? \Drupal::entityTypeManager()->getStorage('taxonomy_term')->load($node_brand) : FALSE;

    if ($node_brand){
      if (self::prepareString($node_brand->label()) === $brand){
        $matching = TRUE;
      } else {
        $matching = FALSE;
      }
    }

    /**
    //Match batch dates
    $field_expiration = date('Ymd', strtotime($batch_item['field_expiration']));
    $field_test_date = date('Ymd', strtotime($batch_item['field_test_date']));
    $node_exp_date = date('Ymd', strtotime($batch_match->field_expiration->value));
    $node_test_date = date('Ymd', strtotime($batch_match->field_test_date->value));

    if ($node_test_date && $node_exp_date && $matching){
      //if we match or if the new batch has a date high than the current node then match
      if ($node_test_date === $field_test_date || $field_test_date > $node_test_date){
        $matching = true;
      } else {
        if ($node_test_date > $field_test_date){
          // if we matched but the batch we are trying to insert has a date lower then lets return null so we can just skip
          return null;
        }
      }

      if ($node_exp_date === $field_expiration || $field_expiration > $node_exp_date){
        $matching = true;
      } else {
        if ($node_exp_date > $field_expiration){
          return null;
        }
      }
    }
    **/

    return $matching;
  }
}
