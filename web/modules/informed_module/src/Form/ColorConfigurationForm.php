<?php

namespace Drupal\informed_module\Form;

use <PERSON>upal\Core\File\FileSystemInterface;
use <PERSON>upal\Core\Form\ConfigFormBase;
use <PERSON><PERSON>al\Core\Form\FormStateInterface;
use League\Csv\Writer;

/**
 * Class ColorConfigurationForm.
 */
class ColorConfigurationForm extends ConfigFormBase {
  const palettes = [
    'general' => [
      'primary',
      'secondary',
      'tertiary',
      'quaternary',
      'quinary',
      'senary',
    ],
    'topbar' => [
      'text_color',
      'text_hover_color',
      'decoration_color',
    ],
    'header' => [
      'background_color',
      'text_color',
      'text_hover_color',
      'link_with_submenu_text_hover_color',
      'link_with_submenu_background_hover_color',
      'submenu_text_color',
      'submenu_text_hover_color',
      'submenu_background_color',
      'submenu_link_background_hover_color',
    ],
    'drilldown' => [
      'back_hover_color',
      'background',
      'link_color',
      'link_hover_color',
      'link_background_color',
      'link_background_hover_color',
      'border_color',
    ],
    'sidebar' => [
      'text_color',
      'text_hover_color',
      'background_color',
      'background_hover_color',
    ],
    'forms' => [
      'label_color',
      'text_color',
      'input_color',
    ],
    'tables' => [
      'header_background_color',
      'header_text_color',
      'header_text_hover_color',
      'even_row_background_color',
      'odd_row_background_color',
      'row_text_color',
      'row_text_hover_color',
    ],
    'accordion' => [
      'label_background_color',
      'label_text_color',
    ]
  ];

  /**
   * {@inheritdoc}
   */
  protected function getEditableConfigNames() {
    return [
      'informed_module.color_configuration',
    ];
  }

  /**
   * {@inheritdoc}
   */
  public function getFormId() {
    return 'color_configuration_form';
  }

  /**
   * {@inheritdoc}
   */
  public function buildForm(array $form, FormStateInterface $form_state) {
    $config = $this->config('informed_module.color_configuration');
    foreach (self::palettes as $palette => $options) {
      $form[$palette] = [
        '#type' => 'details',
        '#title' => "$palette Color Settings",
        '#open' => true,
      ];
      foreach ($options as $option) {
        $form[$palette][$palette.'_'.$option] = [
          '#type' => 'color',
          '#title' => $this->t(ucwords(str_replace('_', ' ', $option))),
          '#default_value' => $config->get($palette.'_'.$option),
        ];
      }
    }

    return parent::buildForm($form, $form_state);
  }

  /**
   * {@inheritdoc}
   */
  public function submitForm(array &$form, FormStateInterface $form_state) {
    parent::submitForm($form, $form_state);

    $config = $this->config('informed_module.color_configuration');
    foreach (self::palettes as $palette => $options) {
      foreach ($options as $option) {
        $config->set($palette.'_'.$option, $form_state->getValue($palette.'_'.$option));
      }
    }

      $fileSystem = \Drupal::service('file_system');
      $uri = 'public://generated_scripts/';
      $fileSystem->deleteRecursive($uri);
      $fileSystem->prepareDirectory($uri, 777);
      $filename = $fileSystem->createFilename('generated-styles.css', $uri);
	  $file = \Drupal::service('file.repository')->writeData(self::generateClasses(),  $filename, FileSystemInterface::EXISTS_REPLACE);
      if ($file){
        $uri = $file->getFileUri();
        $url = \Drupal::service('file_url_generator')->generateAbsoluteString($uri);
        $config->set('stylesheet_url', $url);
      }


      $config->save();
  }

  public static function generateClasses (){
    $config = \Drupal::config('informed_module.color_configuration')->get();
    $classes = '';
    $palettes = self::palettes;

    foreach ($palettes['general'] as $option) {
      $color = $config['general_'.$option];
      $classes .= "body #page-wrapper .bg-color-$option{background-color:$color;}";
      $classes .= "body #page-wrapper .hover-bg-color-$option:hover{background-color:$color;}";
      $classes .= "body #page-wrapper .color-$option{color:$color;}";
      $classes .= "body #page-wrapper .hover-color-$option:hover{color:$color;}";
      $classes .= "body #page-wrapper .anchor-color-$option a,body #page-wrapper a.anchor-color-$option{color:$color;}";
      $classes .= "body #page-wrapper .anchor-hover-color-$option a:hover,body #page-wrapper a.anchor-hover-color-$option:hover, body #page-wrapper .anchor-hover-color-$option .is-active > a{color:$color;}";
      $classes .= "body #page-wrapper .button-$option{background-color:$color;}";
      $classes .= "body #page-wrapper .hover-button-$option:hover{background-color:$color;}";
      $classes .= "body #page-wrapper .text-decoration-color-$option u {background-image: linear-gradient(to bottom,transparent 33%,transparent 33%,transparent 66%,$color 66%,$color);}";
      $classes .= "body #page-wrapper .border-color-$option{border-color:$color;}";

    }

    // default button color
    $classes .= "body a.button, body input.button {background-color:".$config['general_primary'].";color:black;}";
    $classes .= "body a.button:hover, body input.button:hover {background-color:".$config['general_primary'].";color:white;}";

    // Slick Active Thumbnail Primary
    $classes .= "body #page-wrapper .slick-current:after {background-color:".$config['general_primary']."}";


    // Top bar rules
    $classes .= '#topbar .topbar-menu a {color: ' . $config['topbar_text_color'] . '}';
    $classes .= '#topbar .topbar-menu a:hover, #topbar .topbar-menu .is-active > a {color: ' . $config['topbar_text_hover_color'] . '}';
    $classes .= '#topbar .topbar-menu a:hover:after, #topbar .topbar-menu .is-active > a:after {background-color: ' . $config['topbar_decoration_color'] . '}';

    // header rules
//    'text_color',
//      'text_hover_color',
//    'link_background_hover_color',
//      'submenu_text_color',
//      'submenu_text_hover_color',
//      'submenu_background_color',
//      'submenu_link_background_hover_color',
    $classes .= '#main-header {background-color: ' . $config['header_background_color'] . '}';
    $classes .= '#main-header .main-menu a, #main-header .main-menu span{color: ' . $config['header_text_color'] . '}';
    $classes .= '#main-header .main-menu a:hover, #main-header .main-menu span:hover, #main-header .main-menu li.active-root-item > a, #main-header .main-menu li.active-root-item > span{color: ' . $config['header_text_hover_color'] . '}';
    $classes .= '#main-header .main-menu .is-dropdown-submenu-parent a:hover, #main-header .main-menu .is-dropdown-submenu-parent span:hover, #main-header .main-menu li.is-dropdown-submenu-parent.is-active > a, #main-header .main-menu li.is-dropdown-submenu-parent.is-active > span{color: ' . $config['header_link_with_submenu_text_hover_color'] . '}';
    $classes .= '#main-header .main-menu .is-dropdown-submenu-parent a:hover, #main-header .main-menu .is-dropdown-submenu-parent span:hover, #main-header .main-menu li.is-dropdown-submenu-parent.is-active > a, #main-header .main-menu li.is-dropdown-submenu-parent.is-active > span{background-color: ' . $config['header_link_with_submenu_background_hover_color'] . '}';
    $classes .= '#main-header .main-menu .submenu a, #main-header .main-menu .submenu span {color: ' . $config['header_submenu_text_color'] . '}';
    $classes .= '#main-header .main-menu .submenu a:hover, #main-header .main-menu .submenu span:hover, #main-header .main-menu .submenu li.is-active > a, #main-header .main-menu .submenu li.is-active > span{color: ' . $config['header_submenu_text_hover_color'] . '}';
    $classes .= '#main-header .main-menu .submenu {background-color: ' . $config['header_submenu_background_color'] . '}';
    $classes .= '#main-header .main-menu .submenu a:hover, #main-header .main-menu .submenu span:hover, #main-header .main-menu .submenu li.is-active > a, #main-header .main-menu .submenu li.is-active > span{background-color: ' . $config['header_submenu_link_background_hover_color'] . '}';

    // drilldown rules
      //background
      //link_color
      //link_hover_color
      //link_background_color
      //link_background_hover_color
      //border_color
    $classes .= '#page #mobile-menu, #mobile-menu .drilldown .is-drilldown-submenu {background-color: ' . $config['drilldown_background'] . '}';
    $classes .= '#page #mobile-menu #drilldown-menu a {color: ' . $config['drilldown_link_color'] . '}';
    //$classes .= '#page #mobile-menu #drilldown-menu a:hover {color: ' . $config['drilldown_link_hover_color'] . '}';
    $classes .= '#page #mobile-menu #drilldown-menu a {background-color: ' . $config['drilldown_link_background_color'] . '}';
    //$classes .= '#page #mobile-menu #drilldown-menu a:hover {background-color: ' . $config['drilldown_link_background_hover_color'] . '}';
    $classes .= '#page #mobile-menu #drilldown-menu a {border-color: ' . $config['drilldown_border_color'] . '}';
    //$classes .= '#page #mobile-menu #drilldown-menu .js-drilldown-back a:hover {color: ' . $config['drilldown_back_hover_color'] . '}';

    // sidebar rules
    $classes .= '#content .sidebar-menu a, #content .sidebar-menu span{color: ' . $config['sidebar_text_color'] . '}';
    $classes .= '#content .sidebar-menu a:hover, #content .sidebar-menu .is-active > a, #content .sidebar-menu span:hover, #content .sidebar-menu .is-active > span{color: ' . $config['sidebar_text_hover_color'] . '}';
    $classes .= '#content .sidebar-menu a, #content .sidebar-menu span{background-color: ' . $config['sidebar_background_color'] . '}';
    $classes .= '#content .sidebar-menu a:hover, #content .sidebar-menu .is-active > a, #content .sidebar-menu span:hover, #content .sidebar-menu .is-active > span {background-color: ' . $config['sidebar_background_hover_color'] . '}';

    // form rules
//    'label_color',
//    'text_color',
//    'input_color',
    $classes .= '#content form label {color: ' . $config['forms_label_color'] . '}';
    $classes .= '#content form input:not([type="submit"]), #content form textarea, #content form select {background-color: ' . $config['forms_input_color'] . '}';
    $classes .= '#content form input:not([type="submit"]), #content form textarea, #content form select {color: ' . $config['forms_text_color'] . '}';

//      'tables_header_background_color',
//      'tables_header_text_color',
//      'tables_header_text_hover_color',
//      'tables_even_row_background_color',
//      'tables_odd_row_background_color',
//      'tables_row_text_color',
//      'tables_row_text_hover_color',
    $classes .= '#content table thead tr th {background-color: ' . $config['tables_header_background_color'] . '}';
    $classes .= '#content table thead tr th a, #content table thead tr th {color: ' . $config['tables_header_text_color'] . '}';
    $classes .= '#content table thead tr th a:hover, #content table thead tr .is-active a {color: ' . $config['tables_header_text_hover_color'] . '}';
    $classes .= '#content table tbody tr:nth-child(even), [data-breakpoint="small"] #content table tbody tr td:nth-child(even) {background-color: ' . $config['tables_even_row_background_color'] . '}';
    $classes .= '#content table tbody tr:nth-child(odd), [data-breakpoint="small"] #content table tbody tr td:nth-child(odd) {background-color: ' . $config['tables_odd_row_background_color'] . '}';
    $classes .= '#content table tbody tr td, #content table tbody tr td a, #content table tbody tr td .is-active a {color: ' . $config['tables_row_text_color'] . '}';
    $classes .= '#content table tbody tr td a:hover, #content table tbody tr td .is-active a {color: ' . $config['tables_row_text_hover_color'] . '}';

    //accordion classes
    $classes .= '#page .ckeditor-accordion-toggler {background-color: ' . $config['accordion_label_background_color'] . '}';
    $classes .= '#page .ckeditor-accordion-toggler:hover {background-color: ' . $config['accordion_label_background_color'] . '}';
    $classes .= '#page .ckeditor-accordion-toggler {color: ' . $config['accordion_label_text_color'] . ' !important}';

    return  $classes;
  }

}
