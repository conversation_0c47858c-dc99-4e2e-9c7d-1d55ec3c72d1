<?php

namespace Drupal\informed_module\Form;

use <PERSON><PERSON>al\Component\Utility\UrlHelper;
use <PERSON>upal\Core\Form\ConfigFormBase;
use <PERSON><PERSON>al\Core\Form\FormStateInterface;

/**
 * Class PdfPageGeneration.
 */
class PdfPageGeneration extends ConfigFormBase {

  /**
   * {@inheritdoc}
   */
  protected function getEditableConfigNames() {
    return [
      'informed_module.pdf_page_generation',
    ];
  }

  /**
   * {@inheritdoc}
   */
  public function getFormId() {
    return 'pdf_page_generation';
  }

  /**
   * {@inheritdoc}
   */
  public function buildForm(array $form, FormStateInterface $form_state) {
    $config = $this->config('informed_module.pdf_page_generation');
    $uri = 'public://generated-pdf/file.pdf';
    $url = \Drupal::service('file_url_generator')->generateAbsoluteString($uri);
    $is_valid = file_exists($uri);

    $form['path'] = [
      '#type' => 'textfield',
      '#title' => $this->t('Path'),
      '#description' => $this->t('Enter the page path you would like to generate a pdf of. This pdf will get regenerated during cron jobs. Must be like "node/144". Saving will force generate.'),
      '#maxlength' => 64,
      '#size' => 64,
      '#default_value' => $config->get('path'),
    ];

    if ($is_valid){
      $form['filepath'] = [
        '#markup' => "<strong>Path to file:</strong> " . $url,
      ];
      $form['description'] = [
        '#markup' => "<div><small>You can just copy the relative path. So everything after the first slash /sites/default/files/generated-pdf/file.pdf</small></div>",
      ];
    }
    //  goto this path to generate the pdf  /wkhtmltopdf/generatepdf?url=[absolute-path-to-generate]
    return parent::buildForm($form, $form_state);
  }

  /**
   * {@inheritdoc}
   */
  public function submitForm(array &$form, FormStateInterface $form_state) {
    parent::submitForm($form, $form_state);
    $this->config('informed_module.pdf_page_generation')
      ->set('path', $form_state->getValue('path'))
      ->save();
    $host = \Drupal::request()->getScheme() . '://' . \Drupal::request()->getHost();
    $pdf_generate = \Drupal::service('informed_module.generate_pdf')->manuallyGeneratePdf($form_state->getValue('path'));
  }

}
