<?php

namespace Drupal\informed_module\Form;

use <PERSON>upal\Core\Form\ConfigFormBase;
use <PERSON><PERSON>al\Core\Form\FormStateInterface;
use <PERSON><PERSON><PERSON>\node\Entity\Node;

/**
 * Class ProductUpcImport.
 */
class ProductUpcImport extends ConfigFormBase {

  /**
   * {@inheritdoc}
   */
  protected function getEditableConfigNames() {
    return [
      'informed_module.product_upc_import',
    ];
  }

  /**
   * {@inheritdoc}
   */
  public function getFormId() {
    return 'product_upc_import';
  }

  /**
   * {@inheritdoc}
   */
  public function buildForm(array $form, FormStateInterface $form_state) {
    $config = $this->config('informed_module.product_upc_import');
    $form['csv'] = [
      '#type' => 'managed_file',
      '#title' => $this->t('Csv'),
      '#description' => $this->t('Enter a csv or xls with columns: manufacturer, product name, upc to import upc codes onto products. the columns must be in order as mentioned.'),
      '#default_value' => $config->get('csv'),
      '#upload_validators' => [
        'file_validate_extensions' => ['csv xls'],
        'file_validate_size' => [25600000],
      ],
      '#upload_location' => 'public://csv/',
    ];

    $form['chuck_size'] = [
      '#type' => 'select',
      '#title' => $this->t('Batch Chunk Size'),
      '#description' => $this->t('Select the size of chunking you would like to use for batch imports.'),
      '#default_value' => $config->get('chuck_size'),
      '#options' => [
        25 => '25',
        50 => '50',
        100 => '100',
        150 => '150',
        200 => '200',
        250 => '250',
      ],
    ];
    $form = parent::buildForm($form, $form_state);

    $form['actions']['import_upc_codes'] = [
      '#type' => 'submit',
      '#button_type' => "primary",
      '#value' => 'Import Upc Codes',
      '#name' => 'import_upc_codes',
      '#submit' => array([$this, 'ImportUpcCodes']),
    ];

    $form['actions']['clear_upc_codes'] = [
      '#type' => 'submit',
      '#button_type' => "primary",
      '#value' => 'Clear Upc Codes',
      '#name' => 'clear_upc_codes',
      '#submit' => array([$this, 'ClearUpcCodes']),
      '#attributes' => [
        'style' => 'margin:0;',
      ],
    ];

    return $form;
  }

  /**
   * {@inheritdoc}
   */
  public function submitForm(array &$form, FormStateInterface $form_state) {
    parent::submitForm($form, $form_state);


    $csv = \Drupal::entityTypeManager()->getStorage('file')->load($form_state->getValue('csv')[0]);
    if ($csv){
      $csv->setPermanent();
      $csv->save();
    }

    $this->config('informed_module.product_upc_import')
      ->set('csv', $form_state->getValue('csv'))
      ->set('chuck_size', $form_state->getValue('chuck_size'))
      ->save();
  }

  public function ImportUpcCodes (array &$form, FormStateInterface $form_state){
    $file = \Drupal::entityTypeManager()->getStorage('file')->load($form_state->getValue('csv')[0]);
    $config = $this->config('informed_module.product_upc_import');
    $chunk_size = $config->get('chuck_size') ?? [];
    if ($file){
      $uri = $file->getFileUri();
      $data = self::getData($uri);
      $data = self::formBatchRecords($data);
      self::queueImport($data, $chunk_size);
    }
  }

  public function ClearUpcCodes (){
    $config = $this->config('informed_module.product_upc_import');
    $chunk_size = $config->get('chuck_size') ?? 50;
    $batch = [
      'title' => t('Clearing Product Upc Codes'),
      'operations' => [],
      'finished' => 'Product Upc Codes Cleared',
      'file' => \Drupal::service('extension.path.resolver')->getPath('module', 'informed_module') . '/informed_module.entity_import.update.batch.inc',
    ];
    $query = \Drupal::entityQuery('node');
    $query->accessCheck(true);
    $query->condition('type', 'product');
    $product_ids = $query->execute();
    $item_chunks = array_chunk($product_ids, $chunk_size, false);
    $batch['operations'][] = ['edit_entities', [$item_chunks, count($product_ids), __NAMESPACE__ . '\ProductUpcImport' ,'clearProductUpcCodes']];
    batch_set($batch);
  }

  public static function queueImport(array $items = [], $chunk_size = 50){
    if (empty($items)){
      return;
    } else {
      $batch = [
        'title' => t('Importing Product Upc Codes'),
        'operations' => [],
        'finished' => 'Product Upc Codes Imported',
        'file' => \Drupal::service('extension.path.resolver')->getPath('module', 'informed_module') . '/informed_module.entity_import.update.batch.inc',
      ];
      $item_chunks = array_chunk($items, $chunk_size, true);
      $batch['operations'][] = ['edit_entities', [$item_chunks, count($items), __NAMESPACE__ . '\ProductUpcImport' ,'updateProduct']];
      batch_set($batch);
    }

  }

  public static function array_keys_exists(array $keys, array $arr) {
    return !array_diff_key(array_flip($keys), $arr);
  }

  public static function clearProductUpcCodes($id){
    $product = \Drupal::entityTypeManager()->getStorage('node')->load($id);
    if ($product && $product->hasField('field_upc')){
      $product->field_upc = [];
      $product->save();
    }
  }

  public static function updateProduct(array $item){
    if (!self::array_keys_exists(['field_brand', 'field_product', 'field_upc'],$item)){
      return;
    }

    // check and see if the batch id already exists
    $product = preg_replace('/[\x00-\x1F\x80-\xFF]/', '', $item['field_product']);
    $product_matches = \Drupal::EntityTypeManager()->getStorage('node')->loadByProperties(['title' => strtolower($product), 'type' => 'product']);
    $product_match = array_shift($product_matches) ?? [];
    $node = null;
    if ($product_match){
      $node = $product_match;
    }
//    else {
//      $node = Node::create(['type' => 'product', 'title' => $item['field_product']]);
//    }
    if ($node){
      $manufacturers = array_column($node->field_brand->getValue(), 'target_id');
      $brand = preg_replace('/[\x00-\x1F\x80-\xFF]/', '', $item['field_brand']);
      $matched_terms = \Drupal::entityTypeManager()->getStorage('taxonomy_term')->loadByProperties(['name' => strtolower($brand), 'vid' => 'brand']);
      $matched_term = array_shift($matched_terms);
      if ($matched_term){
        if (in_array($matched_term->id(), $manufacturers) && $node->hasField('field_upc')){
//          $node->set('field_upc', '');
          $current_values = array_column($node->field_upc->getValue(), 'value');
          $upc = preg_replace('/[\x00-\x1F\x80-\xFF]/', '', $item['field_upc']);
          $current_values[] = $upc;
          $values = array_unique($current_values);
          $node->field_upc = $values;
          $node->save();
        }
      }
    }

  }

  public static function getData($uri){
    $url = \Drupal::service('file_url_generator')->generateAbsoluteString($uri);
    $ext = pathinfo($url)['extension'] ?? false;
    $inputFileType = ucfirst($ext);
    // the different exts it will take
//    $inputFileType = 'Xlsx';
//    $inputFileType = 'Xls';
//    $inputFileType = 'Xml';
//    $inputFileType = 'Ods';
//    $inputFileType = 'Slk';
//    $inputFileType = 'Gnumeric';
//    $inputFileType = 'Csv';
    $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader($inputFileType);
    $reader->setReadDataOnly(true);
    $reader->setReadEmptyCells(false);
    /**  Load $inputFileName to a Spreadsheet Object  **/
    $spreadsheet = $reader->load($uri);
    $worksheet = $spreadsheet->getActiveSheet();
    $data = $worksheet->toArray();
    $data = array_map('array_filter', $data);
    $data = array_filter($data);
    $data = array_values($data);
    $data = array_map('array_values', $data);
    if ($data[0][0] == 'ID' || $data[0][0] == 'n'){
      $data = array_map([__CLASS__, 'array_remove_first'], $data);
    }
    return $data;
  }

  public static function array_remove_first(array $array){
    array_shift($array);
    return $array;
  }

  public static function formBatchRecords($data){
    $data_bundle = [];
    foreach ($data as $record) {
      $row = [];
      if (isset($record[0])){
        $row['field_brand'] = trim($record[0]);
      }
      if (isset($record[1])){
        $row['field_product'] = trim($record[1]);
      }
      if (isset($record[2])){
        $row['field_upc'] = trim($record[2]);
      }
      //all fields from the record are converted into UTF-8 charset
      $data_bundle[] = $row;
    }
    array_shift($data_bundle);
    return $data_bundle;
  }

}
