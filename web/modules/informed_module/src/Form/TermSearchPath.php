<?php

namespace Drupal\informed_module\Form;

use <PERSON>upal\Core\Form\ConfigFormBase;
use <PERSON>upal\Core\Form\FormStateInterface;

/**
 * Class TermSearchPath.
 */
class TermSearchPath extends ConfigFormBase {

  /**
   * {@inheritdoc}
   */
  protected function getEditableConfigNames() {
    return [
      'informed_module.term_search_path',
    ];
  }

  /**
   * {@inheritdoc}
   */
  public function getFormId() {
    return 'term_search_path';
  }

  /**
   * {@inheritdoc}
   */
  public function buildForm(array $form, FormStateInterface $form_state) {
    $config = $this->config('informed_module.term_search_path');
    $taxonomies = \Drupal::entityTypeManager()->getStorage('taxonomy_vocabulary')->loadMultiple();
    $taxonomies = array_keys($taxonomies);
    $taxonomy_options = [];
    foreach ($taxonomies as $taxonomy) {
      $taxonomy_options[$taxonomy] = $taxonomy;
    }
    $form['taxonomies'] = [
      '#type' => 'checkboxes',
      '#title' => $this->t('Taxonomies'),
      '#description' => $this->t('Choose taxonomies to take on the search query as their link site wide.'),
      '#options' => $taxonomy_options,
      '#default_value' => $config->get('taxonomies'),
    ];

    $form['search_url'] = [
      '#type' => 'textfield',
      '#title' => $this->t('Search Url'),
      '#description' => $this->t('Enter a url to tack on the query parameter to. (example: "/search-results") it will automatically take the taxonomy slug and add ?slug=tid.'),
      '#maxlength' => 64,
      '#size' => 64,
      '#default_value' => $config->get('search_url'),
    ];

    return parent::buildForm($form, $form_state);
  }

  /**
   * {@inheritdoc}
   */
  public function submitForm(array &$form, FormStateInterface $form_state) {
    parent::submitForm($form, $form_state);

    $this->config('informed_module.term_search_path')
      ->set('taxonomies', $form_state->getValue('taxonomies'))
      ->set('search_url', $form_state->getValue('search_url'))
      ->save();
  }

}
