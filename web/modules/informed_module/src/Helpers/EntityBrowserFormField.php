<?php

namespace Drupal\informed_module\Helpers;

use <PERSON><PERSON><PERSON>\Component\Utility\NestedArray;
use <PERSON><PERSON><PERSON>\Core\Form\FormStateInterface;
use <PERSON><PERSON><PERSON>\entity_browser_block\Plugin\Block\EntityBrowserBlock;
use Drupal\image\Entity\ImageStyle;

/**
 * Class EntityBrowserFormField
 * @package Drupal\informed_module\Helpers
 */
class EntityBrowserFormField {
  /**
   * @param $form /pass form by reference to add items
   * @param $configuration /this holds the default values
   * @param array $browsers what browsers do you want to loop thru and add
   * @param $entity_browser /entity browser machine name
   * @param int $cardinality -1 is unlimited values in the entity browser, 1 is one and so on
   * @param string $container_type wrapper to wrap the entity browsers with
   */
  public static function generateEntityBrowsers (&$form, $configuration , array $browsers, $entity_browser, $cardinality = -1, $container_type = 'details'){
    foreach ($browsers as $browser){
      $config = $configuration[$browser]['selection']['entity_browser']['entity_ids'] ?? false;
      $entities = [];
      if ($config){
        $entities = EntityBrowserBlock::loadEntitiesByIDs(explode(' ',$configuration[$browser]['selection']['entity_browser']['entity_ids']));

      }
      $form[$browser] = [
        '#type' => $container_type,
        '#title' => str_replace('_', ' ', $browser) . ' (' . count($entities) . ')' ,
      ];
      $form[$browser]['selection'] = [
        '#type' => 'container',
        '#attributes' => [
          'id' => 'entity-browser-block-form_' . $browser,
          'class' => ['informed-entity-browser-form']
        ],
      ];
      $form[$browser]['selection']['entity_browser'] = [
        '#type' => 'entity_browser',
        '#entity_browser' => $entity_browser,
        '#cardinality' => $cardinality,
        '#process' => [
          [
            '\Drupal\entity_browser\Element\EntityBrowserElement',
            'processEntityBrowser',
          ],
          [self::class, 'processEntityBrowser'],
        ],
        '#default_value' => $entities,
      ];

      $order_class = 'entity-browser-block-delta-order';

      $form[$browser]['selection']['table'] = [
        '#type' => 'table',
        '#header' => [
          'Entity',
          'Operations',
        ],
        '#empty' => 'No entities yet',
        '#tabledrag' => [
          [
            'action' => 'order',
            'relationship' => 'sibling',
            'group' => $order_class,
          ],
        ],
        '#process' => [
          [self::class, 'processTable'],
        ],
        '#default_view_modes' => 'default',
      ];
    }
  }

  /**
   * Render API callback: Processes the table element.
   */
  public static function processTable(&$element, FormStateInterface $form_state, &$complete_form) {
    $parents = array_slice($element['#array_parents'], 0, -1);
    $entity_ids = $form_state->getValue(array_merge($parents, ['entity_browser', 'entity_ids']), '');
    $entities = empty($entity_ids) ? [] : EntityBrowserBlock::loadEntitiesByIDs(explode(' ', $entity_ids));
    $browser = array_slice($element['#array_parents'], -3, 1)[0];
    $display_repository = \Drupal::service('entity_display.repository');
    $delta = 0;
    foreach ($entities as $id => $entity) {
      if(empty($entity))
        continue;

      $title = $entity->label();
      if (explode(':', $id)[0] === 'media'){
        $fid = $entity->field_media_image->getValue()[0]['target_id'];
        $file = \Drupal::entityTypeManager()->getStorage('file')->load($fid);
        $uri = $file->getFileUri();
        $style = ImageStyle::load('medium');
        $url = $style->buildUrl($uri);
        $title = "<img src='". $url ."' />";
      }
      $element[$id] = [
        '#attributes' => [
          'data-entity-id' => $id,
        ],
        'title' => ['#markup' => $title],
        'operations' => [
          'remove' => [
            '#type' => 'button',
            '#value' => t('X'),
            '#op' => 'remove',
            '#name' => 'remove_' . $id,
            '#ajax' => [
              'callback' => [self::class, 'updateCallback'],
              'wrapper' => 'entity-browser-block-form_' . $browser,
            ],
          ],
        ],
      ];
      if (isset($element['#default_view_modes'][$id])) {
        $element[$id]['view_mode']['#default_value'] = $element['#default_view_modes'][$id];
      }

      $delta++;
    }
    return $element;
  }

  /**
   * AJAX callback: Re-renders the Entity Browser button/table.
   */
  public static function updateCallback(array &$form, FormStateInterface $form_state) {
    $trigger = $form_state->getTriggeringElement();
    if (isset($trigger['#op']) && $trigger['#op'] === 'remove') {
      $parents = array_slice($trigger['#array_parents'], 0, -4);
      $selection = NestedArray::getValue($form, $parents);
      $id = str_replace('remove_', '', $trigger['#name']);
      unset($selection['table'][$id]);
      $value = explode(' ', $selection['entity_browser']['entity_ids']['#value']);
      $selection['entity_browser']['entity_ids']['#value'] = array_diff($value, [$id]);
    }
    else {
      $parents = array_slice($trigger['#array_parents'], 0, -2);
      $selection = NestedArray::getValue($form, $parents);
    }
    return $selection;
  }

  /**
   * Render API callback: Processes the entity browser element.
   */
  public static function processEntityBrowser(&$element, FormStateInterface $form_state, &$complete_form) {
    $parents = array_slice($element['#array_parents'], -4, 3);
    $browser = array_slice($element['#array_parents'], -3, 1)[0];
    $element['entity_ids']['#ajax'] = [
      'callback' => [self::class, 'updateCallback'],
      'wrapper' => 'entity-browser-block-form_' . $browser,
      'event' => 'entity_browser_value_updated',
    ];
    $element['entity_ids']['#default_value'] = implode(' ', array_keys($element['#default_value']));
    return $element;
  }
}




