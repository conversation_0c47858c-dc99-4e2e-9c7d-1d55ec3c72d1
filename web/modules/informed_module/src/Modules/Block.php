<?php
namespace Drupal\informed_module\Modules;

use <PERSON><PERSON>al\Core\Block\BlockBase;
use <PERSON>upal\Core\Form\FormStateInterface;
use \Drupal\file\Entity\File;

/**
 * Class Block
 * @package Drupal\informed_module\Modules
 */
abstract class Block extends BlockBase {
  /**
   * @param array $configuration
   */
  public function setConfiguration(array $configuration) {
		$configuration['cssDefaults'] = $this->cssDefaults();
		parent::setConfiguration($configuration);
	}

	public function cssDefaults() {
		return false;
	}


  /**
   * @param array $form
   * @param FormStateInterface $form_state
   * @return array
   * just add the advanced settings to blocks here
   */
  public function blockForm($form, FormStateInterface $form_state) {
    /** @var \Drupal\block\BlockInterface $block */
    $form['third_party_settings']['#tree'] = TRUE;
    $config = $this->configuration['third_party_settings']['informed_module'];
    $form['third_party_settings']['informed_module'] = BlockFormStyles::styleOptions($config);
		return $form;
	}

  /**
   * @param array $form
   * @param FormStateInterface $form_state
   * save all values submitted from the block
   */
  public function blockSubmit($form, FormStateInterface $form_state) {
		$values = $form_state->getValues();
		foreach ($values as $index => $value) {
      $entities = !empty($value['selection']['entity_browser']['entities']);
      if ($entities){
        unset($value['selection']['entity_browser']['entities']);
      }

			$this->configuration[$index] = $value;
		}
	}

  public function render($render) {
    $config = $this->configuration['third_party_settings']['informed_module'] ?? false;
    if (isset($config['container']['condition'])){
      $condition_one = $config['container']['condition']['condition_one'] ?? false;
      $operator = $config['container']['condition']['operator'] ?? false;
      $condition_two = $config['container']['condition']['condition_two'] ?? false;
      if (!BlockFormStyles::conditionalRender($condition_one, $operator, $condition_two)){
        return [];
      }
    }
    return $render;
  }


  /**
   * @param $form /pass form by reference
   * @param string $machine_name the machine name you would like the form item to be instead of default
   * @param string $label the custom label you would to use
   */
  public function FieldTextColor(&$form, $machine_name = 'text_color', $label = 'Text Color'){
      $form[$machine_name] = [
          '#type' => 'select',
          '#title' => $this->t($label),
          '#default_value' => $this->configuration[$machine_name],
          '#options' => [
              'color-black' =>  'Black',
              'color-white' =>  'White',
              'color-primary' =>  'Primary',
              'color-secondary' =>  'Secondary',
              'color-tertiary' =>  'Tertiary',
              'color-quaternary' =>  'Quaternary',
              'color-quinary' =>  'Quinary',
              'color-senary' =>  'Senary',
              'color-transparent' =>  'Transparent',
          ],
      ];
  }
  /**
   * @param $form /pass form by reference
   * @param string $machine_name the machine name you would like the form item to be instead of default
   * @param string $label the custom label you would to use
   */
    public function FieldTextHoverColor(&$form, $machine_name = 'text_hover_color', $label = 'Text Hover Color'){
        $form[$machine_name] = [
            '#type' => 'select',
            '#title' => $this->t($label),
            '#default_value' => $this->configuration[$machine_name],
            '#options' => [
                'hover-color-black' =>  'Black',
                'hover-color-white' =>  'White',
                'hover-color-primary' =>  'Primary',
                'hover-color-secondary' =>  'Secondary',
                'hover-color-tertiary' =>  'Tertiary',
                'hover-color-quaternary' =>  'Quaternary',
                'hover-color-quinary' =>  'Quinary',
                'hover-color-senary' =>  'Senary',
                'hover-color-transparent' =>  'Transparent',
            ],
        ];
    }
  /**
   * @param $form /pass form by reference
   * @param string $machine_name the machine name you would like the form item to be instead of default
   * @param string $label the custom label you would to use
   */
  public function FieldBackgroundColor(&$form, $machine_name = 'background_color', $label = 'Background Color'){
    $form[$machine_name] = [
      '#type' => 'select',
      '#title' => $this->t($label),
      '#default_value' => $this->configuration[$machine_name],
      '#options' => [
        'bg-color-black' =>  'Black',
        'bg-color-white' =>  'White',
        'bg-color-primary' =>  'Primary',
        'bg-color-secondary' =>  'Secondary',
        'bg-color-tertiary' =>  'Tertiary',
        'bg-color-quaternary' =>  'Quaternary',
        'bg-color-quinary' =>  'Quinary',
        'bg-color-senary' =>  'Senary',
        'bg-color-transparent' =>  'Transparent',
      ],
    ];
  }
  /**
   * @param $form /pass form by reference
   * @param string $machine_name the machine name you would like the form item to be instead of default
   * @param string $label the custom label you would to use
   */
  public function FieldTextAlign(&$form, $machine_name = 'text_align', $label = 'Text Align'){
    $form[$machine_name] = [
      '#type' => 'select',
      '#title' => $this->t($label),
      '#default_value' => $this->configuration[$machine_name],
      '#options' => [
        'text-center' =>  'Center',
        'text-left' =>  'Left',
        'text-right' =>  'Right',
      ],
    ];
  }

}
