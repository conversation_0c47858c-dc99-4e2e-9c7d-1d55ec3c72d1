<?php /** @noinspection PhpDuplicateArrayKeysInspection */

/**
 * @file Link.php
 */
namespace Drupal\informed_module\Modules;

use Drupal\Core\Form\FormStateInterface;
use Dr<PERSON>al\informed_module\Helpers\EntityBrowserFormField;
use Drupal\informed_module\Plugin\Layout\FoundationLayout;

/**
 * Class BlockFormStyles
 * @package Drupal\informed_module\Modules
 */
class BlockFormStyles {

  /**
   * @param $config
   * @return array
   * @throws \Drupal\Component\Plugin\Exception\InvalidPluginDefinitionException
   * @throws \Drupal\Component\Plugin\Exception\PluginNotFoundException
   * set up the advanced options
   */
  public static function styleOptions($config) {
        // get all the values needed to put in the default values of the form items
        //$media_item = \Drupal::entityTypeManager()->getStorage('media')->load($config['container']['background']['image']);
	  	$bg_image_style = $config['container']['background']['image_style'] ?? 'original';
	  	$image_style = \Drupal::entityTypeManager()->getStorage('image_style')->load($bg_image_style);

        $header_ids = FoundationLayout::termsToArray($config['container']['header']['header_classes']);
        $header_classes = \Drupal::entityTypeManager()->getStorage('taxonomy_term')->loadMultiple($header_ids);

        $wrapper_ids = FoundationLayout::termsToArray($config['container']['containers']['wrapper_container']);
        $wrapper_classes = \Drupal::entityTypeManager()->getStorage('taxonomy_term')->loadMultiple($wrapper_ids);

        $inner_wrapper_ids = FoundationLayout::termsToArray($config['container']['containers']['inner_wrapper_container']);
        $inner_wrapper_classes = \Drupal::entityTypeManager()->getStorage('taxonomy_term')->loadMultiple($inner_wrapper_ids);

        $form_options = [
          'container' => [
            '#weight' => 0,
            '#type' => 'details',
            '#title' => 'Advanced Settings',
            'visibility' => [
              '#type' => 'details',
              '#title' => 'Visibility',
              'visibility' => [
                '#type' => 'checkboxes',
                '#description' => 'Check screens where you would like to hide this element.',
                '#options' => [
                  'hide-for-small-only' => 'Hide For Small',
                  'hide-for-medium-only' => 'Hide For Medium',
                  'hide-for-large-only' => 'Hide For Large',
                  'hide-for-xlarge-only' => 'Hide For XLarge',
                  'hide-for-xxlarge-only' => 'Hide For XXLarge',
                ],
                '#default_value' => $config['container']['visibility']['visibility'],
              ],
            ],
            'header' => [
              '#type' => 'details',
              '#title' => 'Header',
              'header_classes' => [
                '#type' => 'entity_autocomplete',
                '#title' => 'Header Container Classes',
                '#description' => 'Tag as many classes you would like to be on the header container.',
                // '#title_display' => 'invisible',
                '#default_value' => $header_classes,
                '#target_type' => 'taxonomy_term',
                '#selection_settings' => [
                  'target_bundles' => ['classes'], // your content type
                ],
                '#tags' => true,
                '#maxlength' => 1000,
              ],
              'header_position' => [
                '#type' => 'select',
                '#title' => 'Header Position',
                '#options' => [
                  'header-above' => 'Above',
//                  'header-below' => 'Below',
                  'header-inline' => 'Inline',
                ],
                '#default_value' => $config['container']['header']['header_position'],
              ],
              'header_size' => [
                '#type' => 'select',
                '#title' => 'Header Size',
                '#options' => [
                  'h2' => 'Default',
                  'h1' => 'h1',
                  'h2' => 'h2',
                  'h3' => 'h3',
                  'h4' => 'h4',
                  'h5' => 'h5',
                  'h6' => 'h6',
                ],
                '#default_value' => $config['container']['header']['header_size'],
              ],
            ],
            'background' => [
              '#type' => 'details',
              '#title' => 'Background',
              '#open' => FALSE,
//              'image' => [
//                '#type' => 'entity_autocomplete',
//                '#title' => 'Image',
//                '#description' => 'Select an Image.',
//                // '#title_display' => 'invisible',
//                '#default_value' => $media_item,
//                '#target_type' => 'media',
//                '#selection_settings' => [
//                  'target_bundles' => ['image'], // your content type
//                ],
//                '#maxlength' => 1000,
//              ],
              'image_style' => [
                '#type' => 'entity_autocomplete',
                '#title' => 'Image Style',
                '#description' => 'Select an a image style to render your slides in.',
                // '#title_display' => 'invisible',
                '#default_value' => $image_style,
                '#target_type' => 'image_style',
                '#maxlength' => 1000,
              ],
            ],
            'containers' => [
              '#type' => 'details',
              '#title' => 'Containers',
              '#open' => FALSE,
              'wrapper_container' => [
                '#type' => 'entity_autocomplete',
                '#title' => 'Wrapper Container Classes',
                '#description' => 'Tag as many classes you would like to be on the wrapper container.',
                // '#title_display' => 'invisible',
                '#default_value' => $wrapper_classes,
                '#target_type' => 'taxonomy_term',
                '#selection_settings' => [
                  'target_bundles' => ['classes'], // your content type
                ],
                '#tags' => true,
                '#maxlength' => 1000,
              ],
              'inner_wrapper_container' => [
                '#type' => 'entity_autocomplete',
                '#title' => 'Inner Wrapper Container Classes',
                '#description' => 'Tag as many classes you would like to be on the inner wrapper container.',
                // '#title_display' => 'invisible',
                '#default_value' => $inner_wrapper_classes,
                '#target_type' => 'taxonomy_term',
                '#selection_settings' => [
                  'target_bundles' => ['classes'], // your content type
                ],
                '#tags' => true,
                '#maxlength' => 1000,
              ],
              'wrapper_container_inline_styles' => [
                '#type' => 'textarea',
                '#rows' => 10,
                '#title' => 'Wrapper inline Styles',
                '#description' => 'Use this to add inline styles to the wrapper of this block.',
                // '#title_display' => 'invisible',
                '#default_value' => $config['container']['containers']['wrapper_container_inline_styles'],
              ],
            ],
            'condition' => [
              '#type' => 'details',
              '#title' => 'Conditional Display (Show if True)',
              'condition_one' => [
                '#type' => 'textfield',
                '#title' => 'Condition One',
                '#description' => 'Enter a token to evaluate with.',
                '#default_value' => $config['container']['condition']['condition_one'],
              ],
              'operator' => [
                '#type' => 'select',
                '#title' => 'Operator',
                '#description' => 'Select an operator.',
                '#default_value' => $config['container']['condition']['operator'],
                '#options' => [
                  '=' => '=',
                  '>' => '>',
                  '<' => '<',
                ],
              ],
              'condition_two' => [
                '#type' => 'textfield',
                '#title' => 'Condition Two',
                '#description' => 'Enter a token to evaluate with.',
                '#default_value' => $config['container']['condition']['condition_two'],
              ],
            ],

          ],
        ];

        // add a entity browser field to handle background of a block
        EntityBrowserFormField::generateEntityBrowsers($form_options['container']['background'], $config['container']['background'], ['image'], 'images', 1, 'container');

        return $form_options;
    }

    public static function conditionalRender($condition_one = '', $operator = '', $condition_two = ''): bool
    {
      $node = \Drupal::routeMatch()->getParameter('node') ?? '';
      $render = true;
      if ($node){
        if ($condition_one && $operator && $condition_two){
          $render = false;
          $condition_one =\Drupal::token()->replace($condition_one, ['node' => $node]);
          $condition_two =\Drupal::token()->replace($condition_two, ['node' => $node]);
          switch ($operator){
            case '=':
              $render = ($condition_one === $condition_two);
              break;
            case '>':
              $render = ($condition_one > $condition_two);
              break;
            case '<':
              $render = ($condition_one < $condition_two);
              break;
          }
        }
      }

      return $render;
    }

}
