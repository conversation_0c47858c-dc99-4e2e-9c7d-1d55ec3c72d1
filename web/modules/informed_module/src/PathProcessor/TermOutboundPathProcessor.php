<?php
namespace Drupal\informed_module\PathProcessor;

use Drupal\Core\PathProcessor\OutboundPathProcessorInterface;
use Drupal\Core\Render\BubbleableMetadata;
use Symfony\Component\HttpFoundation\Request;

 class TermOutboundPathProcessor implements OutboundPathProcessorInterface {

  public function processOutbound($path, &$options = array(), Request $request = NULL, BubbleableMetadata $bubbleable_metadata = NULL) {
    $route = \Drupal::routeMatch()->getRouteObject();
    if ($route) {
      $current_is_admin = \Drupal::service('router.admin_context')->isAdminRoute($route);
      $router = \Drupal::service('router.no_access_checks');
      if (\Drupal::service('path.validator')->isValid($path)) {
        try {
          $outbound_route = $router->match($path);
          $outbound_is_admin = \Drupal::service('router.admin_context')->isAdminRoute($outbound_route['_route_object']);
          if (!$current_is_admin && !$outbound_is_admin && isset($outbound_route['taxonomy_term'])) {
            $term = $outbound_route['taxonomy_term'];
            $config = \Drupal::config('informed_module.term_search_path');
            if ($config && $term) {
              $taxonomies = $config->get('taxonomies');
              if (is_array($taxonomies)) {
                $taxonomies = array_filter($taxonomies);
                $search_url = $config->get('search_url');
                $vocabulary = $term->bundle();
                $term_id = $term->id();
                if (in_array($vocabulary, $taxonomies) && $search_url) {
                  $path = "$search_url";
                  $options['query'][$vocabulary][$term_id] = $term_id;
                }
              }
            }
          }
        }
        catch (\Symfony\Component\Routing\Exception\MethodNotAllowedException $e) {
          // If we get a MethodNotAllowedException, it means the route exists but doesn't support GET.
          // In this case, we can't process it as a taxonomy term route, so we just return the original path.
        }
        catch (\Exception $e) {
          // For any other exceptions, just continue with the original path
        }
      }
    }

     return $path;
   }
}
