<?php

namespace Drupal\informed_module\Plugin\Block;


use <PERSON><PERSON><PERSON>\Core\Form\FormStateInterface;
use <PERSON><PERSON>al\informed_module\Modules\Block;
use <PERSON><PERSON>al\informed_module\Modules\BlockFormStyles;


/**
 * @Block(
 * id = "body",
 * admin_label = @Translation("Body"),
 * )
 */

class Body extends Block {

    public function defaultConfiguration() {
        return array(

        );
    }
    public function blockForm($form, FormStateInterface $form_state) {

        $form = parent::blockForm($form, $form_state);

        return $form;
    }

    public function blockSubmit ($form, FormStateInterface $form_state){
        parent::blockSubmit($form, $form_state);
        $this->configuration['title'] = $form['settings']['label']['#value'];
    }

    public function build() {
        $node = \Drupal::routeMatch()->getParameter('node');
        if ($node instanceof \Drupal\node\NodeInterface) {
          // You can get nid and anything else you need from the node object.
          $content = $node->body->value;
        } else {
          return ['#markup' => '
            <h4>Subtitle Text Goes Here</h4>

            <h5 class="font-weight-normal"><span style="font-family:Titillium Web,sans-serif;">Some important descriptive text about a product or service goes in this location, though it doesn’t need to appear here if there’s no reason for it to exist.</span></h5>

            <img src="https://via.placeholder.com/1130X565"/>

            <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse nec justo et enim feugiat consequat. Nulla varius consectetur massa at feugiat. Morbi at massa nec augue tempor condimentum quis quis augue. In non velit quis urna porttitor porttitor et sit amet libero. Maecenas vitae ipsum ultricies, varius dolor sit amet, gravida elit. Morbi sit amet convallis diam. Cras sapien nulla, luctus eget erat vel, fringilla pharetra diam. In eu dignissim quam. Donec ornare leo in ornare commodo. Sed pharetra finibus nunc, sit amet rhoncus dui bibendum id. Maecenas ac risus lorem. Pellentesque sagittis vulputate turpis, et dapibus enim eleifend non. Vivamus sed varius justo.</p>

            <p>Nullam pretium ac orci vitae iaculis. Etiam mattis neque ac quam sollicitudin, eget scelerisque ligula maximus. Nulla vel venenatis diam. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Fusce tempor consectetur feugiat. Aliquam facilisis lectus ac maximus tincidunt. Nullam venenatis dolor eget ultricies sodales. Morbi non ultrices ex. Phasellus condimentum ligula ipsum, nec laoreet felis mollis in. Mauris elit diam, egestas et hendrerit nec, convallis posuere mauris. Phasellus quis turpis quam. Duis sapien dolor, pulvinar facilisis ullamcorper vitae, interdum non orci. Morbi sit amet convallis diam. Cras sapien nulla, luctus eget erat vel, fringilla pharetra diam. In eu dignissim quam.</p>

            <p>Nunc imperdiet dignissim laoreet. Sed auctor, tortor ac fringilla vestibulum, lacus libero eleifend tellus, sed posuere neque</p>

            <p><a class="button button-color-primary hover-color-white color-black" href="#" title="placeholder">BUTTON LINK</a></p>
          '];
        }
        $render = [
            '#theme' => 'wysiwyg',
            '#content' => [
              '#type' => 'processed_text',
              '#text' => $content ?? '',
              '#format' => 'full_html',
            ],
        ];

      return parent::render($render);
    }
}
