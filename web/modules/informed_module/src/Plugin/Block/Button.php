<?php

namespace Drupal\informed_module\Plugin\Block;


use <PERSON>upal\Core\Form\FormStateInterface;
use <PERSON><PERSON>al\informed_module\Helpers\EntityBrowserFormField;
use Drupal\informed_module\Modules\Block;
use Drupal\informed_module\Modules\BlockFormStyles;


/**
 * @Block(
 * id = "button",
 * admin_label = @Translation("Button"),
 * )
 */

class Button extends Block {

    public function defaultConfiguration() {
        return array(

        );
    }
    public function blockForm($form, FormStateInterface $form_state) {
        $form['text'] = [
            '#type' => 'textfield',
            '#title' => $this->t('Button Text'),
            '#description' => $this->t('Can handle NODE level tokens.'),
            '#default_value' => $this->configuration['text'],
        ];

        $form['icon_class'] = [
            '#type' => 'textfield',
            '#title' => $this->t('Icon Class'),
            '#description' => $this->t('You can paste in any font awesome icon class here (fas fa-arrow-right). Or custom ones.'),
            '#default_value' => $this->configuration['icon_class'],
        ];

        $form['icon_size'] = [
            '#type' => 'textfield',
            '#title' => $this->t('Icon Size'),
            '#description' => $this->t('Can be any font-size unit px, %, em, rem.'),
            '#default_value' => $this->configuration['icon_size'],
        ];

        $internal_link = \Drupal::entityTypeManager()->getStorage('node')->load($this->configuration['internal_link']);

        $form['internal_link'] = [
            '#type' => 'entity_autocomplete',
            '#title' => $this->t('Internal Link'),
            '#description' => $this->t('You can use this field to reference an internal page to this site.'),
            // '#title_display' => 'invisible',
            '#default_value' => $internal_link,
            '#target_type' => 'node',
            '#maxlength' => 1000,
        ];

        $form['external_link'] = [
            '#type' => 'textfield',
            '#title' => $this->t('External Link'),
            '#description' => $this->t('Paste in an external link here. Can handle NODE level tokens.'),
            '#default_value' => $this->configuration['external_link'],
        ];

      $form['open_new_window'] = [
        '#type' => 'checkbox',
        '#title' => $this->t('Open New Window'),
        '#default_value' => $this->configuration['open_new_window'],
      ];

//        black
//        white
//        primary
//        secondary
//        tertiary
//        quaternary
//        quinary
//        senary
//        transparent

        $form['text_color'] = [
            '#type' => 'select',
            '#title' => $this->t('Text Color'),
            '#default_value' => $this->configuration['text_color'],
            '#options' => [
                'color-black' =>  'Black',
                'color-white' =>  'White',
                'color-primary' =>  'Primary',
                'color-secondary' =>  'Secondary',
                'color-tertiary' =>  'Tertiary',
                'color-quaternary' =>  'Quaternary',
                'color-quinary' =>  'Quinary',
                'color-senary' =>  'Senary',
                'color-transparent' =>  'Transparent',
            ],
        ];

        $form['background_color'] = [
            '#type' => 'select',
            '#title' => $this->t('Background Color'),
            '#default_value' => $this->configuration['background_color'],
            '#options' => [
                'bg-color-black' =>  'Black',
                'bg-color-white' =>  'White',
                'bg-color-primary' =>  'Primary',
                'bg-color-secondary' =>  'Secondary',
                'bg-color-tertiary' =>  'Tertiary',
                'bg-color-quaternary' =>  'Quaternary',
                'bg-color-quinary' =>  'Quinary',
                'bg-color-senary' =>  'Senary',
                'bg-color-transparent' =>  'Transparent',
            ],
        ];

        $form['hover_color'] = [
            '#type' => 'select',
            '#title' => $this->t('Text Hover Color'),
            '#default_value' => $this->configuration['hover_color'],
            '#options' => [
                'hover-color-black' =>  'Black',
                'hover-color-white' =>  'White',
                'hover-color-primary' =>  'Primary',
                'hover-color-secondary' =>  'Secondary',
                'hover-color-tertiary' =>  'Tertiary',
                'hover-color-quaternary' =>  'Quaternary',
                'hover-color-quinary' =>  'Quinary',
                'hover-color-senary' =>  'Senary',
                'hover-color-transparent' =>  'Transparent',
            ],
        ];

        $form['hover_background_color'] = [
            '#type' => 'select',
            '#title' => $this->t('Background Hover Color'),
            '#default_value' => $this->configuration['hover_background_color'],
            '#options' => [
                'hover-bg-color-black' =>  'Black',
                'hover-bg-color-white' =>  'White',
                'hover-bg-color-primary' =>  'Primary',
                'hover-bg-color-secondary' =>  'Secondary',
                'hover-bg-color-tertiary' =>  'Tertiary',
                'hover-bg-color-quaternary' =>  'Quaternary',
                'hover-bg-color-quinary' =>  'Quinary',
                'hover-bg-color-senary' =>  'Senary',
                'hover-bg-color-transparent' =>  'Transparent',
            ],
        ];

        $form = parent::blockForm($form, $form_state);

        return $form;
    }

    public function blockSubmit ($form, FormStateInterface $form_state){
        parent::blockSubmit($form, $form_state);
        $this->configuration['title'] = $form['settings']['label']['#value'];
    }

    public function build() {
        $node = \Drupal::routeMatch()->getParameter('node') ?? '';
        $settings = [
            'text' => \Drupal::token()->replace($this->configuration['text'], ['node' => $node]),
            'icon_class' => $this->configuration['icon_class'] ?? '',
            'icon_size' => $this->configuration['icon_size'] ?? '',
            'internal_link' => $this->configuration['internal_link'] ?? '',
            'external_link' => \Drupal::token()->replace($this->configuration['external_link'], ['node' => $node]),
            'open_new_window' => $this->configuration['open_new_window'] ?? '',
            'text_color' => $this->configuration['text_color'] ?? '',
            'background_color' => $this->configuration['background_color'] ?? '',
            'hover_color' => $this->configuration['hover_color'] ?? '',
            'hover_background_color' => $this->configuration['hover_background_color'] ?? '',
        ];

        $render = [
            '#theme' => 'button_block',
            '#settings' => $settings,
        ];

      return parent::render($render);
    }
}
