<?php

namespace Drupal\informed_module\Plugin\Block;


use <PERSON><PERSON>al\Core\Form\FormStateInterface;
use <PERSON><PERSON>al\informed_module\Modules\Block;
use <PERSON><PERSON>al\informed_module\Modules\BlockFormStyles;


/**
 * @Block(
 * id = "global_layout_renderer",
 * admin_label = @Translation("Global Layout Renderer"),
 * )
 */

class GlobalLayoutRenderer extends Block {

    public function defaultConfiguration() {
        return array(

        );
    }
    public function blockForm($form, FormStateInterface $form_state) {

        $global_layout = \Drupal::entityTypeManager()->getStorage('node')->load($this->configuration['global_layout']);
        $form['global_layout'] = [
            '#type' => 'entity_autocomplete',
            '#title' => $this->t('Choose a global layout'),
            '#description' => $this->t('This will render the chosen layout as it displays on the view page of that node.'),
            '#default_value' => $global_layout,
            '#target_type' => 'node',
            '#selection_settings' => [
                'target_bundles' => ['global_layout'], // your content type
            ],
            '#maxlength' => 1000,
        ];

        $form = parent::blockForm($form, $form_state);

        return $form;
    }

    public function blockSubmit ($form, FormStateInterface $form_state){
        parent::blockSubmit($form, $form_state);
    }

    public function build() {

        $global_layout = \Drupal::entityTypeManager()->getStorage('node')->load($this->configuration['global_layout']);
        if(!$global_layout){ return ['#markup' => 'Please Enter a valid global layout reference.'];}
        $view = \Drupal::entityTypeManager()->getViewBuilder('node')->view($global_layout);
        $content = \Drupal::service('renderer')->render($view);

        $render = [
            '#theme' => 'layout_renderer',
            '#content' => $content,
        ];

        return $render;
    }
}
