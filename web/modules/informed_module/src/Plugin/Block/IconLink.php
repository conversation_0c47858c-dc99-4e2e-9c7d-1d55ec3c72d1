<?php

namespace Drupal\informed_module\Plugin\Block;


use Drupal\Core\Form\FormStateInterface;
use <PERSON><PERSON>al\informed_module\Helpers\EntityBrowserFormField;
use Drupal\informed_module\Modules\Block;
use Drupal\informed_module\Modules\BlockFormStyles;


/**
 * @Block(
 * id = "icon_link",
 * admin_label = @Translation("Icon Link"),
 * )
 */

class IconLink extends Block {

    public function defaultConfiguration() {
        return array(

        );
    }
    public function blockForm($form, FormStateInterface $form_state) {

        $media_item = \Drupal::entityTypeManager()->getStorage('media')->load($this->configuration['icon']);
        $image_style = \Drupal::entityTypeManager()->getStorage('image_style')->load($this->configuration['image_style']);

        $form['url'] = [
          '#type' => 'textfield',
          '#required' => 'true',
          '#title' => $this->t('Url'),
          '#description' => $this->t('Enter a valid url either internal or external.'),
          // '#title_display' => 'invisible',
          '#default_value' => $this->configuration['url'],
          '#maxlength' => 1000,
        ];
        // @todo: provide an entity reference view that will render the preview icon
        // @todo: export configuration and save in here for the install
        $form['icon'] = [
            '#type' => 'entity_autocomplete',
            '#required' => 'true',
            '#title' => $this->t('Icon'),
            '#description' => $this->t('Select an Icon.'),
            // '#title_display' => 'invisible',
            '#default_value' => $media_item,
            '#target_type' => 'media',
            '#selection_settings' => [
              'target_bundles' => ['icon'], // your content type
            ],
            '#maxlength' => 1000,
        ];

        $form['image_style'] = [
          '#type' => 'entity_autocomplete',
          '#required' => 'true',
          '#title' => $this->t('Image Style'),
          '#description' => $this->t('Select an a image style to render your slides in.'),
          // '#title_display' => 'invisible',
          '#default_value' => $image_style,
          '#target_type' => 'image_style',
          '#maxlength' => 1000,
        ];

        $form = parent::blockForm($form, $form_state);

        return $form;
    }

    public function blockSubmit ($form, FormStateInterface $form_state){
        parent::blockSubmit($form, $form_state);
        $this->configuration['title'] = $form['settings']['label']['#value'];
    }

    public function build() {
        $media_item = \Drupal::entityTypeManager()->getStorage('media')->load($this->configuration['icon']);

        if(!$this->configuration['icon'] || !$media_item){ return ['#markup' => 'Please Enter a valid media reference.'];}
        if(!$this->configuration['image_style']){ return ['#markup' => 'Please Enter a valid Image Style.'];}
        $render = [
            '#theme' => 'icon_link',
            '#settings' => [
              'title' => $this->configuration['title'],
              'icon' => $media_item,
              'image_style' => $this->configuration['image_style'],
              'url' => $this->configuration['url'],
            ],
        ];

      return parent::render($render);
    }
}
