<?php

namespace Drupal\informed_module\Plugin\Block;


use <PERSON><PERSON>al\Core\Form\FormStateInterface;
use <PERSON><PERSON><PERSON>\entity_browser_block\Plugin\Block\EntityBrowserBlock;
use Drupal\informed_module\Helpers\EntityBrowserFormField;
use Dr<PERSON>al\informed_module\Modules\Block;
use Drupal\informed_module\Modules\BlockFormStyles;


/**
 * @Block(
 * id = "image",
 * admin_label = @Translation("Image"),
 * )
 */

class Image extends Block {

    public function defaultConfiguration() {
        return array(

        );
    }
    public function blockForm($form, FormStateInterface $form_state) {

        $image_style = \Drupal::entityTypeManager()->getStorage('image_style')->load($this->configuration['image_style']);
        EntityBrowserFormField::generateEntityBrowsers($form, $this->configuration, ['image'], 'images', 1, 'container');
        $form['image_style'] = [
          '#type' => 'entity_autocomplete',
          '#required' => 'true',
          '#title' => $this->t('Image Style'),
          '#description' => $this->t('Select an a image style to render your slides in.'),
          // '#title_display' => 'invisible',
          '#default_value' => $image_style,
          '#target_type' => 'image_style',
          '#maxlength' => 1000,
        ];

      $form['full_width'] = [
        '#type' => 'checkbox',
        '#title' => $this->t('Full Width'),
        '#description' => $this->t('Force image to 100% width. Upscaling like this can make the image pixelated.'),
        // '#title_display' => 'invisible',
        '#default_value' => $this->configuration['full_width'],
      ];

        $form = parent::blockForm($form, $form_state);

        return $form;
    }

    public function blockSubmit ($form, FormStateInterface $form_state){
        parent::blockSubmit($form, $form_state);
        $this->configuration['title'] = $form['settings']['label']['#value'];
    }

    public function build() {
        $media_item = $this->configuration['image']['selection']['entity_browser']['entity_ids'] ?? false;
        if (is_string($media_item)){
          $media_item = explode(':',$media_item)[1];
        }

        if (is_numeric($media_item)){
          $media_item = \Drupal::entityTypeManager()->getStorage('media')->load($media_item);
        }

        if (!$media_item){
          return ['#markup' => 'Please Enter a valid media reference.'];
        }

        if(!$this->configuration['image_style']){ return ['#markup' => 'Please Enter a valid Image Style.'];}
        $render = [
            '#theme' => 'image_block',
            '#settings' => [
              'image' => $media_item,
              'image_style' => $this->configuration['image_style'] ?? 'none',
              'full_width' => $this->configuration['full_width'] ?? false,
            ],
        ];

      return parent::render($render);
    }
}
