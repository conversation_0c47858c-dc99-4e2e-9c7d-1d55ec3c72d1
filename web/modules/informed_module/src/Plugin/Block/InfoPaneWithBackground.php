<?php

namespace Drupal\informed_module\Plugin\Block;


use Drupal\Core\Form\FormStateInterface;
use Drupal\informed_module\Helpers\EntityBrowserFormField;
use Drupal\informed_module\Modules\Block;
use Drupal\informed_module\Modules\BlockFormStyles;


/**
 * @Block(
 * id = "info_pane_with_background",
 * admin_label = @Translation("Info Pane With Background"),
 * )
 */

class InfoPaneWithBackground extends Block {

  public function defaultConfiguration() {
    return array(

    );
  }
  public function blockForm($form, FormStateInterface $form_state) {

    $image_style = \Drupal::entityTypeManager()->getStorage('image_style')->load($this->configuration['image_style']);
    $icon_style = \Drupal::entityTypeManager()->getStorage('image_style')->load($this->configuration['icon_style']);
    EntityBrowserFormField::generateEntityBrowsers($form, $this->configuration, ['icon'], 'images', 1, 'details');
    EntityBrowserFormField::generateEntityBrowsers($form, $this->configuration, ['image'], 'images', 1, 'details');

    $form['icon_style'] = [
      '#type' => 'entity_autocomplete',
      '#required' => 'true',
      '#title' => $this->t('Icon Style'),
      '#description' => $this->t('Select an a image style to render in.'),
      // '#title_display' => 'invisible',
      '#default_value' => $icon_style,
      '#target_type' => 'image_style',
      '#maxlength' => 1000,
    ];

    $form['image_style'] = [
      '#type' => 'entity_autocomplete',
      '#required' => 'true',
      '#title' => $this->t('Image Style'),
      '#description' => $this->t('Select an a image style to render in.'),
      // '#title_display' => 'invisible',
      '#default_value' => $image_style,
      '#target_type' => 'image_style',
      '#maxlength' => 1000,
    ];



    $form['text'] = [
      '#type' => 'textfield',
      '#title' => $this->t('Button Text'),
      '#description' => $this->t('Can handle NODE level tokens.'),
      '#default_value' => $this->configuration['text'],
    ];

    $form['external_link'] = [
      '#type' => 'textfield',
      '#title' => $this->t('External Link'),
      '#description' => $this->t('Paste in an external link here. Can handle NODE level tokens.'),
      '#default_value' => $this->configuration['external_link'],
    ];

    $form['open_new_window'] = [
      '#type' => 'checkbox',
      '#title' => $this->t('Open New Window'),
      '#default_value' => $this->configuration['open_new_window'],
    ];

    $form['gradiant'] = [
      '#type' => 'textfield',
      '#title' => $this->t('Gradiant'),
      '#description' => $this->t('Enter a linear gradient code.'),
      '#default_value' => $this->configuration['gradiant'],
    ];

    $form['medium_down_margin'] = [
      '#type' => 'number',
      '#title' => $this->t('Margin Bottom'),
      '#description' => $this->t('this controls the bottom margin of the image side of the info panel. example "-120"'),
      '#default_value' => $this->configuration['medium_down_margin'],
    ];

    $form['layout'] = [
      '#type' => 'select',
      '#title' => $this->t('Layout'),
      '#default_value' => $this->configuration['layout'],
      '#options' => [
        '0' => 'Left',
        '1' => 'Right'
      ],
    ];

    $form['background_color'] = [
      '#type' => 'select',
      '#title' => $this->t('Background Color'),
      '#default_value' => $this->configuration['background_color'],
      '#options' => [
        'bg-color-black' =>  'Black',
        'bg-color-white' =>  'White',
        'bg-color-primary' =>  'Primary',
        'bg-color-secondary' =>  'Secondary',
        'bg-color-tertiary' =>  'Tertiary',
        'bg-color-quaternary' =>  'Quaternary',
        'bg-color-quinary' =>  'Quinary',
        'bg-color-senary' =>  'Senary',
        'bg-color-transparent' =>  'Transparent',
      ],
    ];

    $form['subtitle'] = [
      '#type' => 'text_format',
      '#format' => 'full_html',
      '#title' => $this->t('Subtitle'),
      '#description' => $this->t('Enter markup.'),
      '#default_value' => $this->configuration['subtitle']['value'],
    ];

    $form['body'] = [
      '#type' => 'text_format',
      '#format' => 'full_html',
      '#title' => $this->t('Body'),
      '#description' => $this->t('Enter markup.'),
      '#default_value' => $this->configuration['body']['value'],
    ];

    $form = parent::blockForm($form, $form_state);

    return $form;
  }

  public function blockSubmit ($form, FormStateInterface $form_state){
    parent::blockSubmit($form, $form_state);
    $this->configuration['title'] = $form['settings']['label']['#value'];
  }

  public function build() {
    $node = \Drupal::routeMatch()->getParameter('node') ?? '';

    $settings = [
      'text' => \Drupal::token()->replace($this->configuration['text'], ['node' => $node]),
      'external_link' => \Drupal::token()->replace($this->configuration['external_link'], ['node' => $node]),
      'open_new_window' => $this->configuration['open_new_window'] ?? '',
      'background_color' => $this->configuration['background_color'] ?? '',
      'gradiant' => $this->configuration['gradiant'] ?? '',
      'medium_down_margin' => $this->configuration['medium_down_margin'] ?? '',
      'layout' => $this->configuration['layout'] ?? '',
      'image' => $this->getMediaItem('image'),
      'image_style' => $this->configuration['image_style'] ?? 'none',
      'icon' => $this->getMediaItem('icon'),
      'icon_style' => $this->configuration['icon_style'] ?? 'none',
      'body' => [
        '#type' => 'processed_text',
        '#text' => \Drupal::token()->replace($this->configuration['body']['value'], ['node' => $node]),
        '#format' => 'full_html',
      ],
      'subtitle' => [
        '#type' => 'processed_text',
        '#text' => \Drupal::token()->replace($this->configuration['subtitle']['value'], ['node' => $node]),
        '#format' => 'full_html',
      ],
    ];

    $render = [
      '#theme' => 'info_panel_with_background',
      '#settings' => $settings,
    ];

    return parent::render($render);
  }

  public function getMediaItem($field_name){
    $media_item = $this->configuration[$field_name]['selection']['entity_browser']['entity_ids'] ?? false;
    if (is_string($media_item)){
      $media_item = explode(':',$media_item)[1];
    }

    if (is_numeric($media_item)){
      $media_item = \Drupal::entityTypeManager()->getStorage('media')->load($media_item);
    }

    if (!$media_item){
      return false;
    }

    if(!$this->configuration['image_style']){ return false;}

    return $media_item;
  }
}
