<?php

namespace Drupal\informed_module\Plugin\Block;

use Drupal\Component\Plugin\Factory\DefaultFactory;
use Dr<PERSON>al\Component\Utility\NestedArray;
use Drupal\Core\Access\AccessResult;
use Drupal\Core\Block\Annotation\Block;
use Drupal\Core\Block\BlockBase;
use Drupal\Core\Cache\CacheableMetadata;
use Drupal\Core\Entity\EntityDisplayBase;
use Drupal\Core\Entity\EntityFieldManagerInterface;
use Drupal\Core\Entity\FieldableEntityInterface;
use Drupal\Core\Extension\ModuleHandlerInterface;
use Drupal\Core\Field\FieldDefinitionInterface;
use Drupal\Core\Field\FormatterInterface;
use Drupal\Core\Field\FormatterPluginManager;
use Drupal\Core\Form\EnforcedResponseException;
use Drupal\Core\Form\FormHelper;
use Drupal\Core\Form\FormStateInterface;
use Drupal\Core\Plugin\ContainerFactoryPluginInterface;
use Drupal\Core\Plugin\ContextAwarePluginInterface;
use Drupal\Core\Session\AccountInterface;
use Drupal\Core\StringTranslation\TranslatableMarkup;
use Drupal\informed_module\Modules\BlockFormStyles;
use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Drupal\layout_builder\Plugin\Block\FieldBlock;

/**
 * Provides a block that renders a field from an entity.
 *
 * @Block(
 *   id = "informed_field_block",
 *   deriver = "\Drupal\layout_builder\Plugin\Derivative\FieldBlockDeriver",
 * )
 *
 * @internal
 *   Plugin classes are internal.
 */
class InformedFieldBlock extends FieldBlock {

  /**
   * {@inheritdoc}
   */
  public function build() {
    $config = $this->getConfiguration();
    $build = parent::build();

	$build[0]['#third_party_settings']['layout_builder']['label_text'] = $config['formatter']['label_text'];
	$build[0]['#third_party_settings']['layout_builder']['label_size'] = $config['formatter']['label_size'];
	$build[0]['#third_party_settings']['layout_builder']['item_wrapper'] = $config['formatter']['item_wrapper'];
	$build[0]['#third_party_settings']['layout_builder']['item_separator'] = $config['formatter']['item_separator'];
	$build[0]['#third_party_settings']['layout_builder']['item_wrapper_classes'] = $config['formatter']['item_wrapper_classes'];
	$build[0]['#third_party_settings']['layout_builder']['item_classes'] = $config['formatter']['item_classes'];
	$build[0]['#third_party_settings']['layout_builder']['item_sort'] = $config['formatter']['item_sort'] ?? '';

    $third_party_settings = $config['third_party_settings']['informed_module'] ?? false;
    if ($third_party_settings && isset($third_party_settings['container']['condition'])){
      $condition_one = $third_party_settings['container']['condition']['condition_one'] ?? false;
      $operator = $third_party_settings['container']['condition']['operator'] ?? false;
      $condition_two = $third_party_settings['container']['condition']['condition_two'] ?? false;
      if (!BlockFormStyles::conditionalRender($condition_one, $operator, $condition_two)){
        return [];
      }
    }

    return $build;
  }

  /**
   * {@inheritdoc}
   */
  public function blockForm($form, FormStateInterface $form_state) {
    $config = $this->getConfiguration();
    $type = $this->getFieldDefinition()->getType();
    $field_name = $this->getFieldDefinition()->getName();
    $bundle = $this->getFieldDefinition()->getTargetBundle();
    $entity_type = $this->getFieldDefinition()->getTargetEntityTypeId();
    $bundle_fields = \Drupal::getContainer()->get('entity_field.manager')->getFieldDefinitions($entity_type, $bundle);
    $field_definition = $bundle_fields[$field_name];
    $field_target_type = $field_definition->getSetting('target_type');

    $form['formatter'] = [
      '#tree' => TRUE,
      '#process' => [
        [$this, 'formatterSettingsProcessCallback'],
      ],
    ];

    $form['formatter']['label_text'] = [
      '#type' => 'textfield',
      '#title' => $this->t('Label Text'),
      '#description' => $this->t('This text will be used to override the default label of the field.'),
      '#default_value' => $config['formatter']['label_text'],
    ];
    $form['formatter']['label_size'] = [
      '#type' => 'select',
      '#title' => 'Header Size',
      '#options' => [
        'h2' => 'h2',
        'h3' => 'h3',
        'h4' => 'h4',
        'h5' => 'h5',
        'h6' => 'h6',
      ],
      '#default_value' => $config['formatter']['label_size'],
    ];

    $form['formatter']['item_wrapper'] = [
      '#type' => 'select',
      '#title' => 'Item Wrapper',
      '#options' => [
        'u' => 'Underline',
        'h2' => 'H2',
        'h3' => 'H3',
        'h4' => 'H4',
        'h5' => 'H5',
        'h6' => 'H6',
        'div' => 'Div',
        'span' => 'Span',
        'strong' => 'Bold',
        'i' => 'Italic',
        'p' => 'Paragraph',
      ],
      '#default_value' => $config['formatter']['item_wrapper'],
    ];

    $form['formatter']['item_wrapper_classes'] = [
      '#type' => 'textfield',
      '#title' => 'Item Wrapper Classes',
      '#description' => $this->t('Enter classes separated by a space to be applied to the wrapper of all the items.'),
      '#default_value' => $config['formatter']['item_wrapper_classes'],
    ];

    $form['formatter']['item_classes'] = [
      '#type' => 'textfield',
      '#title' => 'Item Classes',
      '#description' => $this->t('Enter classes separated by a space to be applied to each item.'),
      '#default_value' => $config['formatter']['item_classes'],
    ];

    $form['formatter']['item_separator'] = [
      '#type' => 'textfield',
      '#title' => 'Item Separator',
      '#description' => $this->t('When there are multiple items this will be used to separate them. Can accept html.'),
      '#default_value' => $config['formatter']['item_separator'],
    ];

    if ($type === 'entity_reference' && $field_target_type === 'taxonomy_term'){
      $form['formatter']['item_sort'] = [
        '#type' => 'select',
        '#title' => $this->t('Item Sort Type'),
        '#description' => $this->t('This will attempt to sort the list of multiple items, if there are multiples, by title.'),
        '#options' => [
          '' => $this->t('None'),
          'asc' => $this->t('Asc'),
          'desc' => $this->t('Desc'),
        ],
        '#default_value' => $config['formatter']['item_sort'],
      ];
    }

    $form['formatter']['label'] = [
      '#type' => 'select',
      '#title' => $this->t('Label'),
      // @todo This is directly copied from
      //   \Drupal\field_ui\Form\EntityViewDisplayEditForm::getFieldLabelOptions(),
      //   resolve this in https://www.drupal.org/project/drupal/issues/2933924.
      '#options' => [
        'above' => $this->t('Above'),
        'inline' => $this->t('Inline'),
        'hidden' => '- ' . $this->t('Hidden') . ' -',
        'visually_hidden' => '- ' . $this->t('Visually Hidden') . ' -',
      ],
      '#default_value' => $config['formatter']['label'],
    ];

    $form['formatter']['type'] = [
      '#type' => 'select',
      '#title' => $this->t('Formatter'),
      '#options' => $this->getApplicablePluginOptions($this->getFieldDefinition()),
      '#required' => TRUE,
      '#default_value' => $config['formatter']['type'],
      '#ajax' => [
        'callback' => [static::class, 'formatterSettingsAjaxCallback'],
        'wrapper' => 'formatter-settings-wrapper',
      ],
    ];

    // Add the formatter settings to the form via AJAX.
    $form['formatter']['settings_wrapper'] = [
      '#prefix' => '<div id="formatter-settings-wrapper">',
      '#suffix' => '</div>',
    ];


    $form['third_party_settings']['#tree'] = TRUE;
    $config = $this->configuration['third_party_settings']['informed_module'];
    $form['third_party_settings']['informed_module'] = BlockFormStyles::styleOptions($config);

    return $form;
  }


  public function blockSubmit($form, FormStateInterface $form_state) {
    $this->configuration['formatter'] = $form_state->getValue('formatter');
    $values = $form_state->getValues();
    foreach ($values as $index => $value) {
      $entities = !empty($value['selection']['entity_browser']['entities']);
      if ($entities){
        unset($value['selection']['entity_browser']['entities']);
      }
      $this->configuration[$index] = $value;
    }
  }


}
