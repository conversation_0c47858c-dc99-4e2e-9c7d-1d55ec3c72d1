<?php

namespace Drupal\informed_module\Plugin\Block;

use <PERSON><PERSON>al\Core\Access\AccessResult;
use <PERSON><PERSON>al\Core\Block\Annotation\Block;
use <PERSON><PERSON>al\Core\Form\FormStateInterface;
use <PERSON><PERSON>al\Core\Path\PathMatcherInterface;
use <PERSON><PERSON>al\Core\Session\AccountInterface;
use Drupal\Core\Language\LanguageManagerInterface;
use Drupal\Core\Url;
use Drupal\informed_module\Modules\BlockFormStyles;
use Drupal\language\Plugin\Block\LanguageBlock;
use Symfony\Component\DependencyInjection\ContainerInterface;

/**
 * Provides a 'Language switcher' block.
 *
 * @Block(
 *   id = "informed_language_block",
 *   admin_label = @Translation("Informed Language switcher"),
 *   deriver = "Drupal\language\Plugin\Derivative\LanguageBlock"
 * )
 */
class InformedLanguageBlock extends LanguageBlock {

  /**
   * @param array $form
   * @param FormStateInterface $form_state
   * @return array
   * just add the advanced settings to blocks here
   */
  public function blockForm($form, FormStateInterface $form_state) {

    $form['text_color'] = [
      '#type' => 'select',
      '#title' => $this->t('Text Color'),
      '#default_value' => $this->configuration['text_color'],
      '#options' => [
        'color-black' =>  'Black',
        'color-white' =>  'White',
        'color-primary' =>  'Primary',
        'color-secondary' =>  'Secondary',
        'color-tertiary' =>  'Tertiary',
        'color-quaternary' =>  'Quaternary',
        'color-quinary' =>  'Quinary',
        'color-senary' =>  'Senary',
        'color-transparent' =>  'Transparent',
      ],
    ];

    $form['background_color'] = [
      '#type' => 'select',
      '#title' => $this->t('Background Color'),
      '#default_value' => $this->configuration['background_color'],
      '#options' => [
        'bg-color-black' =>  'Black',
        'bg-color-white' =>  'White',
        'bg-color-primary' =>  'Primary',
        'bg-color-secondary' =>  'Secondary',
        'bg-color-tertiary' =>  'Tertiary',
        'bg-color-quaternary' =>  'Quaternary',
        'bg-color-quinary' =>  'Quinary',
        'bg-color-senary' =>  'Senary',
        'bg-color-transparent' =>  'Transparent',
      ],
    ];

    $form['hover_color'] = [
      '#type' => 'select',
      '#title' => $this->t('Text Hover Color'),
      '#default_value' => $this->configuration['hover_color'],
      '#options' => [
        'hover-color-black' =>  'Black',
        'hover-color-white' =>  'White',
        'hover-color-primary' =>  'Primary',
        'hover-color-secondary' =>  'Secondary',
        'hover-color-tertiary' =>  'Tertiary',
        'hover-color-quaternary' =>  'Quaternary',
        'hover-color-quinary' =>  'Quinary',
        'hover-color-senary' =>  'Senary',
        'hover-color-transparent' =>  'Transparent',
      ],
    ];

    $form['hover_background_color'] = [
      '#type' => 'select',
      '#title' => $this->t('Background Hover Color'),
      '#default_value' => $this->configuration['hover_background_color'],
      '#options' => [
        'hover-bg-color-black' =>  'Black',
        'hover-bg-color-white' =>  'White',
        'hover-bg-color-primary' =>  'Primary',
        'hover-bg-color-secondary' =>  'Secondary',
        'hover-bg-color-tertiary' =>  'Tertiary',
        'hover-bg-color-quaternary' =>  'Quaternary',
        'hover-bg-color-quinary' =>  'Quinary',
        'hover-bg-color-senary' =>  'Senary',
        'hover-bg-color-transparent' =>  'Transparent',
      ],
    ];

    $form['dropdown_background_color'] = [
      '#type' => 'select',
      '#title' => $this->t('Dropdown Background Color'),
      '#default_value' => $this->configuration['dropdown_background_color'],
      '#options' => [
        'bg-color-black' =>  'Black',
        'bg-color-white' =>  'White',
        'bg-color-primary' =>  'Primary',
        'bg-color-secondary' =>  'Secondary',
        'bg-color-tertiary' =>  'Tertiary',
        'bg-color-quaternary' =>  'Quaternary',
        'bg-color-quinary' =>  'Quinary',
        'bg-color-senary' =>  'Senary',
        'bg-color-transparent' =>  'Transparent',
      ],
    ];

    $form['dropdown_text_color'] = [
      '#type' => 'select',
      '#title' => $this->t('Dropdown Text Color'),
      '#default_value' => $this->configuration['dropdown_text_color'],
      '#options' => [
        'anchor-color-black' =>  'Black',
        'anchor-color-white' =>  'White',
        'anchor-color-primary' =>  'Primary',
        'anchor-color-secondary' =>  'Secondary',
        'anchor-color-tertiary' =>  'Tertiary',
        'anchor-color-quaternary' =>  'Quaternary',
        'anchor-color-quinary' =>  'Quinary',
        'anchor-color-senary' =>  'Senary',
        'anchor-color-transparent' =>  'Transparent',
      ],
    ];

    $form['dropdown_text_hover_color'] = [
      '#type' => 'select',
      '#title' => $this->t('Dropdown Text Hover Color'),
      '#default_value' => $this->configuration['dropdown_text_hover_color'],
      '#options' => [
        'anchor-hover-color-black' =>  'Black',
        'anchor-hover-color-white' =>  'White',
        'anchor-hover-color-primary' =>  'Primary',
        'anchor-hover-color-secondary' =>  'Secondary',
        'anchor-hover-color-tertiary' =>  'Tertiary',
        'anchor-hover-color-quaternary' =>  'Quaternary',
        'anchor-hover-color-quinary' =>  'Quinary',
        'anchor-hover-color-senary' =>  'Senary',
        'anchor-hover-color-transparent' =>  'Transparent',
      ],
    ];

    $form['dropdown_background_hover_color'] = [
      '#type' => 'select',
      '#title' => $this->t('Dropdown Background Hover Color'),
      '#default_value' => $this->configuration['dropdown_background_hover_color'],
      '#options' => [
        'hover-bg-color-black' =>  'Black',
        'hover-bg-color-white' =>  'White',
        'hover-bg-color-primary' =>  'Primary',
        'hover-bg-color-secondary' =>  'Secondary',
        'hover-bg-color-tertiary' =>  'Tertiary',
        'hover-bg-color-quaternary' =>  'Quaternary',
        'hover-bg-color-quinary' =>  'Quinary',
        'hover-bg-color-senary' =>  'Senary',
        'hover-bg-color-transparent' =>  'Transparent',
      ],
    ];



    /** @var \Drupal\block\BlockInterface $block */
    $form['third_party_settings']['#tree'] = TRUE;
    $config = $this->configuration['third_party_settings']['informed_module'];
    $form['third_party_settings']['informed_module'] = BlockFormStyles::styleOptions($config);
    return $form;
  }

  /**
   * @param array $form
   * @param FormStateInterface $form_state
   * save all values submitted from the block
   */
  public function blockSubmit($form, FormStateInterface $form_state) {
    parent::blockSubmit($form, $form_state);
    $values = $form_state->getValues();
    foreach ($values as $index => $value) {
      $entities = !empty($value['selection']['entity_browser']['entities']);
      if ($entities){
        unset($value['selection']['entity_browser']['entities']);
      }

      $this->configuration[$index] = $value;
    }
  }

  /**
   * {@inheritdoc}
   */
  public function build() {
    $build = parent::build();
    $build['#settings'] = $this->configuration;
    $lang_code = \Drupal::languageManager()->getCurrentLanguage()->getId();
    $links = $build['#links'] ?? [];
    foreach ($links as $lang => $link){
      if ($lang === $lang_code){
        $build['#settings']['active_item'] = $link;
      }
    }


    return $build;
  }
}
