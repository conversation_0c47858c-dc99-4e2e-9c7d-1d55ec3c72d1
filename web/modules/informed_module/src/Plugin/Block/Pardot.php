<?php

namespace Drupal\informed_module\Plugin\Block;


use Drupal\Core\Form\FormStateInterface;
use <PERSON>upal\informed_module\Modules\Block;
use Drupal\informed_module\Modules\BlockFormStyles;
use Drupal\taxonomy\TermInterface;


/**
 * @Block(
 * id = "pardot",
 * admin_label = @Translation("Pardot"),
 * )
 */

class Pardot extends Block {

  public function defaultConfiguration() {
    return [];
  }

  public function blockForm($form, FormStateInterface $form_state) {
        $form['pardot_url'] = [
      '#type' => 'textfield',
      '#title' => $this->t('Pardot URL'),
      '#description' => $this->t('The Pardot URL to receive submission data.'),
      '#default_value' => $this->configuration['pardot_url'],
    ];

    return $form;
  }

  public function blockSubmit ($form, FormStateInterface $form_state){
    parent::blockSubmit($form, $form_state);
  }

  public function build() {
    $token = \Drupal::request()->query->get('id');
    $source = '';
	$data = false;

    //load submission data from token
    if ($token) {
      /** @var \Drupal\webform\WebformSubmissionStorageInterface $webform_submission_storage */
      $webform_submission_storage = \Drupal::entityTypeManager()->getStorage('webform_submission');
      if ($entities = $webform_submission_storage->loadByProperties(['token' => $token])) {
        $webform_submission = reset($entities);
		$data = $webform_submission->getData();
      }

      if($data) {
        $source = $this->configuration['pardot_url'];

        //build url args
        foreach($data as $key => $value) {
          $value = urlencode($value);

          if ($key === array_key_first($data)) {
            $source .= "?{$key}={$value}";
          } else {
            $source .= "&{$key}={$value}";
          }
        }
      }
    }

    $render = [
      '#theme' => 'pardot',
      '#source' => $source,
      '#site_name' => \Drupal::config('system.site')->get('name')
    ];

    return $render;
  }
}
