<?php

namespace Drupal\informed_module\Plugin\Block;


use <PERSON><PERSON>al\Core\Form\FormStateInterface;
use <PERSON><PERSON>al\informed_module\Helpers\EntityBrowserFormField;
use Drupal\informed_module\Modules\Block;
use Drupal\informed_module\Modules\BlockFormStyles;


/**
 * @Block(
 * id = "search_form",
 * admin_label = @Translation("Search Form"),
 * )
 */

class SearchForm extends Block {

    public function defaultConfiguration() {
        return array(

        );
    }
    public function blockForm($form, FormStateInterface $form_state) {

        $form['placeholder_text'] = [
            '#type' => 'textfield',
            '#title' => $this->t('Placeholder Text'),
            '#description' => $this->t('Enter the link to the page you would like to search. Search page must use "search" as the query param. This will be used in a header above the input in the modal.'),
            '#default_value' => $this->configuration['placeholder_text'],
        ];

        $form['link'] = [
            '#type' => 'textfield',
            '#title' => $this->t('External Link'),
            '#description' => $this->t('Enter the link to the page you would like to search. Search page must use "search" as the query param.'),
            '#default_value' => $this->configuration['link'],
        ];

        $this->FieldTextColor($form, 'input_text_color', 'Input Text Color');
        $this->FieldTextColor($form, 'label_text_color', 'Label Text Color');
        $this->FieldBackgroundColor($form, 'input_background_color', 'Input Background Color');
        $this->FieldBackgroundColor($form, 'label_background_color', 'Label Background Color');

        $form['size'] = [
            '#type' => 'select',
            '#title' => $this->t('Search Form Size'),
            '#default_value' => $this->configuration['size'],
            '#options' => [
              'large-form' => 'Large',
              'small-form' => 'Small',
            ],
        ];

        $form['icon_only'] = [
            '#type' => 'checkbox',
            '#title' => $this->t('Only Icon'),
            '#description' => 'Checking this results in only the icon showing. Clicking the icon will pop up a modal with the search form in it. Will typically use this on smaller screens',
            '#default_value' => $this->configuration['icon_only'],
        ];

        $form['icon_size'] = [
            '#type' => 'textfield',
            '#title' => $this->t('Icon Size'),
            '#description' => $this->t('Can be any font-size unit px, %, em, rem. Changes the size of the stand along icon'),
            '#default_value' => $this->configuration['icon_size'] ?? '20px',
        ];

        $this->FieldTextColor($form, 'modal_label_text_color', 'Modal Label Text Color');
        $this->FieldTextColor($form, 'modal_close_text_color', 'Modal Close Text Color');
        $this->FieldTextHoverColor($form, 'modal_close_text_hover_color', 'Modal Close Text Hover Color');
        $this->FieldBackgroundColor($form, 'modal_background_color', 'Modal Background Color');


        $form = parent::blockForm($form, $form_state);

        return $form;
    }

    public function blockSubmit ($form, FormStateInterface $form_state){
        parent::blockSubmit($form, $form_state);
        $this->configuration['title'] = $form['settings']['label']['#value'];
    }

    public function build() {
        $render = [
            '#theme' => 'search_form',
            '#settings' => $this->configuration,
        ];

      return parent::render($render);
    }
}
