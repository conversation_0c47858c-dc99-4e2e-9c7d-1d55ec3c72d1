<?php

namespace Drupal\informed_module\Plugin\Block;


use Drupal\Core\Form\FormStateInterface;
use Drupal\informed_module\Modules\Block;
use Drupal\informed_module\Modules\BlockFormStyles;


/**
 * @Block(
 * id = "slider",
 * admin_label = @Translation("Slider"),
 * )
 */

class Slider extends Block {

    public function defaultConfiguration() {
        return array(

        );
    }
    public function blockForm($form, FormStateInterface $form_state) {

        $entity_queue = \Drupal::entityTypeManager()->getStorage('entity_queue')->load($this->configuration['entity_queue_selection']);
        $custom_block = \Drupal::entityTypeManager()->getStorage('node')->load($this->configuration['custom_block']);
        $image_style = \Drupal::entityTypeManager()->getStorage('image_style')->load($this->configuration['image_style']);

        $form['label']['#type'] = 'text_format';
        $form['label']['#format'] = 'full_html';

        $form['entity_queue_selection'] = [
            '#type' => 'entity_autocomplete',
            '#title' => $this->t('Entity Queue'),
            '#description' => $this->t('Select an Entity Queue.'),
            // '#title_display' => 'invisible',
            '#default_value' => $entity_queue,
            '#target_type' => 'entity_queue',
            '#maxlength' => 1000,
        ];


        $form['embed'] = [
          '#type' => 'textfield',
          '#title' => $this->t('Iframe Embed'),
          '#description' => $this->t('Example:"https://www.youtube.com/embed/kEG1AK-JduI?controls=0&autoplay=1&loop=1&mute=1&playlist=kEG1AK-JduI"'),
          '#default_value' => $this->configuration['embed']
        ];


      $form['image_style'] = [
          '#type' => 'entity_autocomplete',
          '#title' => $this->t('Image Style'),
          '#description' => $this->t('Select an a image style to render your slides in.'),
          // '#title_display' => 'invisible',
          '#default_value' => $image_style,
          '#target_type' => 'image_style',
          '#maxlength' => 1000,
        ];

      $form['text_layer'] = [
        '#type' => 'text_format',
        '#format' => 'full_html',
        '#title' => $this->t('Text Layer'),
        '#description' => $this->t('Enter markup. This markup will appear as the overlay content for every slide or video. Use for embed.'),
        '#default_value' => $this->configuration['text_layer']['value'],
      ];

      $form['custom_block'] = [
        '#type' => 'entity_autocomplete',
        '#title' => $this->t('Select a Custom Global Layout Block'),
        '#description' => $this->t('Select a custom Global Layout to display below the text layer'),
        // '#title_display' => 'invisible',
        '#default_value' => $custom_block,
        '#target_type' => 'node',
          '#selection_settings' => [
              'target_bundles' => ['global_layout'], // your content type
          ],
        '#maxlength' => 1000,
      ];

      $form['auto_play'] = [
          '#type' => 'checkbox',
          '#title' => $this->t('Autoplay'),
          '#description' => $this->t('Whether or not to autoplay on page load.'),
          '#default_value' => !empty($this->configuration['auto_play']) ? $this->configuration['auto_play'] : 1,
        ];

        $form['timer_delay'] = [
          '#type' => 'number',
          '#title' => $this->t('Timer Delay'),
          '#description' => $this->t('How long between each transition in ms.'),
          '#default_value' => !empty($this->configuration['timer_delay']) ? $this->configuration['timer_delay'] : 5000,
        ];

        $form['nav_buttons'] = [
          '#type' => 'checkbox',
          '#title' => $this->t('Navigation Arrows'),
          '#description' => $this->t('Whether or not to include arrows.'),
          '#default_value' => !empty($this->configuration['nav_buttons']) ? $this->configuration['nav_buttons'] : 0,
        ];

        $form['bullets'] = [
          '#type' => 'checkbox',
          '#title' => $this->t('Navigation Bullets'),
          '#description' => $this->t('Whether or not to include bullets.'),
          '#default_value' => !empty($this->configuration['bullets']) ? $this->configuration['bullets'] : 0,
        ];

        $form = parent::blockForm($form, $form_state);

        return $form;
    }

    public function blockSubmit ($form, FormStateInterface $form_state){
        parent::blockSubmit($form, $form_state);
        $this->configuration['title'] = $form['settings']['label']['#value'];
    }

    public function build() {
        $entities = [];
        if (empty($this->configuration['embed'])){
          if (!$this->configuration['entity_queue_selection']){ return ['#markup' => 'Please Enter a valid Entityqueue or embed link'];}
          if (!$this->configuration['image_style']){ return ['#markup' => 'Please Enter a valid Image Style.'];}
          $queue = \Drupal::entityTypeManager()->getStorage('entity_subqueue')->load($this->configuration['entity_queue_selection']);

          $entities = $queue->get('items')->referencedEntities();
          if (!$entities){
              return ['#markup' => 'There are no Entities in this Entityqueue.'];
          }
        }


        $render = [
            '#theme' => 'slider',
            '#entities' => $entities,
            '#embed' => $this->configuration['embed'],
            '#text_layer' => [
              '#type' => 'processed_text',
              '#text' => $this->configuration['text_layer']['value'],
              '#format' => 'full_html',
            ],
            '#custom_block' => $this->configuration['custom_block'],
            '#image_style' => $this->configuration['image_style'],
            '#settings' => [
              'auto_play' => $this->configuration['auto_play'],
              'timer_delay' => $this->configuration['timer_delay'],
              'nav_buttons' => $this->configuration['nav_buttons'],
              'bullets' => $this->configuration['bullets'],
            ],
        ];

      return parent::render($render);
    }
}
