<?php

namespace Drupal\informed_module\Plugin\Block;


use Drupal\Core\Form\FormStateInterface;
use <PERSON><PERSON>al\informed_module\Modules\Block;
use Drupal\informed_module\Modules\BlockFormStyles;
use Drupal\taxonomy\TermInterface;


/**
 * @Block(
 * id = "title",
 * admin_label = @Translation("Title"),
 * )
 */

class title extends Block {

    public function defaultConfiguration() {
        return array(

        );
    }
    public function blockForm($form, FormStateInterface $form_state) {
        $form['text_size'] = [
          '#type' => 'select',
          '#title' => 'Size',
          '#default_value' => $this->configuration['text_size'],
          '#options' => [
            'h1' => 'H1',
            'h2' => 'H2',
            'h3' => 'H3',
            'h4' => 'H4',
            'h5' => 'H5',
            'h6' => 'H6',
          ],
        ];
        // ['text_color']
        $this->FieldTextColor($form);
        // ['text_align']
        $this->FieldTextAlign($form);

        $form = parent::blockForm($form, $form_state);
        return $form;
    }

    public function blockSubmit ($form, FormStateInterface $form_state){
        parent::blockSubmit($form, $form_state);
        $this->configuration['title'] = $form['settings']['label']['#value'];
    }

    public function build() {
      $entity = \Drupal::routeMatch()->getParameter('node') ?? \Drupal::routeMatch()->getParameter('taxonomy_term');
      if ($entity instanceof \Drupal\node\NodeInterface) {
        // You can get nid and anything else you need from the node object.
        $content = $entity->getTitle();
      } else if ($entity instanceof \Drupal\taxonomy\Entity\Term) {
        $content = $entity->getName();
      } else {
        return ['#markup' => '<h1 class="'. $this->configuration['text_size'] . ' ' . ($this->configuration['text_color'] ?? '') .' '. ($this->configuration['text_align'] ?? '') .'">Title</h1>'];
      }


      $render = [
          '#theme' => 'title_block',
          '#content' => $content ?? '',
          '#settings' => $this->configuration,
      ];

      return parent::render($render);
    }
}
