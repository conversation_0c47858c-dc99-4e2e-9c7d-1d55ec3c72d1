<?php

namespace Drupal\informed_module\Plugin\Block;


use <PERSON>upal\Core\Form\FormStateInterface;
use <PERSON><PERSON>al\informed_module\Modules\Block;
use <PERSON><PERSON>al\informed_module\Modules\BlockFormStyles;


/**
 * @Block(
 * id = "wysiwyg",
 * admin_label = @Translation("Wysiwyg"),
 * )
 */

class Wysiwyg extends Block {

    public function defaultConfiguration() {
        return array(

        );
    }
    public function blockForm($form, FormStateInterface $form_state) {
        $form['label']['#type'] = 'text_format';
        $form['label']['#format'] = 'full_html';
        $form['wysiwyg'] = [
            '#type' => 'text_format',
            '#format' => 'full_html',
            '#title' => $this->t('Wysiwyg'),
            '#description' => $this->t('Enter markup. Will Accept NODE related tokens.'),
            '#default_value' => $this->configuration['wysiwyg']['value'],
        ];

        $form = parent::blockForm($form, $form_state);

        return $form;
    }

    public function blockSubmit ($form, FormStateInterface $form_state){
        parent::blockSubmit($form, $form_state);
        $this->configuration['title'] = $form['settings']['label']['#value'];
    }

    public function build() {
        $node = \Drupal::routeMatch()->getParameter('node') ?? '';
        $withdrawal_override = $this->configuration['wysiwyg']['value'];

        if ($node instanceof \Drupal\node\NodeInterface) {
          if ($node->hasField('field_withdrawal_override') && $node->get('field_withdrawal_override') !== null && !empty(trim($node->get('field_withdrawal_override')->value))) {
            $withdrawal_override = $node->get('field_withdrawal_override')->value;
          }
        }

        $render = [
            '#theme' => 'wysiwyg',
            '#content' => [
              '#type' => 'processed_text',
              '#text' => \Drupal::token()->replace($withdrawal_override, ['node' => $node]),
              '#format' => 'full_html',
            ],
        ];
      return parent::render($render);
    }
}
