<?php

namespace Drupal\informed_module\Plugin\Field\FieldFormatter;

use <PERSON><PERSON>al\Component\Utility\Html;
use <PERSON><PERSON>al\Core\Field\Annotation\FieldFormatter;
use <PERSON><PERSON>al\Core\Field\FieldItemInterface;
use <PERSON><PERSON>al\Core\Field\FieldItemListInterface;
use Drupal\Core\Field\FormatterBase;
use Drupal\Core\Form\FormStateInterface;
use Drupal\datetime\Plugin\Field\FieldFormatter\DateTimeCustomFormatter;

/**
 * Plugin implementation of the 'date_show_field_formatter' formatter.
 *
 * Provides a way to show date fields with the custom formatter after the date is hit.
 *
 * @FieldFormatter(
 *   id = "date_show_field_formatter",
 *   label = @Translation("Show On Date"),
 *   field_types = {
 *     "datetime"
 *   }
 * )
 */


class DateShowFieldFormatter extends DateTimeCustomFormatter {

  /**
   * {@inheritdoc}
   */
  public function viewElements(FieldItemListInterface $items, $langcode) {
    // if the date is not more than today don't show
    foreach ($items as $id => $item){
      $timestamp = $item->date->getTimestamp();
      $current_timestamp = time();
      if ($timestamp > $current_timestamp){
        unset($items[$id]);
      }
    }



    $elements = parent::viewElements($items, $langcode);

    return $elements;
  }


}
