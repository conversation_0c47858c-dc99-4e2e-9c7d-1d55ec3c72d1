<?php

namespace Drupal\informed_module\Plugin\Field\FieldFormatter;

use <PERSON><PERSON>al\Component\Utility\Html;
use <PERSON><PERSON>al\Core\Field\Annotation\FieldFormatter;
use <PERSON><PERSON>al\Core\Field\FieldItemInterface;
use <PERSON><PERSON>al\Core\Field\FieldItemListInterface;
use Drupal\Core\Field\FormatterBase;
use Drupal\Core\Form\FormStateInterface;
use Drupal\link\Plugin\Field\FieldFormatter\LinkFormatter;

/**
 * Plugin implementation of the 'extended link' formatter.
 *
 * Provides a way to add new customizations to core link formatter
 *
 * @FieldFormatter(
 *   id = "extended_link",
 *   label = @Translation("Link (extended)"),
 *   field_types = {
 *     "link"
 *   }
 * )
 */


class ExtendedLinkFormatter extends LinkFormatter {

  /**
   * {@inheritdoc}
   */
  public function settingsForm(array $form, FormStateInterface $form_state) {
    $elements = parent::settingsForm($form, $form_state);

    $elements['rel'] = [
      '#type' => 'radios',
      '#title' => $this->t('Add rel="*" to links'),
      '#return_value' => 'nofollow',
      '#options' => array(
        '' => $this->t('disabled'),
        'nofollow' => $this->t('nofollow'),
        'sponsored' => $this->t('sponsored')
      ),
      '#default_value' => $this->getSetting('rel'),
      ];

    return $elements;
  }

}
