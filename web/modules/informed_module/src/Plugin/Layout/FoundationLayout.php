<?php
namespace Drupal\informed_module\Plugin\Layout;

use <PERSON><PERSON><PERSON>\Core\Form\FormStateInterface;
use <PERSON><PERSON><PERSON>\Core\Layout\LayoutDefault;
use <PERSON><PERSON>al\Core\Plugin\PluginFormInterface;
use <PERSON><PERSON>al\entity_browser_block\Plugin\Block\EntityBrowserBlock;
use <PERSON><PERSON><PERSON>\image\Entity\ImageStyle;
use Drupal\informed_module\Helpers\EntityBrowserFormField;

/**
 * Class FoundationLayout
 *
 * a class to configure and render custom layouts
 *
 * @package Drupal\informed_module\Plugin\Layout
 */
class FoundationLayout extends LayoutDefault implements PluginFormInterface {

  /**
   * @var array array of form items to create with class browser
   */
  public $class_bundles = [
    'layout_container_classes',
    'layout_inner_container_classes',
    'grid_container_classes',
    'cell_classes',
  ];
  /**
   * {@inheritdoc}
   */
  public function defaultConfiguration() {
    return parent::defaultConfiguration() + [
        'extra_classes' => 'Default',
      ];
  }

  /**
   * {@inheritdoc}
   */
  public function buildConfigurationForm(array $form, FormStateInterface $form_state) {
    $form = parent::buildConfigurationForm($form, $form_state);
    $configuration = $this->getConfiguration();
    if ($this->getPluginId() === 'global_layout'){
        $global_layout = \Drupal::entityTypeManager()->getStorage('node')->load($this->configuration['global_layout']);
        $form['global_layout'] = [
            '#type' => 'entity_autocomplete',
            '#title' => $this->t('Choose a global layout'),
            '#description' => $this->t('This will render the chosen layout as it displays on the view page of that node.'),
            '#default_value' => $global_layout,
            '#target_type' => 'node',
            '#selection_settings' => [
                'target_bundles' => ['global_layout'], // your content type
            ],
            '#maxlength' => 1000,
        ];
    } else {
        EntityBrowserFormField::generateEntityBrowsers($form, $configuration, $this->class_bundles, 'classes');
        EntityBrowserFormField::generateEntityBrowsers($form, $this->configuration, ['background_image'], 'images', 1);
        $regions = $this->getPluginDefinition()->getRegionNames();
        EntityBrowserFormField::generateEntityBrowsers($form, $this->configuration, $regions, 'classes');
    }
    return $form;
  }

  /**
   * @param array $terms array of terms to get class bundle string from and group together
   * @return string
   */
  public static function get_terms_class_bundle(array $terms){
    if (!$terms){
      return '';
    }
    $class_bundle = '';
    foreach ($terms as $term){
      $class_bundle .= $term->field_class_bundle->value . ' ' ;
    }

    return $class_bundle;

  }

  /**
   * @param $terms /group array of term objects to an array of term ids
   *
   * @return array
   */
  public static function termsToArray($terms) {
    $term_ids = [];
    if (!$terms){
      return [];
    }
    foreach ($terms as $id) {
      array_push($term_ids, $id['target_id']);
    }
    return $term_ids;
  }

  /**
   * {@inheritdoc}
   */
  public function validateConfigurationForm(array &$form, FormStateInterface $form_state) {
    // any additional form validation that is required
  }

  /**
   * {@inheritdoc}
   */
  public function submitConfigurationForm(array &$form, FormStateInterface $form_state) {
    $values = $form_state->getValues();
    foreach ($values as $index => $value) {
      $entities = !empty($value['selection']['entity_browser']['entities']);
      if ($entities){
        unset($value['selection']['entity_browser']['entities']);
      }
      $this->configuration[$index] = $value;
    }
  }

  /**
   * {@inheritdoc}
   */
  public function build($regions) {
    $build = parent::build($regions);

      if ($build['#settings']){
          if ($this->getPluginId() === 'global_layout'){
              $global_layout = \Drupal::entityTypeManager()->getStorage('node')->load($build['#settings']['global_layout']);
              if(!$global_layout){ return ['#markup' => 'Please Enter a valid global layout reference.'];}

              $build['#settings']['global_layout'] = \Drupal::service('renderer')->render(\Drupal::entityTypeManager()->getViewBuilder('node')->view($global_layout));
          } else {
                $regions = $this->getPluginDefinition()->getRegionNames();
                $class_wrappers = array_merge($this->class_bundles, $regions);
                // loop thru all items that would contain a entity browser for classes
                foreach ($class_wrappers as $bundle){
                    $entity_ids = $build['#settings'][$bundle]['selection']['entity_browser']['entity_ids'] ?? false;
                    // if there are selected ids then get string of classes seperated by spaces
                    if ($entity_ids){
                      $build['#settings'][$bundle] = self::get_terms_class_bundle(EntityBrowserBlock::loadEntitiesByIDs(explode(' ',$entity_ids)));
                    } else {
                      unset($build['#settings'][$bundle]);
                    }
                }

                // check for media ids
                $media_item = $build['#settings']['background_image']['selection']['entity_browser']['entity_ids'] ?? false;
                $build['#settings']['background_image'] = '';
                // if there is an item split out the string we are getting to get the actual media id
                if (is_string($media_item)){
                    $media_item = explode(':',$media_item);
                    $media_item = is_array($media_item) && count($media_item) === 2 ? $media_item[1] : '';
                  if (is_numeric($media_item)){
                    // load up the media item and get the url for the image.
                    $media_item = \Drupal::entityTypeManager()->getStorage('media')->load($media_item);
                    if ($media_item){
                      $fid = $media_item->field_media_image->getValue()[0]['target_id'];
                      $file = \Drupal::entityTypeManager()->getStorage('file')->load($fid);
                      $uri = $file->getFileUri();
                      $style = ImageStyle::load('original');
                      $url = $style->buildUrl($uri);
                      $build['#settings']['background_image'] = $url;
                    }
                }
              }
          }
    }
    $build['#attached']['library'][] = 'informed_module/informed_entity_browsers';
    return $build;
  }





}
