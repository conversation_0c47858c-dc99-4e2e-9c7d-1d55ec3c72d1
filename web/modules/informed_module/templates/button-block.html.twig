
{#settings.text#}
{#settings.internal_link#}
{#settings.external_link#}
{#settings.text_color#}
{#settings.background_color#}
{#settings.hover_color#}
{#settings.hover_background_color#}
{#settings.icon_class#}
{#settings.icon_size#}
{#settings.open_new_window#}
{% set icon_size = settings.icon_size ? 'font-size:'~ settings.icon_size ~';' : '' %}
{% set url = settings.internal_link ? path('entity.node.canonical', {'node': settings.internal_link}) : settings.external_link %}
{% set new_window = settings.open_new_window ? 'target="blank" rel="noreferrer nofollow"' : '' %}
<a href="{{ url }}" {{new_window}} class="button {{ settings.text_color }} {{ settings.hover_color }} {{ settings.background_color }} {{ settings.hover_background_color }}" style="display: inline-flex;">
  {{ settings.text }}
  <i class="{{ settings.icon_class ? settings.icon_class : 'hide' }}" style="margin-left:10px;{{ icon_size }}"></i>
</a>

