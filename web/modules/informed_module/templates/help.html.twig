<h3>
  Classes
</h3>
<p>
  Foundation sites is enabled on this theme.
</p>
<ul>
  <li><a href="https://get.foundation/sites/docs/xy-grid.html">XY Grid</a></li>
  <li><a href="https://get.foundation/sites/docs/flexbox-utilities.html">Flexbox</a></li>
  <li><a href="https://get.foundation/sites/docs/typography-helpers.html">Helpers</a></li>
  <li><a href="https://get.foundation/sites/docs/visibility.html">Visibility</a></li>
  <li><a href="https://get.foundation/sites/docs/">All</a></li>

</ul>

<p>
  There are many helpful classes you can utilize to create layouts on the spot. There is a classes taxonomy where you can add classes with a label description and the classes to render on an element.
</p>
<h5>
  Colors
</h5>
<p>
  You can configure colors in the color configuration area. You can use these formulas to get the colors for fonts and backgrounds. replace the color name with appropriate color label.
</p>
<p>
  <strong>Color names include:</strong> primary, secondary, tertiary, quaternary, quinary, senary, black, transparent, and white
</p>
<ul>
  <li>color-{color-name}</li>
  <li>hover-color-{color-name}</li>
  <li>bg-color-{color-name}</li>
  <li>hover-bg-color-{color-name}</li>
  <li>anchor-color-{color-name}</li>
  <li>anchor-hover-color-{color-name}</li>
  <li>button-color-{color-name}</li>
  <li>hover-button-color-{color-name}</li>
  <li>text-decoration-color-{color-name}</li>
  <li>border-color-{color-name}</li>
</ul>

<h5>
  Spacing
</h5>
<p>
  Here are some formulas for spacing classes. Replace the {spacing} with one from the below list.
</p>
<p>
  <strong>Spacings Include:</strong> ultra-small, small, medium, large, xlarge
  <strong>Sides Include:</strong> top, bottom, left, right, vertical
</p>

<ul>
  <li>{spacing}-margin</li>
  <li>{spacing}-{side}-margin</li>
  <li>{spacing}-padding</li>
  <li>{spacing}-{side}-padding</li>
</ul>

<h5>
  Helpers
</h5>
<p>
  <strong>Types:</strong> margin, padding
  <strong>Sizes:</strong> small, medium, large
  <strong>Sides:</strong> top, bottom, left, right
</p>

<ul>
  <li>no-{type}</li>
  <li>no-{type}-{side}</li>
  <li>{size}-margin</li>
  <li>no-{type}-on-{size}-up</li>
  <li>no-{type}-{side}-on-{size}-up</li>
  <li>border-on-{size}-up</li>
  <li>border-{side}-on-{size}-up</li>
</ul>

<ul>
  <li>show-first-child-on-{size}</li>
  <li>display-flex</li>
  <li>display-block</li>
  <li>display-inline-block</li>
  <li>display-inline</li>
  <li>align-items-center</li>
  <li>justify-content-center</li>
  <li>justify-content-space-between</li>
  <li>justify-content-end</li>
  <li>no-border-last-child </li>
  <li>no-border-first-child</li>
  <li>no-border-first-and-last-child</li>
</ul>









