{#settings#}
  {#image#}
  {#image_style#}
  {#icon#}
  {#icon_style#}
  {#text#}
  {#external_link#}
  {#open_new_window#}
  {#background_color#}
  {#body#}
  {#subtitle#}
  {#gradiant#}
  {#medium_down_margin#}
  {#layout#}
{% set field =  settings.image.field_media_image %}
{% set media =  field.0.entity.uri.value ? field.0.entity.uri.value : '' %}
{% set media_mime =  field.0.entity.filemime.value %}
{% set media_alt =  field.description ? field.description : field.alt %}
{% set url =  file_url(media) %}
{% set image_style =  field.0.entity.uri.value | image_style(settings.image_style) %}
{% set url =  image_style ? image_style : url %}

{% set background_image =  url != '/' and url != '' ? 'style=background-position:center;background-repeat:no-repeat;background-size:cover;' : '' %}

{% set layout =  settings.layout == '0' ? '1' : '3' %}
<div class="grid-container full xlarge-vertical-padding position-relative no-padding-on-medium-down">
  <div class="background-image show-for-large b-lazy" data-src="{{ url }}" {{ background_image }}>
  </div>
  <div class="grid-container position-relative z-index-10">
    <div class="grid-x grid-padding-x">
      <div class="cell small-12 large-6 show-for-large large-order-2 no-padding-on-medium-down">
      </div>
      <div class="cell small-12 large-6 hide-for-large large-order-2 no-padding-on-medium-down" style="margin-bottom:{{ settings.medium_down_margin }}px;">
        <img class="display-block margin-auto b-lazy" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==" data-src="{{ url }}" alt="{{media_alt}}">
      </div>
      <div class="cell small-12 large-6 large-order-{{ layout }} no-padding-on-medium-down">
        <div class="grid-container">
          <div class="grid-x grid-padding-x grid-padding-y">
            <div class="cell small-12" style="background:{{ settings.gradiant }}">
              {% set field =  settings.icon.field_media_image %}
              {% set media =  field.0.entity.uri.value ? field.0.entity.uri.value : '' %}
              {% set media_mime =  field.0.entity.filemime.value %}
              {% set media_alt =  field.description ? field.description : field.alt %}
              {% set url =  file_url(media) %}
              {% set image_style =  field.0.entity.uri.value | image_style(settings.image_style) %}
              {% set url =  image_style ? image_style : url %}
              <img class="display-block margin-auto b-lazy" src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==" data-src="{{ url }}" alt="{{media_alt}}">
            </div>
            <div class="cell small-12 {{ settings.background_color }} xlarge-left-padding xlarge-right-padding xlarge-bottom-padding medium-top-padding" >
              <div class="subtitle">
                {{ settings.subtitle }}
              </div>
              <div class="body medium-vertical-margin show-for-large">
                {{ settings.body }}
              </div>
              {% set url = settings.external_link %}
              {% set new_window = settings.open_new_window ? 'target="blank" rel="noreferrer nofollow"' : '' %}
              <div class="large-top-margin hide-for-large"></div>
              <div class="text-center">
                <a href="{{ url }}" {{new_window}} class="button color-black hover-color-white bg-color-white hover-bg-color-black display-flex">
                  {{ settings.text }}
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
