{#settings#}
    {#placeholder_text#}
    {#icon_size#}
    {#link#}
    {#input_text_color#}
    {#label_text_color#}
    {#input_background_color#}
    {#label_background_color#}
    {#size#}
    {#icon_only#}
    {#modal_close_text_color#}
    {#modal_close_text_hover_color#}
    {#modal_background_color#}
    {#modal_label_text_color#}
{% set hash = 'form_' ~ random() %}
{% set icon_size = settings.icon_size ? 'font-size:'~ settings.icon_size ~';' : '' %}
{% if settings.icon_only == '1' %}
    <a class="block-search-icon-only" data-toggle="{{hash}}">
        <i class="informed-icon informed-search" style="{{ icon_size }}"></i>
    </a>
{% endif %}
<div id="{{ hash }}" class="block-search {{ settings.size }} {{ settings.icon_only == '1' ? 'hide icon_only ' ~ settings.modal_background_color : '' }}" data-toggler=".show">
    <div class="close-icon-wrapper {{ settings.icon_only != '1' ? 'hide' : '' }}">
      <a data-toggle="{{ hash }}"><i class="informed-icon informed-close {{ settings.modal_close_text_hover_color }} {{ settings.modal_close_text_color }}"></i></a>
    </div>
    <h6 class="{{ settings.icon_only != '1' ? 'hide' : '' }} grid-container h1 {{ settings.modal_label_text_color }}">
        {{ settings.placeholder_text }}
    </h6>
    <form action="{{settings.link}}">

          <input class="{{settings.input_background_color}} {{settings.input_text_color}}" placeholder="{{settings.icon_only != '1' ? settings.placeholder_text : '' }}" type="search" name="search" />
          <label class="submit-label {{settings.label_background_color}} {{settings.label_text_color}} no-margin-bottom" for="edit-submit-{{ hash }}">
              <input id="edit-submit-{{ hash }}" type="submit" value="Search" />
          </label>
    </form>
</div>
