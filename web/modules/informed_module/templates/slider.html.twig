<div class="hero-slider">
  {% if entities|length > 1%}
{#    @todo need to handle multiple slides #}
      <div class="orbit " role="region" aria-label="slider" data-orbit data-bullets="{{ settings.bullets == 1 ? 'true' : ''}}" data-nav-buttons="{{ settings.nav_buttons == 1 ? 'true' : '' }}" data-autoplay="{{ settings.auto_play == 1 ? 'true' : '' }}" data-timer-delay="{{ settings.timer_delay }}">
            <div class="orbit-wrapper">
                  <div class="orbit-controls {{settings.nav_buttons == 1 ? '' : 'hide'}}">
                        <button class="orbit-previous"><span class="show-for-sr">Previous Slide</span>&#9664;&#xFE0E;</button>
                        <button class="orbit-next"><span class="show-for-sr">Next Slide</span>&#9654;&#xFE0E;</button>
                  </div>
                  <ul class="orbit-container ">
                      {% for entity in entities %}
                            {% set field =  entity.field_media_image ? entity.field_media_image : entity.field_media_video_file %}
                            {% set media =  field.0.entity.uri.value ? field.0.entity.uri.value : '' %}
                            {% set media_mime =  field.0.entity.filemime.value %}
                            {% set media_alt =  field.description ? field.description : field.alt %}
                            {% set url =  file_url(media) %}
                            {% set text = entity.field_text_layer.value %}
                            {% set block = entity.field_global_layout.0.target_id %}
                              <li class="{{ loop.first ? 'is-active' : '' }} orbit-slide">
                                    <figure class="orbit-figure">
                                        {% if 'image' in media_mime %}
                                          {% set image_style =  field.0.entity.uri.value | image_style(image_style) %}
                                          <img src="{{ file_url(media) }}" alt="{{media_alt}}">
                                        {% else %}
                                          <video autoplay loop muted playsinline>
                                            <source src="{{ url }}" type="{{media_mime}}"/>
                                          </video>
                                        {% endif %}
                                        {% if text_layer['#text'] == '' and custom_block == '' %}
                                          <figcaption class="orbit-caption grid-x grid-padding-x position-absolute-center">
                                              <div class="cell small-12">
                                                {% if text %}
                                                  {{ text|raw }}
                                                {% endif %}
                                                {% if text and block %}
                                                  <div class="space"></div>
                                                {% endif %}
                                                {% if block %}
                                                  <div class="block">
                                                      {{ drupal_entity('node', block, check_access=false) }}
                                                  </div>
                                                {% endif %}
                                              </div>
                                          </figcaption>
                                        {% endif %}
                                    </figure>
                              </li>
                        {% endfor %}
                    {% if text_layer or custom_block %}
                      <div class="slider-hero-static-caption position-absolute-center grid-padding-x">
                        <div class="cell small-12">
                          {% if text_layer['#text'] %}
                            {{ text_layer|raw }}
                          {% endif %}
                          {% if text_layer['#text'] and custom_block %}
                            <div class="space"></div>
                          {% endif %}
                          {% if custom_block %}
                            <div class="block">
                                {{ drupal_entity('node', block, check_access=false) }}
                            </div>
                          {% endif %}
                        </div>
                      </div>
                    {% endif %}
                  </ul>
            </div>
            <nav class="orbit-bullets {{settings.bullets == 1 ? '' : 'hide'}}">
                  {% for node in nodes %}
                        {% if loop.first %}
                              <button class="{{ loop.first ? 'is-active' : '' }}" data-slide="0"><span class="show-for-sr">slide details.</span><span class="show-for-sr">Current Slide</span></button>
                        {% else %}
                              <button data-slide="{{ loop.index0 }}"><span class="show-for-sr">slide details.</span></button>
                        {% endif %}
                  {% endfor %}
            </nav>
      </div>
{% else %}
      <div class="slider-hero-static position-relative">
        <div class="slider-content-wrapper">
          {% set entity = entities.0 %}
          {% set text = text_layer['#text'] ? text_layer : entity.field_text_layer.value %}
          
          {% set enable_split  = entity.field_enable_split_content.value ? true : false %}          
          {% set left_content  = entity.field_left_content.value  ? entity.field_left_content.value : '' %}
          {% set right_content = entity.field_right_content.value ? entity.field_right_content.value : '' %}
          
          {% set block = custom_block ? custom_block : entity.field_global_layout.0.target_id %}
          {% if embed %}
            <div class='embed-container'>
              <iframe src="{{ embed }}" frameborder="0" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
            </div>
          {% else %}
                  {% set field =  entity.field_media_image ? entity.field_media_image : entity.field_media_video_file %}
                  {% set media =  field.0.entity.uri.value ? field.0.entity.uri.value : '' %}
                  {% set media_mime =  field.0.entity.filemime.value %}
                  {% set media_alt =  field.description ? field.description : field.alt %}
                  {% set url =  file_url(media) %}
                    {% if 'image' in media_mime %}
                          {% set image_style =  field.0.entity.uri.value | image_style(image_style) %}
                          <img src="{{ file_url(media) }}" alt="{{media_alt}}">
                    {% else %}
                          <video autoplay loop muted playsinline>
                            <source src="{{ url }}" type="{{media_mime}}"/>
                          </video>
                    {% endif %}
          {% endif %}
          <div class="slider-hero-static-caption position-absolute-center grid-padding-x">
            <div class="cell small-12">
              
              {% if enable_split %}
                <div class="grid-container">
                  <div class="grid-x grid-margin-x grid-margin-y large-vertical-padding ">
                    <div class="cell large-auto large-vertical-padding  ">{{ left_content|raw }}</div>
                    <div class="cell large-auto large-vertical-padding  ">{{ right_content|raw }}</div>
                  </div>
                </div>
              
              {% elseif text %}
                {{ text|raw }}
              {% endif %}
              
              {% if text and block %}
                <div class="space"></div>
              {% endif %}
              
              {% if block %}
                <div class="block">
                    {{ drupal_entity('node', block, check_access=false) }}
                </div>
              {% endif %}
              
            </div>
          </div>
        </div>
      </div>
{% endif %}

</div>
