{#
/**
 * @file
 * Default theme implementation for views to display rows in a grid.
 *
 * Available variables:
 * - attributes: HTML attributes for the wrapping element.
 * - title: The title of this group of rows.
 * - view: The view object.
 * - rows: The rendered view results.
 * - options: The view plugin style options.
      data_bullets
      data_nav_buttons
      use_thumbnails_as_nav_buttons
      data_auto_play
      data_timer_delay
      data_use_mui
 * - items: A list of grid items. Each item contains a list of rows or columns.
 *   The order in what comes first (row or column) depends on which alignment
 *   type is chosen (horizontal or vertical).
 *   - attributes: HTML attributes for each row or column.
 *   - content: A list of columns or rows. Each row or column contains:
 *     - attributes: HTML attributes for each row or column.
 *     - content: The row or column contents.
 *
 * @see template_preprocess_views_view_grid()
 *
 * @ingroup themeable
 */
#}
{%
  set slide_classes = [
  ]
%}

{% if title %}
  <h3>{{ title }}</h3>
{% endif %}
<div class="tiny-slider">
  {% for row in rows %}
    <div>
      {{- row.content -}}
    </div>
  {% endfor %}
</div>
