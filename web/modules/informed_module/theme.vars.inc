<?php
   function theme_vars (){
      return [
        'informed_module' => [
          'render element' => 'children',
        ],
        'help' => [
          'variables' => [
          ],
        ],
        'links__language_block' => [
          'variables' => [
            'settings' => [],
          ],
        ],
        'slider' => [
          'variables' => [
             'entities' => [],
             'embed' => '',
             'text_layer' => '',
             'custom_block' => '',
             'image_style' => [],
             'settings' => [],
          ],
        ],
        'wysiwyg' => [
          'variables' => [
            'content' => [],
          ],
        ],
        'title_block' => [
          'variables' => [
            'content' => [],
            'settings' => [],
          ],
        ],
        'search_form' => [
          'variables' => [
            'settings' => [],
          ],
        ],
        'icon_link' => [
          'variables' => [
            'settings' => [],
          ],
        ],
        'image_block' => [
          'variables' => [
            'settings' => [],
          ],
        ],
        'info_panel_with_background' => [
          'variables' => [
            'settings' => [],
          ],
        ],
        'button_block' => [
          'variables' => [
              'settings' => [],
          ],
        ],
        'layout_renderer' => [
          'variables' => [
              'content' => [],
          ],
        ],
        'pardot' => [
          'variables' => [
            'source' => '',
            'site_name' => ''
          ],
        ],
      ];
   }
