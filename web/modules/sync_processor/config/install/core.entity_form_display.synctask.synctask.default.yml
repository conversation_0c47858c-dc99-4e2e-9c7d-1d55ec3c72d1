langcode: en
status: true
dependencies:
  config:
    - field.field.synctask.synctask.field_batch_file
    - field.field.synctask.synctask.field_batch_items
    - field.field.synctask.synctask.field_import_status
    - field.field.synctask.synctask.field_messages
    - field.field.synctask.synctask.field_processed
    - field.field.synctask.synctask.field_scheduled_run_time
    - field.field.synctask.synctask.field_chunks_ran
    - field.field.synctask.synctask.field_finish_time
    - field.field.synctask.synctask.field_total_chunks
  module:
    - entity_reference_revisions
    - file
    - sync_processor
    - text
id: synctask.synctask.default
targetEntityType: synctask
bundle: synctask
mode: default
content:
  created:
    type: datetime_timestamp
    weight: 4
    region: content
    settings: {  }
    third_party_settings: {  }
  field_batch_file:
    type: file_generic
    weight: 5
    region: content
    settings:
      progress_indicator: throbber
    third_party_settings: {  }
  field_scheduled_run_time:
    type: datetime_timestamp
    weight: 6
    region: content
    settings: {  }
    third_party_settings: {  }
  label:
    type: string_textfield
    weight: 0
    region: content
    settings:
      size: 60
      placeholder: ''
    third_party_settings: {  }
  langcode:
    type: language_select
    weight: 1
    region: content
    settings:
      include_locked: true
    third_party_settings: {  }
  status:
    type: boolean_checkbox
    weight: 8
    region: content
    settings:
      display_label: false
    third_party_settings: {  }
  uid:
    type: entity_reference_autocomplete
    weight: 3
    region: content
    settings:
      match_operator: CONTAINS
      match_limit: 10
      size: 60
      placeholder: ''
    third_party_settings: {  }
hidden:
  field_batch_items: true
  field_finish_time: true
  field_chunks_ran: true
  field_total_chunks: true
  field_import_status: true
  field_messages: true
  field_processed: true
  field_batch_ids: true
