langcode: en
status: true
dependencies:
  config:
    - field.field.synctask.synctask.field_batch_file
    - field.field.synctask.synctask.field_batch_items
    - field.field.synctask.synctask.field_import_status
    - field.field.synctask.synctask.field_messages
    - field.field.synctask.synctask.field_processed
    - field.field.synctask.synctask.field_scheduled_run_time
    - field.field.synctask.synctask.field_chunks_ran
    - field.field.synctask.synctask.field_finish_time
    - field.field.synctask.synctask.field_total_chunks
  module:
    - entity_reference_revisions
    - file
    - sync_processor
id: synctask.synctask.default
targetEntityType: synctask
bundle: synctask
mode: default
content:
  field_batch_file:
    type: file_default
    label: above
    settings:
      use_description_as_link_text: true
    third_party_settings: {  }
    weight: 1
    region: content
  field_batch_items:
    type: entity_reference_revisions_entity_view_ST
    label: above
    settings: { }
    third_party_settings: { }
    weight: 6
    region: content
  field_import_status:
    type: string
    label: above
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 3
    region: content
  field_messages:
    type: st_messages
    label: above
    settings: {  }
    third_party_settings: {  }
    weight: 5
    region: content
  field_processed:
    type: boolean
    label: above
    settings:
      format: default
      format_custom_false: ''
      format_custom_true: ''
    third_party_settings: {  }
    weight: 4
    region: content
  field_scheduled_run_time:
    type: timestamp
    label: above
    settings:
      date_format: medium
      custom_date_format: ''
      timezone: ''
      tooltip:
        date_format: long
        custom_date_format: ''
      time_diff:
        enabled: false
        future_format: '@interval hence'
        past_format: '@interval ago'
        granularity: 2
        refresh: 60
    third_party_settings: {  }
    weight: 2
    region: content
  field_finish_time:
    type: timestamp
    label: above
    settings:
      date_format: medium
      custom_date_format: ''
      timezone: ''
      tooltip:
        date_format: long
        custom_date_format: ''
      time_diff:
        enabled: false
        future_format: '@interval hence'
        past_format: '@interval ago'
        granularity: 2
        refresh: 60
    third_party_settings: { }
    weight: 4
    region: content
  label:
    type: string
    label: hidden
    settings:
      link_to_entity: false
    third_party_settings: {  }
    weight: 0
    region: content
hidden:
  created: true
  langcode: true
  search_api_excerpt: true
  status: true
  uid: true
  field_total_chunks: true
  field_chunks_ran: true
  field_batch_ids: true
