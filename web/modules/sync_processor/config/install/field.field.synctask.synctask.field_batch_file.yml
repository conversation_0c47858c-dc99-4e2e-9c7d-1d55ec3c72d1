langcode: en
status: true
dependencies:
  config:
    - field.storage.synctask.field_batch_file
  module:
    - file
    - sync_processor
id: synctask.synctask.field_batch_file
field_name: field_batch_file
entity_type: synctask
bundle: synctask
label: 'Batch File'
description: ''
required: true
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:file'
  handler_settings: {  }
  file_directory: 'batchSync/[date:custom:Y]/[date:custom:m]/'
  file_extensions: csv
  max_filesize: ''
  description_field: false
field_type: file
