/**
 * @file
 * Styles for administration pages.
 */

/**
 * Node add/edit form layout
 */

/* <PERSON>rrow screens */
.layout-region {
    box-sizing: border-box;
}

.sync-full {
  max-width: 100%;
}

/* Wide screens */
@media
screen and (min-width: 780px),
(orientation: landscape) and (min-device-height: 780px) {

    .layout-region-node-main,
    .layout-region-node-footer {
        float: left; /* LTR */
        box-sizing: border-box;
        width: 65%;
        padding-right: 2em; /* LTR */
    }

    [dir="rtl"] .layout-region-node-main,
    [dir="rtl"] .layout-region-node-footer {
        float: right;
        padding-right: 0;
        padding-left: 2em;
    }

    .layout-region-node-secondary {
        float: right; /* LTR */
        width: 35%;
    }

    [dir="rtl"] .layout-region-node-secondary {
        float: left;
    }

    /* @todo File an issue to add a standard class to all text-like inputs */
    .layout-region-node-secondary .form-autocomplete,
    .layout-region-node-secondary .form-text,
    .layout-region-node-secondary .form-tel,
    .layout-region-node-secondary .form-email,
    .layout-region-node-secondary .form-url,
    .layout-region-node-secondary .form-search,
    .layout-region-node-secondary .form-number,
    .layout-region-node-secondary .form-color,
    .layout-region-node-secondary textarea {
        box-sizing: border-box;
        width: 100%;
        max-width: 100%;
    }
}

/**
 * The vertical toolbar mode gets triggered for narrow screens, which throws off
 * the intent of media queries written for the viewport width. When the vertical
 * toolbar is on, we need to suppress layout for the original media width + the
 * toolbar width (240px). In this case, 240px + 780px.
 */
@media
screen and (max-width: 1020px) {

    .toolbar-vertical.toolbar-tray-open .layout-region-node-main,
    .toolbar-vertical.toolbar-tray-open .layout-region-node-footer,
    .toolbar-vertical.toolbar-tray-open .layout-region-node-secondary {
        float: none;
        width: auto;
        padding-right: 0;
    }
}
