<?php
namespace Drupal\sync_processor;

use <PERSON><PERSON>al\Core\Config\ConfigFactory;
use Drupal\Core\File\FileSystemInterface;
use Drupal\file\Entity\File;

class ApiConnector
{
	protected $settings;

	protected $filesystem;

	protected $domain;
	protected $apikey;
	protected $alias;
  protected $env;
  
	/**
	 * Constructs a ImportManager object.
	 */
	public function __construct($config)
	{
		$this->settings = $config;
		$this->filesystem = \Drupal::service('file_system');
	}

	public function setConnection() {
		$this->domain = $this->settings->get('domain');
		$this->apikey = $this->settings->get('apikey');
		$this->alias = $this->settings->get('alias');

    if (isset($_ENV['PANTHEON_ENVIRONMENT']) && !empty($_ENV['PANTHEON_ENVIRONMENT']))
      $this->env = $_ENV['PANTHEON_ENVIRONMENT'];
    else
      $this->env = 'live';

		if (empty($this->domain) || empty($this->apikey) || empty($this->alias)) {
			\Drupal::logger('sync_processor')->info('Missing required parameters to connect to api');
			return false;
		}

		return true;
	}

	public function test() {
		// test api connection
		if ($this->setConnection()) {
			try {
				$client = \Drupal::httpClient();
				$request = $client->get($this->domain . '/list', ['query' => ['authkey' => $this->apikey, 'salias' => $this->alias, 'env' => $this->env]]);
			}
			catch (\Exception $error) {
				$logger = \Drupal::logger('sync_processor');
				$logger->error($error->getMessage());
				return false;
			}

			if ($request->getStatusCode() != 200) {
				return false;
			}

			return true;
		}
		return false;
	}

	public function list() {
		// get list of remote files
		if ($this->test()) {
			$client = \Drupal::httpClient();
			$request = $client->get($this->domain . '/list', ['query' => ['authkey' => $this->apikey, 'salias' => $this->alias, 'env' => $this->env]]);
			$response = json_decode($request->getBody());

			return $response->data;
		}
		return false;
	}

	public function fetch($fileName) {
		if ($this->test()) {
			//fetch remote file
			$file_dir = 'public://batchSync/' . date('Y/m');
			$this->filesystem->prepareDirectory($file_dir, FileSystemInterface::CREATE_DIRECTORY);
			$local_file_name = basename($fileName);
			$local_file = $this->filesystem->realpath("{$file_dir}/{$local_file_name}");

			$client = \Drupal::httpClient();
			$request = $client->get($this->domain . '/fetch', ['query' => ['authkey' => $this->apikey, 'salias' => $this->alias, 'fn' => $fileName, 'env' => $this->env]]);
			$data = $request->getBody();
			$file = $this->filesystem->saveData($data, $local_file);

			if (empty($file)) {
				\Drupal::logger('sync_processor')->info('Failed to download @file from remote.<br/>', ['@file' => $fileName]);
				return false;
			}

			$filed = File::create([
				'filename' => basename($file),
				'uri' => $file_dir . '/' . basename($file),
				'status' => 1,
				'uid' => 1,
			]);
			$filed->setPermanent();
			$filed->save();

			\Drupal::logger('sync_processor')->info('Downloaded @file from remote.<br/>', ['@file' => $fileName]);
			return [
				'id' => $filed->id(),
				'name' => basename($file)
				];
		}
		return false;
	}

	public function done($fileName) {
		if ($this->test()) {
			$client = \Drupal::httpClient();
			$request = $client->get($this->domain . '/processed', ['query' => ['authkey' => $this->apikey, 'salias' => $this->alias, 'fn' => $fileName, 'env' => $this->env]]);
			$response = json_decode($request->getBody());

			return $response->message;
		}
		return false;
	}
}
