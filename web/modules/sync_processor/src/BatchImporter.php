<?php
namespace Drupal\sync_processor;

use Drupal\Core\Config\ConfigFactory;
use Drupal\Core\File\FileSystemInterface;
use Drupal\Core\Queue\QueueFactory;
use Drupal\Core\Queue\QueueInterface;
use Drupal\file\Entity\File;
use Exception;

class BatchImporter {
	protected $settings;

	protected $filesystem;

	public function __construct(ConfigFactory $config, FileSystemInterface $filesystem)
	{
		$this->settings = $config->get('sync_processor.sync_settings');
		$this->filesystem = $filesystem;
	}

	public function start() {
		try {
			$existing_query = \Drupal::entityQuery('synctask')
				->condition('status', 1)
				->condition('field_processed', 0)
				->condition('field_import_status', 'Queued')
				->sort('id', 'ASC')
				->accessCheck(FALSE);
			$existing_results = $existing_query->execute();

			if (empty($existing_results)) {
				$ctime = strtotime('now');
				$query = \Drupal::entityQuery('synctask')
					->condition('status', 1)
					->condition('field_processed', 0)
					->condition('field_import_status', 'New')
					->condition('field_scheduled_run_time', $ctime, '<=')
					->sort('id', 'ASC')
					->accessCheck(FALSE);
				$results = $query->execute();

				if (!empty($results)) {
					$result = reset($results);
					$st = new SyncTask($this->settings);
					if ($st->load($result)) {
						$st->queue();
						$entity = \Drupal::entityTypeManager()->getStorage('synctask')->load($result);
						if ($entity instanceof syncTaskInterface) {
							$entity->set('field_import_status', 'Queued');
							$entity->save();
						}
						$logger = \Drupal::logger('sync_processor');
						$logger->info("Task id: {$result} queued for import");
					}
				}
			}
		}
		catch (Exception $e) {
			$logger = \Drupal::logger('sync_processor');
			$logger->error($e->getMessage());
		}
		return false;
	}

	public function retry($id, $now = false) {

	}

	private function getNextRun() {
		//get next run time based on set frequency
		$freq_int = $this->settings->get('frequency_int');
		$freq_i = $this->settings->get('frequency_type');
		$time = 0;
		$today = strtotime("today");

		switch ($freq_i) {
			case 'm':
				$time = strtotime('+'. $freq_int .' minutes');
				break;
			case 'h':
				$d = date('H:00:00');
				$time = strtotime('+'. $freq_int .' hours', strtotime($d));
				break;
			case 'd':
				$time = strtotime('+'. $freq_int .' days', $today);
				break;
			default:
				$time = strtotime('now');
		}
		return $time;
	}

	public function fetchNew() {
		$api = new ApiConnector($this->settings);

		$newFiles = $api->list();

		if (!empty($newFiles)) {
			foreach ($newFiles as $files) {
				if (!empty($files)) {
					foreach ($files as $file) {
						if ($file->type === 'csv') {
							$download = $api->fetch($file->name);
							if (!empty($download)) {
								$stask = new SyncTask($this->settings);

								$taskObject = [
									'label' => $download['name'],
									'uid' => 0,
									'field_batch_file' => $download['id'],
									'field_import_status' => 'New',
									'field_scheduled_run_time' => $this->getNextRun()
								];

								if ($stask->create($taskObject) && $api->done($file->name)) {
									\Drupal::logger('sync_processor')->info('Created new sync task<br/>');
								} else {
									\Drupal::logger('sync_processor')->warning('Error while trying to import files<br/>');
								}

							}
						}
					}
				}
			}
		}
	}
}
