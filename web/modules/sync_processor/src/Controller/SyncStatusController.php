<?php

namespace Drupal\sync_processor\Controller;

use <PERSON><PERSON>al\Core\Link;
use <PERSON><PERSON>al\Core\Site\Settings;
use <PERSON>upal\Core\StringTranslation\TranslatableMarkup;
use <PERSON><PERSON>al\sync_processor\ApiConnector;
use <PERSON>ymfony\Component\DependencyInjection\ContainerInterface;
use Symfony\Component\HttpFoundation\Response;
use Drupal\Core\DependencyInjection\ContainerInjectionInterface;
use Drupal\system\SystemManager;
use Drupal\Core\StringTranslation\StringTranslationTrait;
use Drupal\Core\Url;

/**
 * Returns responses for System Info routes.
 */
class SyncStatusController implements ContainerInjectionInterface {

	use StringTranslationTrait;

	/**
	 * System Manager Service.
	 *
	 * @var \Drupal\system\SystemManager
	 */
	protected $systemManager;

	/**
	 * {@inheritdoc}
	 */
	public static function create(ContainerInterface $container) {
		return new static(
			$container->get('system.manager')
		);
	}

	/**
	 * Constructs a SystemInfoController object.
	 *
	 * @param \Drupal\system\SystemManager $systemManager
	 *   System manager service.
	 */
	public function __construct(SystemManager $systemManager) {
		$this->systemManager = $systemManager;

		if (!defined('SS_REQUIREMENT_INFO'))
			define('SS_REQUIREMENT_INFO', -1);
		if (!defined('SS_REQUIREMENT_OK'))
			define('SS_REQUIREMENT_OK', 0);
		if (!defined('SS_REQUIREMENT_WARNING'))
			define('SS_REQUIREMENT_WARNING', 1);
		if (!defined('SS_REQUIREMENT_ERROR'))
			define('SS_REQUIREMENT_ERROR', 2);
	}

	/**
	 * Displays the site status report.
	 *
	 * @return array
	 *   A render array containing a list of system requirements for the Drupal
	 *   installation and whether this installation meets the requirements.
	 */
	public function status() {
		$requirements = $this->listItems();
		return ['#type' => 'sync_status_page', '#requirements' => $requirements];
	}

	public function listItems() {
		$m_ver = \Drupal::service('extension.list.module')->getExtensionInfo('sync_processor');

		$apiSettings = \Drupal::config('sync_processor.sync_settings');
		$apiStatus = \Drupal::config('sync_processor.status');

		$next_remote = \Drupal::service('date.formatter')->formatTimeDiffUntil($apiStatus->get('next'));

		$requirements = [
			'selfver' => [
				'title' => 'Sync Processor',
				'value' => $m_ver['version'],
				'severity' => -1,
				'weight' => -10
			],
			'apicheck' => [
				'title' => 'External Api Status',
				'value' => '',
				'severity' => -1,
				'weight' => -1
			],
			'nextimport' => [
				'title' => 'Next Remote Import',
				'value' => !empty($apiStatus->get('next'))? ((string)$next_remote !== '0 seconds')? $next_remote : 'Less than a minute' : 'Less than 2 minutes',
				'severity' => -1,
				'weight' => 2
			],
			'notifications' => [
				'title' => 'Notification Email(s)',
				'value' => !empty($apiSettings->get('notification_email'))? $apiSettings->get('notification_email') : 'Not Set',
				'severity' => -1,
				'weight' => 10
			],
			'dupid' => [
				'title' => 'Multiple products with the same labware id',
				'value' => [],
				'severity' => 0,
				'weight' => 11
			],
			'noid' => [
				'title' => 'Products without a labware id',
				'value' => [],
				'severity' => 0,
				'weight' => 12
			],
		];

		if (empty($apiSettings->get('domain')) || empty($apiSettings->get('apikey')) || empty($apiSettings->get('alias'))) {
			$requirements['apicheck']['value'] = 'Settings not configured fully';
			$requirements['apicheck']['severity'] = 1;
		} else {
			$test = new ApiConnector($apiSettings);

			if ($test->test()){
				$requirements['apicheck']['value'] = 'Available';
				$requirements['apicheck']['severity'] = -1;
			} else {
				$requirements['apicheck']['value'] = 'Error checking status';
				$requirements['apicheck']['severity'] = 2;
			}
		}

		$requirements['cron'] = $this->getCronReq();

		//check if multiple products have the same labware id
		$dup_query = \Drupal::entityQueryAggregate('node')
			->condition('type', 'product')
			->condition('status', 1)
			->condition('field_labware_id', '', '!=')
			->groupBy('field_labware_id')
			->conditionAggregate('field_labware_id', 'COUNT', 1, '>')
			->accessCheck(FALSE);
		$dup_prod_match = $dup_query->execute();

		if (!empty($dup_prod_match)) {
			foreach ($dup_prod_match as $dup) {
				$pquery = \Drupal::entityQuery('node')
					->condition('type', 'product')
					->condition('field_labware_id', $dup['field_labware_id'])
					->condition('status', 1)
					->accessCheck(FALSE);
				$prod_match = $pquery->execute();

				if (empty($prod_match) || count($prod_match) < 2)
					continue;

				$string = [];
				foreach($prod_match as $pid) {
					$url = Url::fromRoute('entity.node.edit_form', ['node' => $pid]);
					$link = Link::fromTextAndUrl($pid, $url);
					$string[] = $link->toString();
				}

				$string = implode(', ', $string);
				$requirements['dupid']['value'][] = [
					'#tag' => 'span',
					'#type' => 'html_tag',
					'#value' => '(' . $string . ')'
				];
				$requirements['dupid']['severity'] = 1;
			}
		} else {
			$requirements['dupid']['value'] = 'None Found';
			$requirements['dupid']['severity'] = 0;
		}

		$pquery = \Drupal::entityQuery('node')
			->condition('type', 'product')
			->condition('status', 1)
			->notExists('field_labware_id')
			->accessCheck(FALSE);
		$prod_match = $pquery->execute();

		if (!empty($prod_match)) {
			$string = [];
			foreach($prod_match as $pid) {
				$url = Url::fromRoute('entity.node.edit_form', ['node' => $pid]);
				$link = Link::fromTextAndUrl($pid, $url);
				$string[] = $link->toString();
			}

			$string = implode(', ', $string);
			$requirements['noid']['value'][] = [
				'#tag' => 'span',
				'#type' => 'html_tag',
				'#value' => $string
			];
			$requirements['noid']['severity'] = 1;
		} else {
			$requirements['noid']['value'] = 'None Found';
			$requirements['noid']['severity'] = 0;
		}

		uasort($requirements, function ($a, $b) {
			if (!isset($a['weight'])) {
				if (!isset($b['weight'])) {
					return strcasecmp($a['title'], $b['title']);
				}
				return -$b['weight'];
			}
			return isset($b['weight']) ? $a['weight'] - $b['weight'] : $a['weight'];
		});

		return $requirements;
	}

	public function getCronReq() {
		$cron_last = \Drupal::state()->get('system.cron_last');
		if (!is_numeric($cron_last)) {
			$cron_last = \Drupal::state()->get('install_time', 0);
		}

		$summary = t('Last run @time ago', ['@time' => \Drupal::service('date.formatter')->formatTimeDiffSince($cron_last)]);

		$requirements = [
			'title' => t('Cron maintenance tasks'),
			'severity' => -1,
			'value' => $summary,
		];

		$requirements['description'][] = [
			[
				'#type' => 'link',
				'#prefix' => '(',
				'#title' => t('more information'),
				'#suffix' => ')',
				'#url' => Url::fromRoute('system.cron_settings'),
			],
			[
				'#prefix' => '<span class="cron-description__run-cron">',
				'#suffix' => '</span>',
				'#type' => 'link',
				'#title' => t('Run cron'),
				'#url' => Url::fromRoute('system.run_cron'),
			],
		];

		return $requirements;
	}

}
