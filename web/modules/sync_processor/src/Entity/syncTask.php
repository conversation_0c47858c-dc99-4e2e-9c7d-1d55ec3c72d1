<?php declare(strict_types = 1);

namespace Drupal\sync_processor\Entity;

use <PERSON><PERSON>al\Core\Entity\Annotation\ContentEntityType;
use <PERSON><PERSON>al\Core\Entity\EntityChangedTrait;
use <PERSON><PERSON>al\Core\Entity\EntityStorageInterface;
use <PERSON><PERSON>al\Core\Entity\EntityTypeInterface;
use Drupal\Core\Entity\RevisionableContentEntityBase;
use Drupal\Core\Field\BaseFieldDefinition;
use <PERSON><PERSON>al\sync_processor\syncTaskInterface;
use <PERSON><PERSON>al\user\EntityOwnerTrait;

/**
 * Defines the syncTask entity class.
 *
 * @ContentEntityType(
 *   id = "synctask",
 *   label = @Translation("Sync Task"),
 *   persistent_cache = FALSE,
 *   render_cache = FALSE,
 *   label_collection = @Translation("Sync Tasks"),
 *   label_singular = @Translation("Sync Task"),
 *   label_plural = @Translation("Sync Tasks"),
 *   label_count = @PluralTranslation(
 *     singular = "@count sync task",
 *     plural = "@count sync task",
 *   ),
 *   handlers = {
 *     "list_builder" = "Drupal\sync_processor\syncTaskListBuilder",
 *     "views_data" = "Drupal\views\EntityViewsData",
 *     "access" = "Drupal\sync_processor\syncTaskAccessControlHandler",
 *     "form" = {
 *       "add" = "Drupal\sync_processor\Form\syncTaskForm",
 *       "edit" = "Drupal\sync_processor\Form\syncTaskForm",
 *       "delete" = "Drupal\Core\Entity\ContentEntityDeleteForm",
 *       "delete-multiple-confirm" = "Drupal\Core\Entity\Form\DeleteMultipleForm",
 *     },
 *     "route_provider" = {
 *       "html" = "Drupal\Core\Entity\Routing\AdminHtmlRouteProvider",
 *     },
 *   },
 *   base_table = "synctask",
 *   data_table = "synctask_field_data",
 *   revision_table = "synctask_revision",
 *   revision_data_table = "synctask_field_revision",
 *   show_revision_ui = TRUE,
 *   translatable = TRUE,
 *   admin_permission = "administer synctask",
 *   entity_keys = {
 *     "id" = "id",
 *     "revision" = "revision_id",
 *     "langcode" = "langcode",
 *     "label" = "label",
 *     "uuid" = "uuid",
 *     "owner" = "uid",
 *   },
 *   revision_metadata_keys = {
 *     "revision_user" = "revision_uid",
 *     "revision_created" = "revision_timestamp",
 *     "revision_log_message" = "revision_log",
 *   },
 *   links = {
 *     "collection" = "/admin/content/synctask",
 *     "add-form" = "/synctask/add",
 *     "canonical" = "/synctask/{synctask}",
 *     "edit-form" = "/synctask/{synctask}/edit",
 *     "delete-form" = "/synctask/{synctask}/delete",
 *     "delete-multiple-form" = "/admin/content/synctask/delete-multiple",
 *   },
 *   field_ui_base_route = "entity.synctask.settings",
 * )
 */
final class syncTask extends RevisionableContentEntityBase implements syncTaskInterface {

  use EntityChangedTrait;
  use EntityOwnerTrait;

  /**
   * {@inheritdoc}
   */
  public function preSave(EntityStorageInterface $storage): void {
    parent::preSave($storage);
    if (!$this->getOwnerId()) {
      // If no owner has been set explicitly, make the anonymous user the owner.
      $this->setOwnerId(0);
    }
  }

  /**
   * {@inheritdoc}
   */
  public static function baseFieldDefinitions(EntityTypeInterface $entity_type): array {

    $fields = parent::baseFieldDefinitions($entity_type);

    $fields['label'] = BaseFieldDefinition::create('string')
      ->setRevisionable(TRUE)
      ->setTranslatable(TRUE)
      ->setLabel(t('Label'))
      ->setRequired(TRUE)
      ->setSetting('max_length', 255)
      ->setDisplayOptions('form', [
        'type' => 'string_textfield',
        'weight' => -5,
      ])
      ->setDisplayConfigurable('form', TRUE)
      ->setDisplayOptions('view', [
        'label' => 'hidden',
        'type' => 'string',
        'weight' => -5,
      ])
      ->setDisplayConfigurable('view', TRUE);

    $fields['status'] = BaseFieldDefinition::create('boolean')
      ->setRevisionable(TRUE)
      ->setLabel(t('Status'))
      ->setDefaultValue(TRUE)
      ->setSetting('on_label', 'Enabled')
      ->setDisplayOptions('form', [
        'type' => 'boolean_checkbox',
        'settings' => [
          'display_label' => FALSE,
        ],
        'weight' => 0,
      ])
      ->setDisplayConfigurable('form', TRUE)
      ->setDisplayOptions('view', [
        'type' => 'boolean',
        'label' => 'above',
        'weight' => 0,
        'settings' => [
          'format' => 'enabled-disabled',
        ],
      ])
      ->setDisplayConfigurable('view', TRUE);

    $fields['uid'] = BaseFieldDefinition::create('entity_reference')
      ->setRevisionable(TRUE)
      ->setTranslatable(TRUE)
      ->setLabel(t('Author'))
      ->setSetting('target_type', 'user')
      ->setDefaultValueCallback(self::class . '::getDefaultEntityOwner')
      ->setDisplayOptions('form', [
        'type' => 'entity_reference_autocomplete',
        'settings' => [
          'match_operator' => 'CONTAINS',
          'size' => 60,
          'placeholder' => '',
        ],
        'weight' => 15,
      ])
      ->setDisplayConfigurable('form', TRUE)
      ->setDisplayOptions('view', [
        'label' => 'above',
        'type' => 'author',
        'weight' => 15,
      ])
      ->setDisplayConfigurable('view', TRUE);

    $fields['created'] = BaseFieldDefinition::create('created')
      ->setLabel(t('Authored on'))
      ->setTranslatable(TRUE)
      ->setDescription(t('The time that the Sync Task was created.'))
      ->setDisplayOptions('view', [
        'label' => 'above',
        'type' => 'timestamp',
        'weight' => 20,
      ])
      ->setDisplayConfigurable('form', TRUE)
      ->setDisplayOptions('form', [
        'type' => 'datetime_timestamp',
        'weight' => 20,
      ])
      ->setDisplayConfigurable('view', TRUE);

    $fields['changed'] = BaseFieldDefinition::create('changed')
      ->setLabel(t('Changed'))
      ->setTranslatable(TRUE)
      ->setDescription(t('The time that the Sync Task was last edited.'));

    return $fields;
  }

  public function isActive()
  {
    return (bool) $this->get('status')->getString();
  }

	/**
	 * {@inheritdoc}
	 */
	public function logger(array|string $item, string $type, array $custom = [])
	{
		if (!in_array($type, ['mail','error','notice','itemerror','unpublished']) || ($type === 'custom' && empty($custom))) {
			$logger = \Drupal::logger('sync_processor');
			$logger->error("Invalid option ({$type}) used with logger interface or no custom log parameters were defined.");
			return;
		}

		$existing = $this->get('field_messages')->getString();
		$existing = json_decode($existing, true);

		$new = [
			'text' => $item
		];

		$existing[$type][] = $new;
		$merged = json_encode($existing);

		$this->set('field_messages', $merged);
		$this->save();
	}

	/**
	 * {@inheritdoc}
	 */
	public function getLogItem(string $type, array $custom = [])
	{
		if (!in_array($type, ['mail','error','notice','itemerror','unpublished']) || ($type === 'custom' && empty($custom))) {
			$logger = \Drupal::logger('sync_processor');
			$logger->error("Invalid option ({$type}) used with logger interface or no custom log parameters were defined.");
			return false;
		}

		$existing = $this->get('field_messages')->getString();
		$existing = json_decode($existing, true);

		if (isset($existing[$type]) && !empty($existing[$type]))
			return $existing[$type];

		return false;
	}

	public function logEmail($text, $type)
	{
		$existing = $this->get('field_messages')->getString();
		$existing = json_decode($existing, true);

		$new = [
			'type' => $type,
			'text' => $text
		];

		$merged = $existing['mail'][] = $new;
		$merged = json_encode($merged);

		$this->set('field_messages', $merged);
		$this->save();
	}

	public function logError($item)
	{
		if (!is_array($item))
			$item = json_decode(json_encode($item), true);

		$existing = $this->get('field_messages')->getString();
		$existing = json_decode($existing, true);

		$merged = $existing['error']['items'][] = $item;
		$merged = json_encode($merged);

		$this->set('field_messages', $merged);
		$this->save();
	}
}
