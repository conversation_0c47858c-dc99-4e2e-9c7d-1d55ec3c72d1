<?php
namespace Drupal\sync_processor;

use Drupal\file\Entity\File;

class FileProcessor {
	protected $settings;

	protected $filesystem;

	protected $file;

	/**
	 * Constructs a FileProcessor object.
	 */
	public function __construct($config)
	{
		$this->settings = $config;
		$this->filesystem = \Drupal::service('file_system');;
	}

	public function load($entity) {
		if ($entity instanceof syncTaskInterface) {
			$fid = $entity->get('field_batch_file')->first()->getValue();
			$file = $this->getFile($fid['target_id']);

			if (!empty($file))
				return $this->file;
		}

		return false;
	}

	public function getFile($fid) {
		$file = File::load($fid);

		$ext = pathinfo($file->getFileUri())['extension'] ?? false;
		$inputFileType = ucfirst($ext);
		$reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader($inputFileType);
		$reader->setReadDataOnly(true);
		$reader->setReadEmptyCells(false);
		/**  Load $inputFileName to a Spreadsheet Object  **/
		$spreadsheet = $reader->load($file->getFileUri());
		$worksheet = $spreadsheet->getActiveSheet();
		$data = $worksheet->toArray();
		$data = $this->convert_to_keys($data);

		if (!empty($data)) {
			$this->file = $data;
			return true;
		}
		return false;
	}

	private function convert_to_keys(array $array, ?int $index = null): array
	{
		// Shift the keys from the first row:
		$keys = array_shift($array);

		$named = [];
		// Loop and build remaining rows into a new array:

		foreach($array as $ln => $vals) {

			// Using specific index or row numbers?
			$key = !is_null($index) ? $vals[$index] : $ln;

			//clean values
			$vals = $this->cleanData($vals);

			// Combine keys and values:
			$named[$key] = array_combine($keys, $vals);
		}

		return $named;
	}

	private function cleanData(array $data) {
		foreach ($data as $ind => $item) {
      if (!empty($item))
  			$data[$ind] = preg_replace('/[\x00-\x1F\x80-\xFF]/', '', $item);
		}
		return $data;
	}

	public function import($file) {
		$spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
	}
}
