<?php

namespace Drupal\sync_processor\Form;

use <PERSON><PERSON>al\Core\Form\ConfigFormBase;
use <PERSON><PERSON>al\Core\Form\FormStateInterface;

/**
 * Class syncSettings.
 */
class syncSettings extends ConfigFormBase
{

    /**
     * {@inheritdoc}
     */
    protected function getEditableConfigNames()
    {
        return [
            'sync_processor.sync_settings',
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function getFormId()
    {
        return 'sync_processor_sync_settings';
    }

    /**
     * {@inheritdoc}
     */
    public function buildForm(array $form, FormStateInterface $form_state)
    {
        $config = $this->config('sync_processor.sync_settings');

        $form['notification'] = [
            '#type' => 'details',
            '#title' => $this->t('Notifications'),
            '#open' => TRUE,
            '#weight' => 0,
        ];
        $form['notification']['notification_email'] = [
            '#type' => 'textarea',
            '#title' => $this->t('Email address(s)'),
			'#description' => $this->t('Separate multiple email addresses with a comma'),
            '#default_value' => $config->get('notification_email'),
        ];
		$form['notification']['email_domain'] = [
			'#type' => 'textfield',
			'#title' => $this->t('Current site domain'),
			'#description' => $this->t('Domain of the site to use for links in emails'),
			'#default_value' => $config->get('email_domain') ?? 'https://choice.wetestyoutrust.com',
		];

		$form['schedule'] = [
			'#type' => 'details',
			'#title' => $this->t('Schedule'),
			'#open' => TRUE,
			'#weight' => 1,
		];

		$form['schedule']['c'] = [
			'#type' => 'container',
			'#attributes' => array('class' => array('form--inline', 'clearfix')),
		];

		$form['schedule']['c']['frequency_int'] = [
			'#type' => 'number',
			'#title' => $this->t('Frequency'),
			'#required' => true,
			'#default_value' => $config->get('frequency_int') ?? '12',
		];

		$form['schedule']['c']['frequency_type'] = [
			'#type' => 'select',
			'#title' => '',
			'#required' => true,
			'#default_value' => $config->get('frequency_type') ?? 'h',
			'#options' => [
				'm' => $this
					->t('Minute(s)'),
				'h' => $this
					->t('Hour(s)'),
				'd' => $this
					->t('Day(s)'),
			],
		];

		$form['schedule']['history'] = [
			'#type' => 'select',
			'#title' => $this->t('History Rotation'),
			'#description' => $this->t('How many import tasks should be kept before deletion'),
			'#required' => true,
			'#default_value' => $config->get('history'),
			'#options' => [
				'10' => $this
					->t('10'),
				'15' => $this
					->t('15'),
				'20' => $this
					->t('20'),
				'50' => $this
					->t('50'),
				'100' => $this
					->t('100'),
			],
		];

        $form['server'] = [
            '#type' => 'details',
            '#title' => $this->t('Connection'),
            '#open' => TRUE,
            '#weight' => 2,
        ];
        $form['server']['domain'] = [
            '#type' => 'textfield',
            '#title' => $this->t('Domain'),
            '#required' => true,
            '#default_value' => $config->get('domain') ?? 'https://wtyt.p1.sdev.site',
        ];
		$form['server']['apikey'] = [
			'#type' => 'textfield',
			'#title' => $this->t('Api Key'),
			'#required' => true,
			'#default_value' => $config->get('apikey'),
		];
        $form['server']['alias'] = [
            '#type' => 'select',
            '#title' => $this->t('Site Alias'),
            '#required' => true,
            '#default_value' => $config->get('alias'),
			'#options' => [
				'is' => $this
					->t('Sport'),
				'ic' => $this
					->t('Choice'),
				'ii' => $this
					->t('Ingredient'),
				'ip' => $this
					->t('Protein'),
			],
        ];

        return parent::buildForm($form, $form_state);
    }

    /**
     * {@inheritdoc}
     */
    public function submitForm(array &$form, FormStateInterface $form_state)
    {
        parent::submitForm($form, $form_state);

        $this->config('sync_processor.sync_settings')
            ->set('notification_email', $form_state->getValue('notification_email'))
			->set('email_domain', $form_state->getValue('email_domain'))
            ->set('frequency_int', $form_state->getValue('frequency_int'))
            ->set('frequency_type', $form_state->getValue('frequency_type'))
			->set('history', $form_state->getValue('history'))
            ->set('domain', $form_state->getValue('domain'))
            ->set('apikey', $form_state->getValue('apikey'))
			->set('alias', $form_state->getValue('alias'))
            ->save();
    }
}
