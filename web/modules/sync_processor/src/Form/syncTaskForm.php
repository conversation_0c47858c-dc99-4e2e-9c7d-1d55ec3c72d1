<?php declare(strict_types = 1);

namespace Drupal\sync_processor\Form;

use <PERSON><PERSON>al\Component\Datetime\TimeInterface;
use <PERSON><PERSON>al\Core\Datetime\DateFormatter;
use Dr<PERSON>al\Core\Datetime\DateFormatterInterface;
use <PERSON><PERSON>al\Core\Entity\ContentEntityForm;
use Dr<PERSON>al\Core\Entity\EntityRepositoryInterface;
use Drupal\Core\Entity\EntityTypeBundleInfoInterface;
use Drupal\Core\Form\FormStateInterface;
use Drupal\Core\Session\AccountInterface;
use Drupal\Core\TempStore\PrivateTempStoreFactory;

/**
 * Form controller for the syncTask entity edit forms.
 */
final class syncTaskForm extends ContentEntityForm {
	/**
	 * The date formatter service.
	 *
	 * @var \Drupal\Core\Datetime\DateFormatterInterface
	 */
	protected $dateFormatter;

	/**
	 * {@inheritdoc}
	 */
	public function __construct(EntityRepositoryInterface $entity_repository, EntityTypeBundleInfoInterface $entity_type_bundle_info, TimeInterface $time) {
		parent::__construct($entity_repository, $entity_type_bundle_info, $time);
		$this->dateFormatter = \Drupal::service('date.formatter');
	}

	/**
	 * {@inheritdoc}
	 */
	public function form(array $form, FormStateInterface $form_state) {
		/** @var \Drupal\node\NodeInterface $node */
		$node = $this->entity;

//		if ($this->operation == 'edit') {
//			$form['#title'] = $this->t('<em>Edit @type</em> @title', [
//				'@type' => node_get_type_label($node),
//				'@title' => $node->label(),
//			]);
//		}

		// Changed must be sent to the client, for later overwrite error checking.
		$form['changed'] = [
			'#type' => 'hidden',
			'#default_value' => $node->getChangedTime(),
		];

		$form = parent::form($form, $form_state);

		$form['advanced']['#attributes']['class'][] = 'entity-meta';

		$form['meta'] = [
			'#type' => 'details',
			'#group' => 'advanced',
			'#weight' => -10,
			'#title' => $this->t('Status'),
			'#attributes' => ['class' => ['entity-meta__header']],
			'#tree' => TRUE,
			'#access' => $this->currentUser()->hasPermission('administer nodes'),
		];
		$form['meta']['istatus'] = [
			'#type' => 'item',
			'#title' => $this->t('Import Status'),
			'#markup' => $this->t($node->get('field_import_status')->getString()),
		];
		$form['meta']['processed'] = [
			'#type' => 'item',
			'#title' => $this->t('Processed'),
			'#markup' => $node->get('field_processed')->value ? $this->t('Yes') : $this->t('No'),
			'#access' => !$node->isNew(),
		];
		$form['meta']['changed'] = [
			'#type' => 'item',
			'#title' => $this->t('Last saved'),
			'#markup' => !$node->isNew() ? $this->dateFormatter->format($node->getChangedTime(), 'short') : $this->t('Not saved yet'),
			'#wrapper_attributes' => ['class' => ['entity-meta__last-saved']],
		];
		$form['meta']['author'] = [
			'#type' => 'item',
			'#title' => $this->t('Author'),
			'#markup' => $node->getOwner()->getAccountName(),
			'#wrapper_attributes' => ['class' => ['entity-meta__author']],
		];

		$form['status']['#group'] = 'footer';

		// Node author information for administrators.
		$form['author'] = [
			'#type' => 'details',
			'#title' => $this->t('Authoring information'),
			'#group' => 'advanced',
			'#attributes' => [
				'class' => ['node-form-author'],
			],
			'#attached' => [
				'library' => ['node/drupal.node'],
			],
			'#weight' => 90,
			'#optional' => TRUE,
		];

		if (isset($form['uid'])) {
			$form['uid']['#group'] = 'author';
		}

		if (isset($form['created'])) {
			$form['created']['#group'] = 'author';
		}

		$form['#attached']['library'][] = 'node/form';

    if ($node->get('field_import_status')->getString() == 'Cancelled' || $node->get('field_import_status')->getString() == 'Done') {
      $form['status']['#attributes']['class'][] = 'hidden';
    }

		return $form;
	}

  /**
   * {@inheritdoc}
   */
  public function save(array $form, FormStateInterface $form_state): int {
    $result = parent::save($form, $form_state);

    $message_args = ['%label' => $this->entity->toLink()->toString()];
    $logger_args = [
      '%label' => $this->entity->label(),
      'link' => $this->entity->toLink($this->t('View'))->toString(),
    ];

    switch ($result) {
      case SAVED_NEW:
        $this->messenger()->addStatus($this->t('New sync task %label has been created.', $message_args));
        $this->logger('sync_processor')->notice('New sync task %label has been created.', $logger_args);
        break;

      case SAVED_UPDATED:
        $this->messenger()->addStatus($this->t('The sync task %label has been updated.', $message_args));
        $this->logger('sync_processor')->notice('The sync task %label has been updated.', $logger_args);
        break;

      default:
        throw new \LogicException('Could not save the entity.');
    }

    $form_state->setRedirectUrl($this->entity->toUrl());

    return $result;
  }

}
