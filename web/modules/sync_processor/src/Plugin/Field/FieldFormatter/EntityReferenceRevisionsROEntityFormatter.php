<?php

namespace Drupal\sync_processor\Plugin\Field\FieldFormatter;

use <PERSON><PERSON>al\Core\Cache\CacheableMetadata;
use Drupal\Core\Entity\EntityDisplayRepositoryInterface;
use <PERSON><PERSON>al\Core\Entity\EntityInterface;
use <PERSON><PERSON>al\Core\Field\EntityReferenceFieldItemListInterface;
use Drupal\Core\Field\FieldDefinitionInterface;
use Drupal\Core\Field\FieldItemListInterface;
use Drupal\Core\Form\FormStateInterface;
use Drupal\Core\Link;
use Drupal\Core\Logger\LoggerChannelFactoryInterface;
use Drupal\Core\Plugin\ContainerFactoryPluginInterface;
use Drupal\Core\TypedData\TranslatableInterface;
use Drupal\Core\Url;
use Drupal\node\NodeInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;
use \Drupal\Core\Entity\RevisionableStorageInterface;
/**
 * Plugin implementation of the 'entity reference rendered entity' formatter.
 *
 * @FieldFormatter(
 *   id = "entity_reference_revisions_entity_view_ST",
 *   label = @Translation("Rendered entity with parent product"),
 *   description = @Translation("Display the referenced entities with custom loading of parent products rendered by entity_view()."),
 *   field_types = {
 *     "string_long"
 *   }
 * )
 */
class EntityReferenceRevisionsROEntityFormatter extends EntityReferenceRevisionsROFormatterBase implements ContainerFactoryPluginInterface {

	/**
	 * The logger factory.
	 *
	 * @var \Drupal\Core\Logger\LoggerChannelFactoryInterface
	 */
	protected $loggerFactory;

	/**
	 * The entity display repository.
	 *
	 * @var \Drupal\Core\Entity\EntityDisplayRepositoryInterface
	 */
	protected $entityDisplayRepository;



	/**
	 * {@inheritdoc}
	 */
	public static function defaultSettings() {
		return array(
				'view_mode' => 'default',
				'link' => FALSE,
			) + parent::defaultSettings();
	}

	/**
	 * {@inheritdoc}
	 */
	public function viewElements(FieldItemListInterface $items, $langcode) {
		$view_mode = 'default';
		$elements = array();

		foreach ($items as $ii) {
			$entities = $ii->getValue();
      foreach ($this->getEntitiesToView($entities['value_tmp'], $langcode) as $delta => $entity) {
				// Protect ourselves from recursive rendering.
				static $depth = 0;
				$depth++;
				if ($depth > 20) {
					$this->loggerFactory->get('entity')->error('Recursive rendering detected when rendering entity @entity_type @entity_id. Aborting rendering.', array('@entity_type' => $entity->getEntityTypeId(), '@entity_id' => $entity->id()));
					return $elements;
				}

				if (empty($entity))
					continue;

				$vids = \Drupal::entityTypeManager()->getStorage('node')->revisionIds($entity);
				$vids = array_reverse(array_values($vids));

				foreach ($vids as $c => $idd) {
					if ($idd === $entity->getLoadedRevisionId()) {
						if (isset($vids[$c+1])) {
							$oid = $vids[$c+1];
						} else {
							$oid = $idd;
						}
						break;
					}
				}

				$product = $entity->get('field_product');
				$product = $product->first()->get('entity')->getTarget()->getValue();

				$plink = $product->toUrl();
				$plink = Link::fromTextAndUrl($product->label(), $plink);

				if ($entity->getLoadedRevisionId() !== $oid) {
					$prevEntit = \Drupal::entityTypeManager()->getStorage('node')->loadRevision($oid);
					$prevEntity = $prevEntit->toArray();

					$curRev = \Drupal::entityTypeManager()->getStorage('node')->loadRevision($entity->getLoadedRevisionId());

					$fieldValues = [
						'status',
						'field_unmatched',
						'field_brand',
						'field_flavour',
						'field_formulation',
						'field_product',
						'field_expiration',
						'field_test_date'
					];

					$diff = $this->nodeDiff($entity, $prevEntity, $fieldValues);


					$name = $entity->label();
					$link = Url::fromRoute('entity.node.revision', ['node' => $entity->id(), 'node_revision' => $entity->getLoadedRevisionId()]);
					$link = Link::fromTextAndUrl($name, $link);
					$elements['changes'][$delta]['entity'] = $link->toString();
					$elements['changes'][$delta]['product'] = $plink->toString();

					if (!empty($diff['new'])) {
						foreach ($diff['new'] as $ind => $val) {
							$elements['changes'][$delta]['status'][] = [
								'#type' => 'html_tag',
								'#tag' => 'span',
								'#value' => '<br>'. ' Field Name: ' . $ind . ' Old Value: ' . $prevEntit->get($ind)->value . ' New Value: ' . $val
							];
						}

					} else {
						unset($elements['changes'][$delta]);
//						$elements['changes'][$delta][] = [
//							'#type' => 'html_tag',
//							'#tag' => 'span',
//							'#value' => '<br>'. $entity->getLoadedRevisionId() . ', ' . $prevEntit->getLoadedRevisionId() . ', ' . $oid
//						];
					}
				} else {
					//new node
					$url = $entity->toUrl('edit-form');
					$link = Link::fromTextAndUrl($entity->label(), $url);

					$elements['new'][$delta]['entity'] = $link->toString();
					$elements['new'][$delta]['product'] = $plink->toString();

          $brand = !empty($entity->get('field_brand')->first())? $entity->get('field_brand')->first()->get('entity')->getTarget()->getValue() : '';
          $brand = !empty($brand)? $brand->label() : '';
          $form = !empty($entity->get('field_formulation')->first())? $entity->get('field_formulation')->first()->get('entity')->getTarget()->getValue() : '';
          $form = !empty($form)? $form->label() : '';

          $elements['new'][$delta]['status'][] = [
            'brand' => [
              '#type' => 'html_tag',
              '#tag' => 'span',
              '#value' => '<br>Brand: ' . $brand
            ],
            'flavor' => [
              '#type' => 'html_tag',
              '#tag' => 'span',
              '#value' => '<br>Flavour: ' . $entity->get('field_flavour')->value
            ],
            'form' => [
              '#type' => 'html_tag',
              '#tag' => 'span',
              '#value' => '<br>Formulation: ' . $form
            ],
            'td' => [
              '#type' => 'html_tag',
              '#tag' => 'span',
              '#value' => '<br>Test Date: ' . $entity->get('field_test_date')->value
            ],
            'ex' => [
              '#type' => 'html_tag',
              '#tag' => 'span',
              '#value' => '<br>Expiration Date: ' . $entity->get('field_expiration')->value
            ]
          ];
				}


				$depth = 0;
			}
		}

		return $elements;
	}

	public function nodeDiff(NodeInterface $node, $narr, $fields) {
		$node_fields = array_intersect_key(array_map(function($val) { return (isset($val[0]) && isset($val[0]['value'])) ? $val[0]['value'] : ((isset($val[0]) && isset($val[0]['target_id']))? $val[0]['target_id'] : []); }, $node->toArray()), array_flip($fields));
		$original_node_fields = array_intersect_key(array_map(function($val) { return (isset($val[0]) && isset($val[0]['value'])) ? $val[0]['value']: ((isset($val[0]) && isset($val[0]['target_id']))? $val[0]['target_id'] : []); }, $narr), array_flip($fields));
		return ['old' => array_diff($original_node_fields, $node_fields), 'new' => array_diff($node_fields, $original_node_fields)];
	}

	/**
	 * {@inheritdoc}
	 *
	 * @see ::prepareView()
	 * @see ::getEntitiesToView()
	 */
	public function view(FieldItemListInterface $items, $langcode = NULL) {
		$entities = $items->getValue();
		$elements = parent::view($items, $langcode);

		$field_level_access_cacheability = new CacheableMetadata();

		// Try to map the cacheability of the access result that was set at
		// _accessCacheability in getEntitiesToView() to the corresponding render
		// subtree. If no such subtree is found, then merge it with the field-level
		// access cacheability.
		foreach ($entities as $delta => $item) {
			// Ignore items for which access cacheability could not be determined in
			// prepareView().
			if (!empty($item->_accessCacheability)) {
				if (isset($elements[$delta])) {
					CacheableMetadata::createFromRenderArray($elements[$delta])
						->merge($item->_accessCacheability)
						->applyTo($elements[$delta]);
				}
				else {
					$field_level_access_cacheability = $field_level_access_cacheability->merge($item->_accessCacheability);
				}
			}
		}

		// Apply the cacheability metadata for the inaccessible entities and the
		// entities for which the corresponding render subtree could not be found.
		// This causes the field to be rendered (and cached) according to the cache
		// contexts by which the access results vary, to ensure only users with
		// access to this field can view it. It also tags this field with the cache
		// tags on which the access results depend, to ensure users that cannot view
		// this field at the moment will gain access once any of those cache tags
		// are invalidated.
		$field_level_access_cacheability->merge(CacheableMetadata::createFromRenderArray($elements))
			->applyTo($elements);

		return $elements;
	}

	/**
	 * Returns the referenced entities for display.
	 *
	 * The method takes care of:
	 * - checking entity access,
	 * - placing the entities in the language expected for display.
	 * It is thus strongly recommended that formatters use it in their
	 * implementation of viewElements($items) rather than dealing with $items
	 * directly.
	 *
	 * For each entity, the EntityReferenceItem by which the entity is referenced
	 * is available in $entity->_referringItem. This is useful for field types
	 * that store additional values next to the reference itself.
	 *
	 * @param array $items
	 *   The item list.
	 * @param string $langcode
	 *   The language code of the referenced entities to display.
	 *
	 * @return \Drupal\Core\Entity\EntityInterface[]
	 *   The array of referenced entities to display, keyed by delta.
	 *
	 * @see ::prepareView()
	 */
	protected function getEntitiesToView(array $items, $langcode) {
		$entities = [];

		foreach ($items as $delta => $item) {
				$entity = $item;

				// Set the entity in the correct language for display.
				if ($entity instanceof TranslatableInterface) {
					$entity = \Drupal::service('entity.repository')->getTranslationFromContext($entity, $langcode);
				}

				$access = $this->checkAccess($entity);
				// Add the access result's cacheability, ::view() needs it.
				$item->_accessCacheability = CacheableMetadata::createFromObject($access);
				if ($access->isAllowed()) {
					// Add the referring item, in case the formatter needs it.
					$entity->_referringItem = $items[$delta];
					$entities[$delta] = $entity;
				}
		}

		return $entities;
	}

	/**
	 * Checks access to the given entity.
	 *
	 * By default, entity 'view' access is checked. However, a subclass can choose
	 * to exclude certain items from entity access checking by immediately
	 * granting access.
	 *
	 * @param \Drupal\Core\Entity\EntityInterface $entity
	 *   The entity to check.
	 *
	 * @return \Drupal\Core\Access\AccessResult
	 *   A cacheable access result.
	 */
	protected function checkAccess(EntityInterface $entity) {
		return $entity->access('view', NULL, TRUE);
	}

}
