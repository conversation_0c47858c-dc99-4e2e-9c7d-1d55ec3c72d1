<?php

namespace Drupal\sync_processor\Plugin\Field\FieldFormatter;

use Drupal\Core\Entity\RevisionableStorageInterface;
use Drupal\Core\Field\Plugin\Field\FieldFormatter\StringFormatter;
use Drupal\Core\Link;

/**
 * Parent plugin for entity reference formatters.
 */
abstract class EntityReferenceRevisionsROFormatterBase extends StringFormatter {

	/**
	 * {@inheritdoc}
	 */
	public function prepareView(array $entities_items) {
		foreach ($entities_items as $items) {
			foreach ($items as $item) {
				$ids = $item->getString();
				if (!empty($ids)) {
					$ids = explode(',', $ids);
					if (!empty($ids)) {
						$storage = \Drupal::entityTypeManager()->getStorage('node');
						$entities = $storage instanceof RevisionableStorageInterface ? $storage->loadMultipleRevisions($ids) : [];
            $item->value_tmp = array_values($entities);
					}
				}
			}
		}
	}

}
