<?php

namespace Drupal\sync_processor\Plugin\Field\FieldFormatter;

use <PERSON><PERSON>al\Core\Field\FieldItemListInterface;
use <PERSON><PERSON>al\Core\Field\Plugin\Field\FieldFormatter\BasicStringFormatter;
use <PERSON><PERSON>al\Core\Link;
use <PERSON><PERSON><PERSON>\Core\Url;

/**
 * Plugin implementation of the 'basic_string' formatter.
 *
 * @FieldFormatter(
 *   id = "st_messages",
 *   label = @Translation("Sync Task Messages"),
 *   field_types = {
 *     "string_long",
 *     "email"
 *   }
 * )
 */
class STMessagesFormatter extends BasicStringFormatter {

	/**
	 * {@inheritdoc}
	 */
	public function viewElements(FieldItemListInterface $items, $langcode) {
		$elements = [];

		foreach ($items as $delta => $item) {
			$existing = $item->getString();
			$existing = json_decode($existing, true);

			if (!empty($existing)) {
				foreach($existing as $type => $value) {
					$out = $value;

					if (is_array($value)) {
						foreach ($value as $delta => $ind) {
							if (is_array($ind)) {
								if ($type === 'unpublished') {
									$out = [];
									if (isset($ind['text']) && is_array($ind['text'])) {
										$storage = \Drupal::entityTypeManager()->getStorage('node')->loadMultiple(array_values($ind['text']));
										foreach ($storage as $i) {
											$product = $i->get('field_product');
											$product = $product? $product->first()->get('entity')->getTarget() : false;

											if (!empty($product)) {
												$product = $product->getValue();
												$plink = $product->toUrl();
												$plink = Link::fromTextAndUrl($product->label(), $plink);
												$plink = $plink->toString();
											} else {
												$plink = 'No Product Set';
											}

											$name = $i->label();
											$link = $i->toUrl();
											$link = Link::fromTextAndUrl($name, $link);
											$out[] = [
												'product' => $plink,
												'entity' =>	$link->toString()
											];
										}

									} else {
										$out[] = [
											'product' => 'Unknown',
											'entity' =>	$ind['text'] ?? $ind
										];
									}
								} else {
									$out[$delta] = $ind['text'] ?? $ind;
								}
							}
						}
					}
					$elements[$type] = $out;
				}
			}
		}

		return $elements;
	}

}
