<?php

namespace Drupal\sync_processor\Plugin\Field\FieldWidget;

use <PERSON><PERSON>al\Component\Utility\Html;
use <PERSON><PERSON>al\Core\Entity\RevisionableStorageInterface;
use Drupal\Core\Field\FieldItemListInterface;
use <PERSON>upal\Core\Field\Plugin\Field\FieldWidget\EntityReferenceAutocompleteWidget;
use Drupal\Core\Field\Plugin\Field\FieldWidget\StringTextareaWidget;
use Drupal\Core\Form\FormStateInterface;
use Drupal\Core\Link;
use Drupal\entity_reference_revisions\Plugin\Field\FieldWidget\EntityReferenceRevisionsAutocompleteWidget;

/**
 * Plugin implementation of the 'entity_reference_revisions_autocomplete' widget.
 *
 * @FieldWidget(
 *   id = "entity_reference_revisions_readonly",
 *   label = @Translation("Read Only"),
 *   description = @Translation("An read only view of references."),
 *   field_types = {
 *     "string_long"
 *   }
 * )
 */
class EntityReferenceRevisionsReadOnlyWidget extends StringTextareaWidget
{

	/**
	 * {@inheritdoc}
	 */
	public function form(FieldItemListInterface $items, array &$form, FormStateInterface $form_state, $get_delta = NULL)
	{
		$field_name = $this->fieldDefinition->getName();
		$parents = $form['#parents'];

		// Store field information in $form_state.
		if (!static::getWidgetState($parents, $field_name, $form_state)) {
			$field_state = [
				'items_count' => count($items),
				'array_parents' => [],
			];
			static::setWidgetState($parents, $field_name, $form_state, $field_state);
		}

		// Collect widget elements.
		$elements = [];

		// If the widget is handling multiple values (e.g Options), or if we are
		// displaying an individual element, just get a single form element and make
		// it the $delta value.
		if ($this->handlesMultipleValues() || isset($get_delta)) {
			$delta = $get_delta ?? 0;
			$element = [
				'#title' => $this->fieldDefinition->getLabel(),
				'#description' => $this->getFilteredDescription(),
			];
			$element = $this->formSingleElement($items, $delta, $element, $form, $form_state);

			if ($element) {
				if (isset($get_delta)) {
					// If we are processing a specific delta value for a field where the
					// field module handles multiples, set the delta in the result.
					$elements[$delta] = $element;
				} else {
					// For fields that handle their own processing, we cannot make
					// assumptions about how the field is structured, just merge in the
					// returned element.
					$elements = $element;
				}
			}
		}
		// If the widget does not handle multiple values itself, (and we are not
		// displaying an individual element), process the multiple value form.
		else {
			$elements = $this->getMultipleElements($items, $form, $form_state);
		}

		// Populate the 'array_parents' information in $form_state->get('field')
		// after the form is built, so that we catch changes in the form structure
		// performed in alter() hooks.
		$elements['#after_build'][] = [static::class, 'afterBuild'];
		$elements['#field_name'] = $field_name;
		$elements['#field_parents'] = $parents;
		// Enforce the structure of submitted values.
		$elements['#parents'] = array_merge($parents, [$field_name]);
		// Most widgets need their internal structure preserved in submitted values.
		$elements += ['#tree' => TRUE];

		$field_widget_complete_form = [
			// Aid in theming of widgets by rendering a classified container.
			'#type' => 'container',
			// Assign a different parent, to keep the main id for the widget itself.
			'#parents' => array_merge($parents, [$field_name . '_wrapper']),
			'#attributes' => [
				'class' => [
					'field--type-' . Html::getClass($this->fieldDefinition->getType()),
					'field--name-' . Html::getClass($field_name),
					'field--widget-' . Html::getClass($this->getPluginId()),
				],
			],
			'widget' => $elements,
		];

		if (empty($elements))
			$field_widget_complete_form = [];

		// Allow modules to alter the field widget form element.
		$context = [
			'form' => $form,
			'widget' => $this,
			'items' => $items,
			'default' => $this->isDefaultValueWidget($form_state),
		];
		\Drupal::moduleHandler()->alter(['field_widget_complete_form', 'field_widget_complete_' . $this->getPluginId() . '_form'], $field_widget_complete_form, $form_state, $context);

		return $field_widget_complete_form;
	}

	protected function getMultipleElements(FieldItemListInterface $items, array &$form, FormStateInterface $form_state)
	{
		$elements = [
			'header' => [
				'#type' => 'html_tag',
				'#tag' => 'h4',
				'#value' => 'Batch Items Referenced',
			],
			'items' => [
				'#type' => 'container',
				'#attributes' => [
					'style' => [
						'display: block; overflow: scroll; max-height: 300px;',
					],
				],
				'list' => [
					'#type' => 'html_tag',
					'#tag' => 'ul',
					'#attributes' => [
						'style' => [
							'columns: 4; -webkit-columns: 4; -moz-columns: 4;',
						],
					],
				]
			]
		];
		$ids = [];

		foreach ($items as $item) {
			$rid = $item->getString();
			if (!empty($rid)) {
				$e_rid = explode(',', $rid);
				foreach ($e_rid as $iid) {
					$ids[] = $iid;
				}
			}
		}

		if (!empty($ids)) {
			$storage = \Drupal::entityTypeManager()->getStorage('node');
			$entities = $storage instanceof RevisionableStorageInterface ? $storage->loadMultipleRevisions($ids) : [];
			foreach ($entities as $entity) {
				$name = $entity->label();
				$link = $entity->toUrl('edit-form', array('attributes' => array('target' => '_blank')));
				$link = Link::fromTextAndUrl($name, $link);
				$elements['items']['list'][] = [
					'#type' => 'html_tag',
					'#tag' => 'li',
					'#value' => $link->toString(),
				];
			}
		} else
			$elements = [];

		return $elements;
	}
}
