<?php
namespace Drupal\sync_processor\Plugin\QueueWorker;

use <PERSON><PERSON>al\Core\Annotation\QueueWorker;
use <PERSON><PERSON>al\Core\Link;
use <PERSON>upal\Core\Queue\QueueWorkerBase;
use <PERSON><PERSON>al\node\Entity\Node;
use <PERSON><PERSON><PERSON>\sync_processor\syncTaskInterface;

/**
 * A sync task worker that runs on CRON run.
 *
 * @QueueWorker(
 *   id = "sync_cleanup_worker",
 *   title = @Translation("Sync Cleanup Worker"),
 *   cron = {"time" = 5}
 * )
 */
class SyncCleanupWorker extends QueueWorkerBase {
	private syncTaskInterface $task;
	/**
	 * {@inheritdoc}
	 */
	public function processItem($data)
	{
		/** @var syncTaskInterface $task */
		$task = \Drupal::entityTypeManager()->getStorage('synctask')->load($data['id']);

		if (!$task)
			return true;

		$this->task = $task;

		$ran = $this->task->get('field_chunks_ran')->getString();
		$ran++;


		if (isset($data['task']) && !empty($data['task'])) {
			if ($data['task'] === 'unpublish') {
				$items = $data['data'];
				if (is_array($items) && !empty($items)) {
					$this->unpublishInactiveNodes($items);
				}
      } elseif ($data['task'] === 'prealert') {
        $this->task->set('field_processed', true);
        $this->task->set('field_import_status', 'Done');

        $ctime = strtotime('now');
        $this->task->set('field_finish_time', $ctime);
      } elseif ($data['task'] === 'alert') {
        $config = \Drupal::config('sync_processor.sync_settings');
        sync_processor_sendmail($config, $this->task, 'done');
        return true;
      }
		}

		$this->task->set('field_chunks_ran', $ran);
		$this->task->save();
		return true;
	}

	private function unpublishInactiveNodes($activeNodes) {
		if (!empty($activeNodes)) {
			$query = \Drupal::entityQuery('node')
				->condition('type', 'batch')
				->condition('nid', $activeNodes, 'IN')
				->condition('status', 1)
				->accessCheck(FALSE);
			$results = $query->execute();

			if (!empty($results)) {
				$nodes = Node::loadMultiple($results);
				if (!empty($nodes)) {
					$link = $this->task->toUrl('canonical', array('attributes' => array('target' => '_blank')));
					$link = Link::fromTextAndUrl('View Task', $link);
					foreach ($nodes as $node) {
						$node->setUnpublished();
						$node->setNewRevision();
						$node->revision_log = 'Sync Task Unpublished. ' . $link->toString();
						$node->setRevisionUserId(0);
						$node->setRevisionCreationTime(\Drupal::time()->getRequestTime());
						$node->save();
					}
				}
			}
		}
	}
}
