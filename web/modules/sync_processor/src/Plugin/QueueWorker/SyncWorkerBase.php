<?php

/**
 * @file
 * Contains Dr<PERSON>al\sync_processor\Plugin\QueueWorker\SyncWorkerBase.php
 */

namespace Drupal\sync_processor\Plugin\QueueWorker;

use <PERSON><PERSON>al\Core\Entity\EntityInterface;
use <PERSON><PERSON>al\Core\Entity\EntityStorageInterface;
use Dr<PERSON>al\Core\Link;
use Dr<PERSON>al\Core\Plugin\ContainerFactoryPluginInterface;
use Drupal\Core\Queue\QueueFactory;
use Drupal\Core\Queue\QueueWorkerBase;
use Drupal\node\Entity\Node;
use Drupal\node\NodeInterface;
use Drupal\sync_processor\syncTaskInterface;
use Symfony\Component\DependencyInjection\ContainerInterface;


/**
 * Provides base functionality for the NodePublish Queue Workers.
 */
abstract class SyncWorkerBase extends QueueWorkerBase {
	private $items;
	private syncTaskInterface $task;

	public function validateData($item) {
		if (!empty($item)){
			$req_fields = [
				'LABWARE_ID','MANUFACTURER','PRODUCT','FLAVOUR','FORMULATION','BATCH_ID','BATCH_EXPIRY','TEST_DATE'
			];
			foreach ($req_fields as $check) {
				if (!isset($item[$check]) || empty($item[$check]))
					return false;
			}
			return true;
		}
		return false;
	}

	public function getValidationError($item) {
		$ee = [];

		if (!empty($item)){
			$req_fields = [
				'LABWARE_ID','MANUFACTURER','PRODUCT','FLAVOUR','FORMULATION','BATCH_ID','BATCH_EXPIRY','TEST_DATE'
			];
			foreach ($req_fields as $check) {
				if (!isset($item[$check]) || empty($item[$check]))
					$ee[] = $check . ' empty or missing';
			}
		} else {
			$ee = ['Invalid Item Detected'];
		}

		return $ee;
	}

	/**
	 * Get product node
	 *
	 * @return EntityInterface|array|false
	 */
	public function getProduct($id, $alt_id = false) {
    $search_id = $alt_id ?: $id;
		$query = \Drupal::entityQuery('node')
			->condition('type', 'product')
			->condition('field_labware_id', $search_id)
			->accessCheck(FALSE)
			->sort('status', 'DESC');
		$product_match = $query->execute();
		if (!empty($product_match))
			return array_values($product_match);
		elseif (!empty($alt_id))
			return $this->getProduct($id);
		return false;
	}

	/**
	 * Get batch node
	 *
   * @return array|false
   */
  public function getBatch($id, $entry, $exactMatch = false, $strictStatus = false, $dateCheck = false, $loopIds = []) {
		$query = \Drupal::entityQuery('node')
			->condition('type', 'batch')
			->condition('title', $entry['BATCH_ID'])
			->condition('field_flavour', $entry['FLAVOUR'])
			->condition('field_product', $id)
			->sort('status', 'DESC')
			->accessCheck(FALSE);

    if ($exactMatch) {
      $get_brand = \Drupal::entityTypeManager()->getStorage('taxonomy_term')->loadByProperties(['name' => trim(strtolower($entry['MANUFACTURER']), ' \0'), 'vid' => 'brand']);
      $matched_brand = array_shift($get_brand);

      $get_formulation = \Drupal::entityTypeManager()->getStorage('taxonomy_term')->loadByProperties(['name' => trim(strtolower($entry['FORMULATION']), ' \0'), 'vid' => 'formulation']);
      $matched_formulation = array_shift($get_formulation);

      if ($matched_brand && $matched_formulation) {
        $brand_tid = $matched_brand->id();
        $formulation_tid = $matched_formulation->id();
        $query->condition('field_brand', $brand_tid);
        $query->condition('field_formulation', $formulation_tid);
        $query->condition('field_test_date', date('Y-m-d', strtotime($entry['TEST_DATE'])));
        $query->condition('field_expiration', date('Y-m-d', strtotime($entry['BATCH_EXPIRY'])));
      }
    }

    if($strictStatus)
      $query->condition('status', 1);

    if(!empty($loopIds))
      $query->condition('nid', $loopIds, 'IN');

		$group = $query->orConditionGroup()
			->condition('field_test_date', date('Y-m-d', strtotime($entry['TEST_DATE'])), '<=')
			->condition('field_expiration', date('Y-m-d', strtotime($entry['BATCH_EXPIRY'])), '<=');
    if ($dateCheck)
  		$query->condition($group);

		$batch_match = $query->execute();
		if (!empty($batch_match))
      return array_values($batch_match);
		return false;
	}

	public function isNodeChanged(NodeInterface $node, $narr, $fields) {
		$node_fields = array_intersect_key(array_map(function($val) { return (isset($val[0]) && isset($val[0]['value'])) ? $val[0]['value'] : ((isset($val[0]) && isset($val[0]['target_id']))? $val[0]['target_id'] : []); }, $node->toArray()), array_flip($fields));
		$original_node_fields = array_intersect_key(array_map(function($val) { return (isset($val[0]) && isset($val[0]['value'])) ? $val[0]['value'] : ((isset($val[0]) && isset($val[0]['target_id']))? $val[0]['target_id'] : []); }, $narr), array_flip($fields));
		return $node_fields !== $original_node_fields;
	}

	/**
	 * Publishes a node.
	 *
	 * @param syncTaskInterface $task
	 * @return array
	 */
	protected function processItems($task) {
		$ret = [
			'status' => true,
			'count' => 0,
			'error' => [],
			'notice' => []
		];


		if (is_array($this->items) && !empty($this->items)) {
			foreach ($this->items as $entry) {
				$validate = $this->validateData($entry);

				if ($validate) {
					$product = $this->getProduct($entry['LABWARE_ID'], $entry['MULTI_PROD_LINK_ID']);

					if (!empty($product)) {
						if (count($product) > 1) {
							$entry['emsg'] = 'Multiple products found with the same Labware Id';
							$ret['notice'][] = $entry;
						}

						$link = $task->toUrl('canonical', array('attributes' => array('target' => '_blank')));
						$link = Link::fromTextAndUrl('View Task', $link);

						$product = $product[0]; //only use the first product matched
						$existingBatch = $this->getBatch($product, $entry, true);
						if (empty($existingBatch)) {
							$node = Node::create(['type' => 'batch', 'title' => $entry['BATCH_ID']]);
						} else {
              $shouldMerge = false; //this is a temp flag to disable the merge logic since they changed their mind on this, but wanted to leave it here so its easy to change later.
              if ($shouldMerge && count($existingBatch) > 1) {
                $eIds = $task->get('field_batch_ids')->getString();
                $eIds = explode(',', $eIds);

                $bExistC = $this->getBatch($product, $entry, false, false, false, $eIds);
                if (!empty($bExistC)) {
                  $existingBatch = $bExistC[0];
                } else {
                  $bDateC = $this->getBatch($product, $entry, false, true);
                  if (!empty($bDateC)) {
                    $existingBatch = $bDateC[0];
                  } else {
                    $bDateC = $this->getBatch($product, $entry, false, false, true);
                    if (!empty($bDateC)) {
                      $existingBatch = $bDateC[0];
                    } else {
                      $existingBatch = $existingBatch[0];
                    }
                  }
                }
              } else {
                $existingBatch = $existingBatch[0];
              }

							$node = Node::load($existingBatch);
						}

						$changed = false;
						$narr = [];
						$nodeNew = $node->isNew();
						if (!$nodeNew) {
							$narr = $node->toArray();
						}

							$node->set('status', "1");
							$node->set('field_unmatched', "0");
							$this->set_taxonomy_term_reference_field($node, 'brand', 'field_brand', $entry['MANUFACTURER'], $entry);
							$node->set('field_flavour', $entry['FLAVOUR']);
							$this->set_taxonomy_term_reference_field($node, 'formulation', 'field_formulation', $entry['FORMULATION'], $entry);
							$node->set('field_product', $product);
							if (!empty($entry['BATCH_EXPIRY']))
								$this->set_date_field($node, 'field_expiration', $entry['BATCH_EXPIRY']);
							if (!empty($entry['TEST_DATE']))
								$this->set_date_field($node, 'field_test_date', $entry['TEST_DATE']);

							if ($nodeNew)
								$node->save();
							else {
								$fieldValues = [
									'status',
									'field_unmatched',
									'field_brand',
									'field_flavour',
									'field_formulation',
									'field_product',
									'field_expiration',
									'field_test_date'
								];

								$changed = $this->isNodeChanged($node, $narr, $fieldValues);

								if ($changed) {
									$node->setNewRevision();
									$node->revision_log = 'Sync Task Imported update. ' . $link->toString();
									$node->setRevisionUserId(0);
									$node->setRevisionCreationTime(\Drupal::time()->getRequestTime());
									$node->save();
								}
							}

							if ($nodeNew || $changed) {
								$bi = $task->get('field_batch_items')->getString();
								if (empty($bi)) {
									$task->set('field_batch_items', $node->getRevisionId());
								} else {
                  if (is_array($bi))
                    $bi = implode(',', $bi);

									$bi .= ',' . $node->getRevisionId();
									$task->set('field_batch_items', $bi);
								}
							}


						$aids = $task->get('field_batch_ids')->getString();
						if (empty($aids)) {
							$task->set('field_batch_ids', $node->id());
						} else {
              $checkE = explode(',', $aids);
              if (!in_array($node->id(), $checkE)) {
                $aids .= ',' . $node->id();
                $task->set('field_batch_ids', $aids);
              }
						}
						$task->save();

					} else {
						$entry['emsg'] = 'No product found with this labware id';
						$ret['error'][] = $entry;
					}

				} else {
					$entry['emsg'] = $this->getValidationError($entry);
					$ret['error'][] = $entry;
				}
			}
		} else {
			$ret['status'] = false;
			$ret['error'] = ['Invalid data array provided to queue processor'];
		}

		return $ret;
	}

	/**
	 * {@inheritdoc}
	 */
	public function processItem($data) {
		/** @var syncTaskInterface $task */
		$task = \Drupal::entityTypeManager()->getStorage('synctask')->load($data['id']);

		if (!$task)
			return true;

		$this->task = $task;
		$this->items = $data['data'];
		if ($this->task->isActive() && $task instanceof syncTaskInterface) {
			$pi = $this->processItems($this->task);
			if ($pi['status']) {
				$ran = $this->task->get('field_chunks_ran')->getString();
				$total = $this->task->get('field_total_chunks')->getString();
				$ran++;
				$this->task->set('field_chunks_ran', $ran);

				if (!empty($pi['error'])) {
					foreach($pi['error'] as $e) {
						$this->task->logger($e, 'itemerror');
					}
				}

				if (!empty($pi['notice'])) {
					foreach($pi['notice'] as $e) {
						$this->task->logger($e, 'notice');
					}
				}

				$this->task->save();

				//Task completed, run completed items and send emails
				if ($total == $ran) {
					$this->taskComplete();
				}
			} else {
				foreach($pi['error'] as $e) {
					$this->task->logger($e, 'error');
				}
			}
			return true;
		} elseif (!$this->task->isActive()) {
      $connection = \Drupal::database();
      $connection->delete('queue')
        ->condition('name', 'cron_sync_worker')
        ->execute();
      $connection->delete('queue')
        ->condition('name', 'sync_cleanup_worker')
        ->execute();
			$ctime = strtotime('now');
			$this->task->set('field_finish_time', $ctime);
			$this->task->set('field_import_status', 'Cancelled');
			$this->task->logger('Task Cancelled', 'notice');
			$this->task->save();
      $syncSettings = \Drupal::config('sync_processor.sync_settings');
      sync_processor_sendmail($syncSettings, $this->task, 'cancel');
			return true;
		}
		return false;
	}

	/**
	 * try and set the term to specific field
	 *
	 * @param $node Node the current node (batch) being processed
	 * @param $vid string the vocabulary id we are targeting to find the term
	 * @param $field string the current field on the node being processed
	 * @param $value string the appropriate value being set to the field
	 * @param $entry array the current entry item if provided for error logging purposes
	 * @throws \Drupal\Component\Plugin\Exception\InvalidPluginDefinitionException
	 * @throws \Drupal\Component\Plugin\Exception\PluginNotFoundException
	 */
	public function set_taxonomy_term_reference_field(Node &$node, string $vid, string $field, string $value, $entry = []){
		$matched_terms = \Drupal::entityTypeManager()->getStorage('taxonomy_term')->loadByProperties(['name' => trim(strtolower($value)), 'vid' => $vid]);
		$matched_term = array_shift($matched_terms);
		if ($matched_term){
			$node->$field = self::setUniqueTargetIds($node, $field, $matched_term);
		} else {
			if (!empty($entry)) {
				$entry['emsg'] = 'Batch field ' . $field . ' does not match existing product field';
				$this->task->logger($entry, 'itemerror');
			}

			self::set_unmatched($node);
			self::set_unmatched_data($node, $field, $value);
		}
	}

	/**
	 * grabs all data in a field and makes sure there are only unique items tagged to it.
	 *
	 * @param $node object the current node (batch) being processed
	 * @param $field string the current field on the node being processed
	 * @param $matched_entity object the entity that has been matched from a string to add to array of target ids for a multifield entity reference field
	 * @return array collection of arrays containing target ids
	 */
	public static function setUniqueTargetIds($node, $field, $matched_entity){
		$current_values = array_column($node->$field->getValue(), 'target_id');
		$current_values[] = $matched_entity->id();
		$current_values = array_unique($current_values);
		$values = [];
		foreach ($current_values as $value){
			$values[] = ['target_id' => $value];
		}

		return $values;
	}

	/**
	 * @param $node object the current node (batch) being processed
	 * @param $field string the current field on the node being processed
	 * @param $value string that appropriate value being set to the field
	 */
	public static function set_date_field(&$node, $field, $value){
		if (date('Y', strtotime($value)) === '1970'){
			self::set_unmatched($node);
			self::set_unmatched_data($node, $field, $value);
		} else {
      $newDate = date('Y-m-d', strtotime($value));
      if ($node->get($field)->value < $newDate)
        $node->set($field, $newDate);
		}
	}

	/**
	 * set whether a batch item has all fields matching or not to give opportunity to check whats not matched. batches can function with only the product tagged.
	 *
	 * @param $node
	 */
	public static function set_unmatched(&$node){
		$node->set('status', 0);
	}

	/**
	 * this is used to write basically an error log to a batch item
	 *
	 * @param $node object the current node (batch) being processed
	 * @param $field string the current field on the node being processed
	 * @param $value string the string value being added
	 */
	public static function set_unmatched_data(&$node, $field, $value){
		$unmatched_data = $node->field_unmatched_data->value;
		if ($unmatched_data){
			$unmatched_data .= "\n";
		} else {
			$unmatched_data = '';
		}
		$node->set('field_unmatched_data',$unmatched_data . $field . ' | ' . $value . ' | ' . date(DATE_RFC2822));
	}

	private function taskComplete()
	{
		$aids = $this->task->get('field_batch_ids')->getString();
		if (isset($aids)) {
			$an = explode(',', $aids);

			$query = \Drupal::entityQuery('node')
				->condition('type', 'batch')
				->condition('nid', $an, 'NOT IN')
				->condition('status', 1)
				->accessCheck(FALSE);
			$results = $query->execute();

			/** @var QueueFactory $queue_factory */
			$queue_factory = \Drupal::service('queue');
			$queue = $queue_factory->get('sync_cleanup_worker', true);

			$chunks = [];
			if (!empty($results)) {
				$this->task->logger($results,'unpublished');
				$chunks = array_chunk($results, 300);

				foreach ($chunks as $chunk) {
					$item_chunk = [
						'id' => $this->task->id(),
						'task' => 'unpublish',
						'data' => []
					];
					foreach ($chunk as $item) {
						$item_chunk['data'][] = $item;
					}
					$queue->createItem($item_chunk);
				}
			}

      $prealert_chunk = [
        'id' => $this->task->id(),
        'task' => 'prealert',
        'data' => []
      ];
      $queue->createItem($prealert_chunk);

			$alert_chunk = [
				'id' => $this->task->id(),
				'task' => 'alert',
				'data' => []
			];
			$queue->createItem($alert_chunk);

			$chunks_field = $this->task->get('field_total_chunks')->getString();
			$chunks_field_count = (int)$chunks_field + count($chunks) + 1;
			$this->task->set('field_total_chunks', $chunks_field_count);
			$this->task->save();
		} else {
			/** @var QueueFactory $queue_factory */
			$queue_factory = \Drupal::service('queue');
			$queue = $queue_factory->get('sync_cleanup_worker', true);

			$chunks_field = $this->task->get('field_total_chunks')->getString();
			$chunks_field++;
			$this->task->set('field_total_chunks', $chunks_field);
			$this->task->save();

			$alert_chunk = [
				'id' => $this->task->id(),
				'task' => 'alert',
				'data' => []
			];
			$queue->createItem($alert_chunk);
		}
	}

}
