<?php
namespace Drupal\sync_processor;

use Drupal\Core\Entity\EntityInterface;
use Drupal\Core\Queue\QueueFactory;

class SyncTask {
	protected $settings;

	protected $filesystem;

	protected $entity;

	/**
	 * Constructs a ImportManager object.
	 */
	public function __construct($config)
	{
		$this->settings = $config;
		$this->filesystem = \Drupal::service('file_system');;
	}

	public function create(array $entity) {
		$e = Entity\syncTask::create([
			'status' => 1
		]);

		foreach ($entity as $key => $field) {
			$e->set($key, $field);
		}

		try {
			$e->save();
			return true;
		}
		catch (\Exception $e) {
			$logger = \Drupal::logger('sync_processor');
			$logger->error($e->getMessage());
			return false;
		}
	}

	public function fail($id) {
		$entity = \Drupal::entityTypeManager()->getStorage('synctask')->load($id);
		if ($entity instanceof syncTaskInterface) {
			try {
				$entity->set('status', false);
				$entity->save();
				return true;
			}
			catch (\Exception $e) {
				$logger = \Drupal::logger('sync_processor');
				$logger->error($e->getMessage());
				return false;
			}
		}
		return false;
	}

	public function load($id) {
		$entity = \Drupal::entityTypeManager()->getStorage('synctask')->load($id);
		if ($entity instanceof syncTaskInterface) {
			try {
				$this->entity = $entity;
				return true;
			}
			catch (\Exception $e) {
				$logger = \Drupal::logger('sync_processor');
				$logger->error($e->getMessage());
				return false;
			}
		}
		return false;
	}

	public function queue() {
		$fp = new FileProcessor($this->settings);

		//Must load entity first
		if (empty($this->entity))
			return false;

		$loaded = $fp->load($this->entity);

		if (!empty($loaded)) {
			/** @var QueueFactory $queue_factory */
			$queue_factory = \Drupal::service('queue');
			$queue = $queue_factory->get('cron_sync_worker', true);
			$chunks = array_chunk($loaded, 100);

			foreach ($chunks as $chunk) {
				$item_chunk = [
					'id' => $this->entity->id(),
					'data' => []
				];
				foreach ($chunk as $item) {
					$item_chunk['data'][] = $item;
				}
				$queue->createItem($item_chunk);
			}

			$this->entity->set('field_total_chunks', count($chunks));
			$this->entity->set('field_chunks_ran', 0);
			$this->entity->set('field_finish_time', 0);
			$this->entity->save();

			sync_processor_sendmail($this->settings, $this->entity, 'new');

			return true;
		}

		return false;
	}

	public function update() {

	}

	public function retry() {
		//trigger task to rerun
	}
}
