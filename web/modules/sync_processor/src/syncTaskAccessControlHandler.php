<?php declare(strict_types = 1);

namespace Drupal\sync_processor;

use <PERSON>upal\Core\Access\AccessResult;
use <PERSON>upal\Core\Entity\EntityAccessControlHandler;
use Drupal\Core\Entity\EntityInterface;
use Drupal\Core\Session\AccountInterface;

/**
 * Defines the access control handler for the synctask entity type.
 *
 * phpcs:disable Drupal.Arrays.Array.LongLineDeclaration
 *
 * @see https://www.drupal.org/project/coder/issues/3185082
 */
final class syncTaskAccessControlHandler extends EntityAccessControlHandler {

  /**
   * {@inheritdoc}
   */
  protected function checkAccess(EntityInterface $entity, $operation, AccountInterface $account): AccessResult {
    return match($operation) {
      'view' => AccessResult::allowedIfHasPermissions($account, ['view sync task', 'administer sync task'], 'OR'),
      'update' => AccessResult::allowedIfHasPermissions($account, ['edit sync task', 'administer sync task'], 'OR'),
      'delete' => AccessResult::allowedIfHasPermissions($account, ['delete sync task', 'administer sync task'], 'OR'),
      default => AccessResult::neutral(),
    };
  }

  /**
   * {@inheritdoc}
   */
  protected function checkCreateAccess(AccountInterface $account, array $context, $entity_bundle = NULL): AccessResult {
    return AccessResult::allowedIfHasPermissions($account, ['create sync task', 'administer sync task'], 'OR');
  }

}
