<?php declare(strict_types = 1);

namespace Drupal\sync_processor;

use <PERSON>upal\Core\Entity\ContentEntityInterface;
use <PERSON>upal\Core\Entity\EntityChangedInterface;
use <PERSON><PERSON>al\user\EntityOwnerInterface;

/**
 * Provides an interface defining an synctask entity type.
 */
interface syncTaskInterface extends ContentEntityInterface, EntityOwnerInterface, EntityChangedInterface {
	public function isActive();

	/**
	 * Set error with task
	 *
	 * @param array|string $item
	 * @param string $type mail|error|notice|itemerror|unpublished|custom
	 * @param array $custom custom array placement in messages block on task
	 *
	 **/
	public function logger(array|string $item, string $type, array $custom = []);

	/**
	 * Get log item from task
	 *
	 * @param string $type mail|error|notice|itemerror|unpublished|custom
	 * @param array $custom custom array placement in messages block on task
	 *
	 * @return array|false
	 **/
	public function getLogItem(string $type, array $custom = []);

	public function logEmail($text, $type);

	public function logError($item);
}
