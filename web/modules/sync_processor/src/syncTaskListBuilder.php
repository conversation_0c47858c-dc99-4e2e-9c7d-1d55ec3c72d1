<?php declare(strict_types = 1);

namespace Drupal\sync_processor;

use Drupal\Core\Entity\EntityInterface;
use Drupal\Core\Entity\EntityListBuilder;

/**
 * Provides a list controller for the synctask entity type.
 */
final class syncTaskListBuilder extends EntityListBuilder {
  /**
   * {@inheritdoc}
   */
  public function buildHeader(): array {
	$header['view'] = '';
	$header['status'] = $this->t('Processing Status');
	$header['file'] = $this->t('File');
    $header['enabled'] = $this->t('Enabled');
    $header['processed'] = $this->t('Processed');
    $header['schedule'] = $this->t('Scheduled Run Time');
    $header['attention'] = $this->t('Needs Attention');
	$header['progress'] = $this->t('Progress');
    return $header + parent::buildHeader();
  }

  /**
   * {@inheritdoc}
   */
  public function buildRow(EntityInterface $entity): array {
    /** @var \Drupal\sync_processor\syncTaskInterface $entity */
	$row['view']['data'] = $entity->toLink('View Report');
	$row['status']['data'] = $entity->get('field_import_status')->view(['label' => 'hidden']);
	$row['file']['data'] = $entity->get('field_batch_file')->view(['label' => 'hidden']);
    $row['enabled'] = $entity->get('status')->value ? $this->t('Yes') : $this->t('No');
    $row['processed']['data'] = $entity->get('field_processed')->value ? $this->t('Yes') : $this->t('No');
    $row['schedule']['data'] = $entity->get('field_scheduled_run_time')->view(['label' => 'hidden']);

	$messages = $entity->get('field_messages')->getString();
	$messages = json_decode($messages, true);

    $row['attention']['data'] = (
		(isset($messages['error']) && !empty($messages['error']))
	|| (isset($messages['itemerror']) && !empty($messages['itemerror']))
	) ? 'Yes' : 'No';

	$done = $entity->get('field_processed')->value;
	if ($done)
		$row['progress'] = '100%';
  else if ($entity->get('field_import_status')->getString() == 'Cancelled') {
    $row['progress'] = 'Cancelled';
  } else {
		$chunks = $entity->get('field_total_chunks')->getString();
		if (empty($chunks))
			$row['progress'] = 'Not Started';
		else {
			$p_chunks = $entity->get('field_chunks_ran')->getString();
			$row['progress'] = "{$p_chunks}/{$chunks} imported";
		}
	}

    return $row + parent::buildRow($entity);
  }

}
