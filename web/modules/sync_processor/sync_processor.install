<?php
/**
 * @file
 * Schema definitions install/update/uninstall hooks.
 */

/**
 * Rebuild cache.
 */
function sync_processor_update_8001() {
	// Empty update to cause a cache rebuild.
}

/**
 * Sets the default config values.
 */
function sync_processor_update_8002() {
	$config = \Drupal::configFactory()->getEditable('sync_processor.sync_settings');
	$config->set('notification_email', '');
	$config->set('frequency_int', 12);
	$config->set('frequency_type', 'h');
	$config->set('history', 10);
	$config->set('domain', false);
	$config->set('apikey', '');
	$config->set('alias', null);
	$config->save();

	$config = \Drupal::configFactory()->getEditable('sync_processor.status');
	$config->set('last', false);
	$config->set('last_count', 0);
	$config->set('last_fails', 0);
	$config->set('last_message', '');
	$config->set('next', 0);
	$config->save();
}

/**
 * Disable cache on synctask entity type
 */
function sync_processor_update_8003() {
	$updateManager = \Drupal::entityDefinitionUpdateManager();
	$entityType = $updateManager->getEntityType('synctask');

	if (!$entityType) {
		return;
	}

	$entityType->set('persistent_cache', FALSE);
	$entityType->set('render_cache', FALSE);
	$updateManager->updateEntityType($entityType);
}
