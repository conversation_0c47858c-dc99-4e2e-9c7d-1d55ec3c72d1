entity.synctask.view:
  title: 'View Task Report'
  route_name: entity.synctask.canonical
  base_route: sync_processor.overview
entity.synctask.edit_form:
  title: 'Edit'
  route_name: entity.synctask.edit_form
  base_route: entity.synctask.canonical
entity.synctask.delete_form:
  title: 'Delete'
  route_name: entity.synctask.delete_form
  base_route: entity.synctask.canonical
  weight: 10

sync_processor.admin_content:
  title: 'Overview'
  route_name: sync_processor.overview
  base_route: sync_processor.overview
  weight: 10
sync_processor.tasks:
  title: 'Tasks'
  route_name: entity.synctask.collection
  base_route: sync_processor.overview
  weight: 11
sync_processor.settings:
  title: 'Settings'
  route_name: sync_processor.settings
  base_route: sync_processor.overview
  weight: 12
