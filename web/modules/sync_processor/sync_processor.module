<?php declare(strict_types = 1);

/**
 * @file
 * Provides an sync task entity type.
 */

use Drupal\Core\Render\Element;
use <PERSON><PERSON>al\user\UserInterface;
use Drupal\Core\Entity\EntityInterface;
use Drupal\Core\Queue\QueueFactory;
use Drupal\Core\Queue\QueueInterface;

/**
 * Implements hook_theme().
 */
function sync_processor_theme(): array {
  return [
    'synctask' => ['render element' => 'elements'],
	  'synctask_edit_form' => [
		  'render element' => 'form',
	  ],
	  'synctask_add_form' => [
		  'render element' => 'form',
	  ],
	  'sync_status' => [
		  'variables' => [
			  'grouped_requirements' => NULL,
			  'requirements' => NULL,
		  ],
	  ],
	  'sync_status_page' => [
		  'variables' => [
			  'counters' => [],
			  'general_info' => [],
			  'requirements' => NULL,
		  ],
	  ],
	  'sync_status_highlight' => [
		  'variables' => ['amount' => NULL, 'text' => NULL, 'severity' => NULL],
	  ],
	  'sync_status_info' => [
		  'variables' => [
			  'selfver' => [],
			  'cron' => [],
			  'database_system' => [],
			  'database_system_version' => [],
			  'php' => [],
			  'php_memory_limit' => [],
			  'webserver' => [],
		  ],
	  ],
	  'sync_status_grouped' => [
		  'variables' => [
			  'grouped_requirements' => NULL,
			  'requirements' => NULL,
		  ],
	  ],
  ];
}

/**
 * Prepares variables for synctask templates.
 *
 * Default template: synctask.html.twig.
 *
 * @param array $variables
 *   An associative array containing:
 *   - elements: An associative array containing the synctask information and any
 *     fields attached to the entity.
 *   - attributes: HTML attributes for the containing element.
 */
function template_preprocess_synctask(array &$variables): void {
  $variables['view_mode'] = $variables['elements']['#view_mode'];
  foreach (Element::children($variables['elements']) as $key) {
    $variables['content'][$key] = $variables['elements'][$key];
  }
}

/**
 * Implements hook_user_cancel().
 */
function sync_processor_user_cancel($edit, UserInterface $account, $method): void {
  switch ($method) {
    case 'user_cancel_block_unpublish':
      // Unpublish sync tasks.
      $storage = \Drupal::entityTypeManager()->getStorage('synctask');
      $synctask_ids = $storage->getQuery()
        ->condition('uid', $account->id())
        ->condition('status', 1)
        ->accessCheck(FALSE)
        ->execute();
      foreach ($storage->loadMultiple($synctask_ids) as $synctask) {
        $synctask->set('status', FALSE)->save();
      }
      break;

    case 'user_cancel_reassign':
      // Anonymize sync tasks.
      $storage = \Drupal::entityTypeManager()->getStorage('synctask');
      $synctask_ids = $storage->getQuery()
        ->condition('uid', $account->id())
        ->accessCheck(FALSE)
        ->execute();
      foreach ($storage->loadMultiple($synctask_ids) as $synctask) {
        $synctask->setOwnerId(0)->save();
      }
      break;
  }
}

/**
 * Implements hook_ENTITY_TYPE_predelete() for user entities.
 */
function sync_processor_user_predelete(UserInterface $account): void {
  // Delete sync tasks that belong to this account.
  $storage = \Drupal::entityTypeManager()->getStorage('synctask');
  $synctask_ids = $storage->getQuery()
    ->condition('uid', $account->id())
    ->accessCheck(FALSE)
    ->execute();
  $storage->delete(
    $storage->loadMultiple($synctask_ids)
  );
  // Delete old revisions.
  $synctask_ids = $storage->getQuery()
    ->allRevisions()
    ->condition('uid', $account->id())
    ->accessCheck(FALSE)
    ->execute();
  foreach (array_keys($synctask_ids) as $revision_id) {
    $storage->deleteRevision($revision_id);
  }
}

/**
 * Implements hook_theme_suggestions_HOOK().
 */
function sync_processor_theme_suggestions_synctask(array $variables) {
	$suggestions = [];
	$synctask = $variables['elements']['#synctask'];
	$sanitized_view_mode = strtr($variables['elements']['#view_mode'], '.', '_');

	$suggestions[] = 'synctask__' . $sanitized_view_mode;

	return $suggestions;
}

/**
 * Implements hook_cron().
 */
function sync_processor_cron() {
	$config = \Drupal::config('sync_processor.status');
	$batchImporter = \Drupal::service('sync_processor.batch_importer');
	$syncSettings = \Drupal::config('sync_processor.sync_settings');

	//Create queue from tasks that are ready to be ran
	try {
		$batchImporter->start();
	}
	catch (Exception $e) {
		$logger = \Drupal::logger('sync_processor');
		$logger->error($e->getMessage());
	}

	try {
		$history = $syncSettings->get('history')?? 10;

		$query = \Drupal::entityQuery('synctask')
			->sort('id', 'ASC')
			->accessCheck(FALSE);
		$group = $query->orConditionGroup()
			->condition('field_import_status', 'Cancelled')
			->condition('field_import_status', 'Done');
		$query->condition($group);
		$res_count = $query->count()->execute();

		if ($res_count > $history) {
			$del_count = $res_count - $history;
			$query = \Drupal::entityQuery('synctask')
				->sort('id', 'ASC')
				->accessCheck(FALSE)
				->range(0, $del_count);
			$group = $query->orConditionGroup()
				->condition('field_import_status', 'Cancelled')
				->condition('field_import_status', 'Done');
			$query->condition($group);
			$idsToDel = $query->execute();

			$storage_handler = \Drupal::entityTypeManager()->getStorage('synctask');
			$entities = $storage_handler->loadMultiple($idsToDel);
			$storage_handler->delete($entities);
			$logger = \Drupal::logger('sync_processor');
			$logger->info('Removed ' . $del_count . ' old sync task reports');
		}
	}
	catch (Exception $e) {
		$logger = \Drupal::logger('sync_processor');
		$logger->error($e->getMessage());
	}

	//Import new files
	try {
		$ctime = strtotime('now');

		if ($ctime >= $config->get('next')) {
			$batchImporter->fetchNew();
			$ec = \Drupal::configFactory()->getEditable('sync_processor.status');
			$ec->set('next', strtotime('+10 minutes'));
			$ec->save();
		}
	}
	catch (Exception $e) {
		$logger = \Drupal::logger('sync_processor');
		$logger->error($e->getMessage());
	}
}

/**
 * Implements hook_preprocess_entity().
 */
function sync_processor_preprocess_synctask(&$variables) {
	$cr = $variables["elements"]["#synctask"]->get('field_chunks_ran')->first()->value;
	$tc = $variables["elements"]["#synctask"]->get('field_total_chunks')->first();

  if (!empty($variables["elements"]["#synctask"]->get('field_total_chunks')->first()))
    $tc = $tc->value;
  else
    $tc = 0;

	if (empty($cr))
		$per = 0;
	else
		$per = floor(($cr/$tc)*100);

	$variables['content']['percent_done'] = ($per > 100)? 100: $per;
	$variables['content']['raw_status'] = $variables["elements"]["#synctask"]->get('field_import_status')->first()->value;
}

/**
 * Implements hook_mail().
 */
function sync_processor_mail($key, &$message, $params) {
 switch ($key) {
	 case 'synctask':
		 $message['from'] = \Drupal::config('system.site')->get('mail');
		 $message['subject'] = $params['subject'];
		 $message['body'][] = $params['message'];
		 $message['headers']['Content-Type'] = 'text/html; charset=UTF-8';
		 break;
 }
}

function sync_processor_sendmail($config, $entity, $type) {
	if ($type === 'new') {
		$templatekey = 'task_start';
		$sub = 'New Sync Task Started';
		$items = 0;
		$messages = [];
		$date = 0;
	}
  if ($type === 'cancel') {
    $templatekey = 'task_cancel';
    $sub = 'A Sync Task Has Been Cancelled';
    $items = 0;
    $messages = [];
    $date = 0;
  }
	if ($type === 'done') {
		$templatekey = 'task_done';
		$sub = 'A Sync Task Has Completed';
    \Drupal::entityTypeManager()->getViewBuilder('synctask')->resetCache();

    $items = $entity->get('field_batch_items')
      ->view([
        'type' => 'entity_reference_revisions_entity_view_ST',
        'label' => 'hidden'
      ]);
    $messages = $entity->get('field_messages')->view([
      'type' => 'st_messages',
      'label' => 'hidden'
    ]);
    $dt = \Drupal::service('date.formatter');
    $date = $dt->format($entity->get('field_finish_time')
      ->first()
      ->getString(), 'custom', 'F jS');
  }

	// Load the Twig theme engine so we can use twig_render_template().
	include_once \Drupal::root() . '/core/themes/engines/twig/twig.engine';
	$sa = $config->get('alias');
	$cd = $config->get('email_domain');

	switch ($sa) {
		case "ii":
			$logo = 'https://choice.wetestyoutrust.com/sites/default/files/2022-01/INFORMEDSmallMonoWhiteLogos_II%20%281%29.png';
			$sitename = 'Informed Ingredient';
			break;
		case "ic":
			$logo = 'https://choice.wetestyoutrust.com/sites/default/files/2022-01/INFORMEDSmallMonoWhiteLogos_IC%20%281%29.png';
			$sitename = 'Informed Choice';
			break;
		case "is":
			$logo = 'https://choice.wetestyoutrust.com/sites/default/files/2022-01/INFORMEDSmallMonoWhiteLogos_IS%20%281%29.png';
			$sitename = 'Informed Sport';
			break;
		case "ip":
			$logo = 'https://choice.wetestyoutrust.com/sites/default/files/2022-01/INFORMEDSmallMonoWhiteLogos_IP%20%281%29.png';
			$sitename = 'Informed Protein';
			break;
	}

  $markup = twig_render_template(\Drupal::service('extension.list.module')
      ->getPath('sync_processor') . '/emails/' . $templatekey . '.html.twig', [
    'items' => $items,
    'messages' => $messages,
    'date' => $date,
    'link' => $cd . $entity->toUrl()->toString(),
    'domain' => $cd,
    'logo' => $logo,
    'sitename' => $sitename,
    // Needed to prevent notices when Twig debugging is enabled.
    'theme_hook_original' => 'not-applicable',
    'cache' => FALSE
  ]);
	// Cast to string since twig_render_template returns a Markup object.
	$body = (string) $markup;
	$mailManager = \Drupal::service('plugin.manager.mail');
	$module = 'sync_processor';
	$key = 'synctask';
	$to = $config->get('notification_email');

	$params['subject'] = $sitename . ': '. $sub;
	$params['message'] = $body;
	$langcode = \Drupal::currentUser()->getPreferredLangcode();
	$send = true;
  if (!empty($to)) {
    $result = $mailManager->mail($module, $key, $to, $langcode, $params, NULL, $send);
  }
}
