{#
/**
 * @file
 * Theme override for status report counter.
 *
 * Available variables:
 * - amount: The number shown on counter.
 * - text: The text shown on counter.
 * - severity: The severity of the counter.
 *
 * @ingroup themeable
 */
#}
{%
    set classes = [
    'system-status-counter',
    'system-status-counter--' ~ severity,
]
%}
<span{{ attributes.addClass(classes) }}>
  <span class="system-status-counter__status-icon system-status-counter__status-icon--{{ severity }}"></span>
  <span class="system-status-counter__status-title">
    <span class="system-status-counter__title-count">{{ amount }} {{ text }}</span>
    <span class="system-status-counter__details"><a href="#{{ severity }}" ><span class="visually-hidden">{{ text }} </span>Details</a></span>
  </span>
</span>
