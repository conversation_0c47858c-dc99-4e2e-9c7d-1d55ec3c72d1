{#
/**
 * @file
 * Theme override for status report general info.
 *
 * Available variables:
 * - drupal: The status of Drupal installation:
 *   - value: The current status of Drupal installation.
 *   - description: The description for current status of Drupal installation.
 * - cron: The status of cron:
 *   - value: The current status of cron.
 *   - description: The description for current status of cron.
 *   - cron.run_cron: An array to render a button for running cron.
 * - database_system: The status of database system:
 *   - value: The current status of database system.
 *   - description: The description for current status of cron.
 * - database_system_version: The info about current database version:
 *   - value: The current version of database.
 *   - description: The description for current version of database.
 * - php: The current version of PHP:
 *   - value: The status of currently installed PHP version.
 *   - description: The description for current installed PHP version.
 * - php_memory_limit: The info about current PHP memory limit:
 *   - value: The status of currently set PHP memory limit.
 *   - description: The description for currently set PHP memory limit.
 * - webserver: The info about currently installed web server:
 *   - value: The status of currently installed web server.
 *   - description: The description for the status of currently installed web
 *     server.
 */
#}
<div class="system-status-general-info">
    <h2 class="system-status-general-info__header">{{ 'Sync Status Overview'|t }}</h2>
    <div class="system-status-general-info__items">
        <div class="system-status-general-info__item">
            <span class="system-status-general-info__item-icon system-status-general-info__item-icon--drupal"></span>
            <div class="system-status-general-info__item-details">
                <h3 class="system-status-general-info__item-title">{{ 'Sync Processor Version'|t }}</h3>
                {{ selfver.value }}
            </div>
        </div>
        <div class="system-status-general-info__item">
            <span class="system-status-general-info__item-icon system-status-general-info__item-icon--clock"></span>
            <div class="system-status-general-info__item-details">
                <h3 class="system-status-general-info__item-title">{{ 'Last Cron Run'|t }}</h3>
                {{ cron.value }}
                {% if cron.run_cron %}
                    <div class="system-status-general-info__run-cron">{{ cron.run_cron }}</div>
                {% endif %}
                {% if cron.description %}
                    <div class="system-status-general-info__description">{{ cron.description }}</div>
                {% endif %}
            </div>
        </div>
{#        <div class="system-status-general-info__item">#}
{#            <span class="system-status-general-info__item-icon system-status-general-info__item-icon--server"></span>#}
{#            <div class="system-status-general-info__item-details">#}
{#                <h3 class="system-status-general-info__item-title">{{ 'Web Server'|t }}</h3>#}
{#                {{ webserver.value }}#}
{#                {% if webserver.description %}#}
{#                    <div class="description">{{ webserver.description }}</div>#}
{#                {% endif %}#}
{#            </div>#}
{#        </div>#}
{#        <div class="system-status-general-info__item">#}
{#            <span class="system-status-general-info__item-icon system-status-general-info__item-icon--php"></span>#}
{#            <div class="system-status-general-info__item-details">#}
{#                <h3 class="system-status-general-info__item-title">{{ 'PHP'|t }}</h3>#}
{#                <h4 class="system-status-general-info__sub-item-title">{{ 'Version'|t }}</h4>{{ php.value }}#}
{#                {% if php.description %}#}
{#                    <div class="description">{{ php.description }}</div>#}
{#                {% endif %}#}

{#                <h4 class="system-status-general-info__sub-item-title">{{ 'Memory limit'|t }}</h4>{{ php_memory_limit.value }}#}
{#                {% if php_memory_limit.description %}#}
{#                    <div class="description">{{ php_memory_limit.description }}</div>#}
{#                {% endif %}#}
{#            </div>#}
{#        </div>#}
{#        <div class="system-status-general-info__item">#}
{#            <span class="system-status-general-info__item-icon system-status-general-info__item-icon--database"></span>#}
{#            <div class="system-status-general-info__item-details">#}
{#                <h3 class="system-status-general-info__item-title">{{ 'Database'|t }}</h3>#}
{#                <h4 class="system-status-general-info__sub-item-title">{{ 'Version'|t }}</h4>{{ database_system_version.value }}#}
{#                {% if database_system_version.description %}#}
{#                    <div class="description">{{ database_system_version.description }}</div>#}
{#                {% endif %}#}

{#                <h4 class="system-status-general-info__sub-item-title">{{ 'System'|t }}</h4>{{ database_system.value }}#}
{#                {% if database_system.description %}#}
{#                    <div class="description">{{ database_system.description }}</div>#}
{#                {% endif %}#}
{#            </div>#}
{#        </div>#}
    </div>
</div>
