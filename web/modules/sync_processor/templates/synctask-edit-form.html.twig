{#
/**
 * @file
 * Theme override for a synctask edit form.
 *
 * Two column template for the synctask add/edit form.
 *
 * This template will be used when a synctask edit form specifies 'synctask_edit_form'
 * as its #theme callback.  Otherwise, by default, synctask add/edit forms will be
 * themed by form.html.twig.
 *
 * Available variables:
 * - form: The synctask add/edit form.
 *
 * @see seven_form_node_form_alter()
 */
#}
<div class="layout-node-form clearfix">
    <div class="layout-region layout-region-node-main">
        {{ form|without('advanced', 'footer', 'actions') }}
    </div>
    <div class="layout-region layout-region-node-secondary">
        {{ form.advanced }}
    </div>
    <div class="layout-region layout-region-node-footer">
        <div class="layout-region-node-footer__content">
            {{ form.footer }}
            {{ form.actions }}
        </div>
    </div>
</div>
