{#
/**
 * @file
 * Default theme implementation to present an synctask entity.
 *
 * This template is used when viewing a canonical synctask page,
 *
 * Available variables:
 * - content: A list of content items. Use 'content' to print all content, or
 *   print a subset such as 'content.label'.
 * - attributes: HTML attributes for the container element.
 *
 * @see template_preprocess_synctask()
 */
#}
{#{{ dump(content.field_messages) }}#}
<div id="page-wrapper">
  <div class="grid-container medium-vertical-padding full bg-color-primary">
    <div class="grid-x">
      <div class="cell">
        <h1 class="h3 color-white text-center">Sync Task Report</h1>
      </div>
    </div>
  </div>
  <div class="grid-container xlarge-vertical-margin">
    <div class="grid-x">
      <div class="cell large-12 card">
        <div class="progress" role="progressbar" aria-valuenow="{{ content.percent_done }}" aria-valuemin="0" aria-valuetext="{{ content.percent_done}} percent" aria-valuemax="100">
          <span class="progress-meter" style="width: {{ content.percent_done}}%">
            <span class="progress-meter-text">{{ content.percent_done}}%</span>
          </span>
        </div>
      </div>
      <div class="cell large-6 card">
        Status - {{ content.field_import_status.0 }}
      </div>
      <div class="cell large-6 card">
        Processed - {{ content.field_processed.0 }}
      </div>
      <div class="cell large-12 card" style="display: inline;">
        File - {{ content.field_batch_file.0 }}
      </div>
      <div class="cell large-6 card" style="display: inline;">
        Scheduled Start Time - {{ content.field_scheduled_run_time.0 }} UTC
      </div>
      <div class="cell large-6 card" style="display: inline;">
        Finish Time -
        {% if content.raw_status == 'Done' %}
          {{ content.field_finish_time.0 }} UTC
        {% elseif content.raw_status == 'Cancelled' %}
          Cancelled
        {% else %}
          TBD
        {% endif %}
      </div>
      {% if content.field_messages.notice|length > 0 %}
        <div class="grid-x medium-vertical-padding sync-full">
          <div class="cell small-vertical-padding">
            <h2 class="h2">Notices</h2>
          </div>
          <div class="cell" style="max-height: 300px;overflow: scroll;">
            <table class="hover">
              <thead>
              <tr>
                <th style="width:60%">Message</th>
                <th style="width:40%">Details</th>
              </tr>
              </thead>
              <tbody>
              {% for item in content.field_messages.notice %}
                <tr>
                  {% if item.emsg|length > 0 %}
                    <th>{{ item.emsg }}</th>
                    <th>
                      {% for key, ii in item %}
                        {% if key != 'emsg' %}
                          {{ key }}: {{ ii }} <br>
                        {% endif %}
                      {% endfor %}
                    </th>
                  {% else %}
                    <th>{{ item }}</th>
                    <th></th>
                  {% endif %}
                </tr>
              {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
      {% endif %}
      {% if content.field_messages.error|length > 0 %}
        <div class="grid-x medium-vertical-padding sync-full">
          <div class="cell small-vertical-padding">
            <h2 class="h2">Errors</h2>
          </div>
          <div class="cell" style="max-height: 300px;overflow: scroll;">
            <table class="hover">
              <thead>
              <tr>
                <th style="width:60%">Message</th>
                <th style="width:40%">Details</th>
              </tr>
              </thead>
              <tbody>
              {% for item in content.field_messages.error %}
                <tr>
                  <th>{{ item.emsg }}</th>
                  <th>
                    {% for key, ii in item %}
                      {% if key != 'emsg' %}
                        {{ key }}: {{ ii }} <br>
                      {% endif %}
                    {% endfor %}
                  </th>
                </tr>
              {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
      {% endif %}
      {% if content.field_messages.itemerror|length > 0 %}
        <div class="grid-x medium-vertical-padding sync-full">
          <div class="cell small-vertical-padding">
            <h2 class="h2">Import Item Errors</h2>
          </div>
          <div class="cell" style="max-height: 300px;overflow: scroll;">
            <table class="hover">
              <thead>
              <tr>
                <th style="width:60%">Message</th>
                <th style="width:40%">Details</th>
              </tr>
              </thead>
              <tbody>
              {% for item in content.field_messages.itemerror %}
                <tr>
                  <th>{{ item.emsg }}</th>
                  <th>
                    {% for key, ii in item %}
                      {% if key != 'emsg' %}
                        {{ key }}: {{ ii }} <br>
                      {% endif %}
                    {% endfor %}
                  </th>
                </tr>
              {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
      {% endif %}
    </div>
    <div class="grid-x align-center-middle text-center medium-vertical-padding">
      <div class="cell medium-4 large-3 card">
        <a href="#new">New Batches</a>
        <span>{{ content.field_batch_items.new|length }}</span>
      </div>
      <div class="cell medium-4 large-3 card">
        <a href="#updated">Updated Batches</a>
        <span>{{ content.field_batch_items.changes|length }}</span>
      </div>
      <div class="cell medium-4 large-3 card">
        <a href="#unpublished">Unpublished Batches</a>
        <span>{{ content.field_messages.unpublished|length }}</span>
      </div>
    </div>
    {% if content.field_batch_items.new|length > 0 %}
      <div id="new" class="grid-x medium-vertical-padding">
        <div class="cell small-vertical-padding">
          <h2 class="h2">New Batches</h2>
        </div>
        <div class="cell" style="max-height: 600px;overflow: scroll;">
          <table class="hover">
            <thead>
            <tr>
              <th width="300">Product</th>
              <th width="300">Batch Item</th>
              <th>Details</th>
            </tr>
            </thead>
            <tbody>
            {% for item in content.field_batch_items.new %}
              <tr>
                <th>{{ item.product }}</th>
                <th>{{ item.entity }}</th>
                <th>{{ item.status }}</th>
              </tr>
            {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    {% endif %}
    {% if content.field_batch_items.changes|length > 0 %}
      <div id="updated" class="grid-x medium-vertical-padding">
        <div class="cell small-vertical-padding">
          <h2 class="h2">Updated Batches</h2>
        </div>
        <div class="cell" style="max-height: 600px;overflow: scroll;">
          <table class="hover">
            <thead>
            <tr>
              <th width="300">Product</th>
              <th width="300">Batch Item</th>
              <th>Changes</th>
            </tr>
            </thead>
            <tbody>
            {% for item in content.field_batch_items.changes %}
              <tr>
                <th>{{ item.product }}</th>
                <th>{{ item.entity }}</th>
                <th>{{ item.status }}</th>
              </tr>
            {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    {% endif %}
    {% if content.field_messages.unpublished|length > 0 %}
      <div id="unpublished" class="grid-x medium-vertical-padding">
        <div class="cell small-vertical-padding">
          <h2 class="h2">Unpublished Batches</h2>
        </div>
        <div class="cell" style="max-height: 600px;overflow: scroll;">
          <table class="hover">
            <thead>
            <tr>
              <th width="300">Product</th>
              <th width="300">Batch Item</th>
            </tr>
            </thead>
            <tbody>
            {% for item in content.field_messages.unpublished %}
              <tr>
                <th>{{ item.product }}</th>
                <th>{{ item.entity }}</th>
              </tr>
            {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    {% endif %}
  </div>
</div>
