<?php

/**
 * @file
 * Drupal module.
 */

use Drupal\Core\Routing\RouteMatchInterface;
use Drupal\Core\Url;

/**
 * Implements hook_help().
 */
function wkhtmltopdf_help($route_name, RouteMatchInterface $route_match) {
  switch ($route_name) {
    case 'help.page.wkhtmltopdf':

      $variables = [
        ':settings' => Url::fromRoute('wkhtmltodpf.settings')->toString(),
      ];

      $output = '';
      $output .= '<h3>' . t('About') . '</h3>';
      $output .= '<p>' . t('The wkhtmltopdf module integrate the binary setted in <a href=":settings">Settings</a> and provide a link to print a page to pdf file.', $variables) . '</p>';

      return $output;
  }
}

/**
 * Implements hook_cron().
 */
function wkhtmltopdf_cron() {
  $path = \Drupal::service('file_system')->realpath('public://wkhtmltopdf');
  foreach (glob($path . '/*') as $file) {
    unlink($file);
  }

}
