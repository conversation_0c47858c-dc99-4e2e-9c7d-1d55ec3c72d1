{"syntax": "postcss-scss", "extends": ["stylelint-config-standard", "stylelint-config-prettier", "stylelint-config-standard-scss", "stylelint-config-prettier-scss"], "plugins": ["stylelint-scss"], "rules": {"color-function-notation": "modern", "selector-class-pattern": null, "custom-property-pattern": null, "at-rule-no-unknown": null, "scss/at-rule-no-unknown": true, "no-descending-specificity": null, "font-family-no-missing-generic-family-keyword": null, "no-duplicate-selectors": null, "no-extra-semicolons": null}}