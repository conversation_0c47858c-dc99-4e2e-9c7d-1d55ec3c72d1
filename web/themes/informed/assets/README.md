# Webpack ES6+ Sass Boilerplate

A webpack 5 based asset bundler for building web apps.

## What’s included?

- [Webpack 5](https://github.com/webpack/webpack) JavaScript module bundler
- [Babel 7](https://babeljs.io/) compiler ES6+ code into a backwards compatible version of JavaScript
- [SASS](http://sass-lang.com) preprocessor for CSS
- [Autoprefixer](https://github.com/postcss/autoprefixer) for vendor prefixes (browser compability)
- [Eslint](https://eslint.org) JavaScript linter
- [Stylelint](http://stylelint.io) CSS/SASS linter
- [Prettier](https://prettier.io/) an opinionated code formatter
- [lint-staged](https://github.com/okonet/lint-staged) run linting and formatting your files that are marked as "staged" via `git add` before you commit.

## Getting started
- if you need to add this to your project:
    - download a zip and copy files to your theme or desired dir.
- then:    
    - copy src_sample and name it src. 
    - rename package-master.json to package.json
    - run `npm install` to fetch all the dependencies
    - run `npm run start` to compile and watch files
    - start developing
    - when you are done, run `npm run build` to get the production version of your app

### Note: you can only edit the ignored dirs/files at bottom of git ignore. (under "editable files/folders").

## Hot Module Reloading

To enable hot module reloading (and React "fast refresh"), run `npm start`, and the default bundle is located at http://localhost:8080/main.min.js. The css file is main.min.css.

The webpack development server will provide a URL to load assets from, see `template.php` for more information on usage.

## Updating
- `git submodule foreach git pull origin master`

## Commands

- `start` - start the dev server
- `build` - create build in `build` folder
- `analyze` - analyze your production bundle
- `lint-code` - run an ESLint check
- `lint-style` - run a Stylelint check
- `check-eslint-config` - check if ESLint config contains any rules that are unnecessary or conflict with Prettier
- `check-stylelint-config` - check if Stylelint config contains any rules that are unnecessary or conflict with Prettier

## Troubleshooting

If you get an error regarding building `fsevents` and Xcode CLI tools, then either

1) open Xcode.
2) open Preferences.
3) under `Locations`, in the `Command Line Tools` dropdown, select the Xcode version corresponding to your current installation.
4) Authorize any prompts for user and password to take effect.

or you may run the following command: `sudo xcode-select --switch /Applications/Xcode.app/Contents/Developer`
