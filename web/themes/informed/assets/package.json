{"name": "able-engine-asset-bundler", "version": "1.0.0", "description": "A webpack 4 based boilerplate for building web apps", "author": "Able Engine", "scripts": {"prestart": "rimraf build && rimraf ./stats.json", "start": "webpack-dev-server --mode development --progress --color", "prebuild": "rimraf build && rimraf ./stats.json", "build": "webpack --mode production --progress --display-error-details --color", "wb": "webpack --watch --mode production --progress --display-error-details --color", "check-eslint-config": "eslint --print-config .eslintrc | eslint-config-prettier-check", "check-stylelint-config": "stylelint-config-prettier-check", "lint-code": "eslint 'src/js/**/*.js'", "lint-style": "stylelint 'src/{scss,css}/**/*.{css,scss}'", "analyze": "webpack-bundle-analyzer ./stats.json"}, "engines": {"node": ">=16.0.0 <17.0.0"}, "dependencies": {"@fortawesome/fontawesome-free": "latest", "foundation-sites": "6.7.4", "jquery": "^3.4.1", "motion-ui": "^2.0.3"}, "devDependencies": {"@babel/core": "latest", "@babel/eslint-parser": "latest", "@babel/plugin-transform-runtime": "latest", "@babel/preset-env": "latest", "@babel/preset-react": "latest", "@babel/register": "latest", "@babel/runtime": "8.0.0-alpha.17", "@pmmmwh/react-refresh-webpack-plugin": "latest", "@material/button": "^5.0.0", "autoprefixer": "^9.0.0", "babel-loader": "^8.2.5", "babel-plugin-add-header-comment": "^1.0.3", "babel-preset-react-app": "latest", "chalk": "^2.3.0", "copy-webpack-plugin": "^5.0.4", "core-js": "latest", "cross-env": "^7.0.3", "css-loader": "^5.1.1", "csso-webpack-plugin": "^1.0.0-beta.12", "eslint": "latest", "eslint-config-babel": "latest", "eslint-config-prettier": "latest", "eslint-plugin-babel": "latest", "eslint-plugin-jsx-a11y": "latest", "eslint-plugin-prettier": "latest", "eslint-plugin-react": "latest", "eslint-plugin-react-hooks": "latest", "file-loader": "^5.0.2", "glob": "^7.2.0", "husky": "^7.0.4", "lint-staged": "latest", "mini-css-extract-plugin": "^2.7.6", "node-sass-magic-importer": "^5.3.2", "minimist": "^1.2.5", "postcss-flexbugs-fixes": "^4.1.0", "postcss-loader": "^3.0.0", "postcss-preset-env": "latest", "prettier": "latest", "react-refresh": "^0.11.0", "resolve-url-loader": "^3.1.1", "rimraf": "^3.0.2", "sass": "latest", "sass-loader": "^10.1.1", "style-loader": "^2.0.0", "stylelint": "latest", "stylelint-config-prettier": "latest", "stylelint-config-prettier-scss": "latest", "stylelint-config-standard": "latest", "stylelint-config-standard-scss": "latest", "stylelint-config-sass-guidelines": "latest", "stylelint-prettier": "latest", "stylelint-scss": "latest", "url-loader": "^3.0.0", "webpack": "5.94.0", "webpack-assets-manifest": "^3.1.1", "webpack-bundle-analyzer": "^3.5.2", "webpack-cli": "^3.3.9", "webpack-dev-server": "^3.11.3", "webpack-merge": "latest", "webpack-stats-plugin": "latest"}, "production": [">0.2%", "not dead", "not op_mini all", "iOS >= 8"], "browserslist": {"production": [">0.2%", "not dead", "not op_mini all", "iOS >= 8"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "lint-staged": {"assets/js/*.js": ["eslint --fix"], "assets/scss/*.{css,scss}": ["stylelint --fix"]}}