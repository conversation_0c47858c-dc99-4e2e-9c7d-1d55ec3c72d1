import autoprefixer from 'autoprefixer';
import MiniCssExtractPlugin from 'mini-css-extract-plugin';
import paths from './paths';
import plugin from 'postcss-flexbugs-fixes';
import sass from 'sass';
import webpack from 'webpack';
import globImporter from 'node-sass-magic-importer';

module.exports = {
	context: paths.src,
	entry: ['./js/index.js', './scss/main'],
	output: {
		filename: `[name].min.js`,
		path: paths.build
	},
	resolve: {
		extensions: ['.js', '.jsx', '.scss', '.css']
	},
	performance: {
		hints: false
	},
	externals: {
		// require("jquery") is external and available
		// on the global var jQuery
		jquery: 'jQuery'
	},
	optimization: {
		moduleIds: 'deterministic'
	},
	module: {
		rules: [
			{
				test: /\.js$/,
				exclude: /node_modules/,
				loader: 'babel-loader',
				options: {
					presets: ['@babel/preset-env'],
					plugins: [
						'@babel/plugin-transform-async-to-generator',
						'@babel/plugin-proposal-object-rest-spread',
						'@babel/plugin-proposal-class-properties',
						'@babel/plugin-transform-runtime'
					]
				}
			},
			{
				test: /\.(scss|css)$/,
				exclude: /node_modules/,
				use: [
					{
						loader: MiniCssExtractPlugin.loader
					},
					{
						loader: 'css-loader',
						options: {
							sourceMap: true
						}
					},
					{
						loader: 'resolve-url-loader',
						options: {
							sourceMap: true,
							keepQuery: true,
							debug: true
						}
					},
					{
						loader: 'postcss-loader',
						options: {
							plugins: () => [autoprefixer, plugin],
							sourceMap: true
						}
					},
					{
						loader: 'sass-loader',
						options: {
							sourceMap: true,
							sassOptions: {
								implementation: sass,
								indentWidth: 4,
								importer: globImporter(),
								includePaths: [paths.node_modules],
								sourceComments: true,
								sourceMapContents: true,
								outputStyle: 'compressed'
							}
						}
					}
				]
			},
			{
				test: /((fa-[a-z].*-\d{3})\.svg|\.(|woff2?|eot|ttf|otf)*)$/,
				use: {
					loader: 'file-loader',
					options: {
						name: '[name].[ext]'
					}
				}
			},
			{
				test: /^((?!fa-[a-z].*-\d{3}).)*\.(gif|ico|jpe?g|png|svg|webp)$/,
				use: {
					loader: 'file-loader',
					options: {
						outputPath: '../images',
						name: '[name].[ext]'
					}
				}
			}
		]
	},
	plugins: [
		new MiniCssExtractPlugin({
			filename: '[name].min.css',
			chunkFilename: '[id].css'
		})
	]
};
