import path from 'path';

const browserHost = 'localhost';
const browserPort = 8080;

const development = {
	devtool: 'inline-source-map',
	devServer: {
		public: `${browserHost}:${browserPort}/assets`,
		publicPath: '/',
		contentBase: path.join(__dirname) + '/assets',
		historyApiFallback: true,
		hot: true,
		inline: true,
		compress: true,
		host: browserHost,
		port: browserPort,
		disableHostCheck: true,
		headers: { 'Access-Control-Allow-Origin': '*' },
		proxy: {
			'/assets': `http://${browserHost}:${browserPort}`
		}
	}
};

module.exports = development;
