<?php

/**
 * @file
 * Functions to support theming in the informed theme.
 */
use Drupal\block\Entity\Block;
use Drupal\Component\Utility\Html;
use Drupal\Component\Utility\Xss;
use Drupal\entity_browser_block\Plugin\Block\EntityBrowserBlock;
use Drupal\csp\Csp;
use Symfony\Component\HttpFoundation\Response;

/**
 * Implements hook_preprocess_HOOK() for Block document templates.
 */
function informed_preprocess_page(array &$variables) {
   $route_name = \Drupal::routeMatch()->getRouteName();
   if ($route_name){
       $route_prefix = explode('.', $route_name)[0];
       $variables['route_prefix'] = $route_prefix;
   }
}

function informed_preprocess_html(&$variables) {
  if (\Drupal::moduleHandler()->moduleExists('csp')) {
    $nonce_service = \Drupal::service('csp.nonce');
    $variables['csp_nonce'] = $nonce_service->getValue();
  }

  // Existing ISApp detection code
  $ua = \Drupal::request()->headers->get('User-Agent', '');
  if (!empty($ua) && strpos($ua, 'ISApp/1.0.0') !== false) {
    unset($variables['page']['top_bar']);
    unset($variables['page']['header']);
    unset($variables['page']['footer']);
    $variables['attributes']['class'][] = 'ISApp';
    $variables['ISApp'] = true;

    // Update viewport meta tag to include nonce
    $viewport = [
      '#tag' => 'meta',
      '#attributes' => [
        'name' => 'viewport',
        'content' => 'width=device-width, initial-scale=1.0, maximum-scale=1.0',
        'nonce' => $variables['csp_nonce']?? '',
      ],
    ];
    $variables['page']['#attached']['html_head'][] = [$viewport, 'viewport'];
  }

  if (\Drupal::hasService('cache_context.user_agent')) {
    $variables['#cache']['contexts'][] = 'user_agent';
  }

  // Attach site name as a valid class for global targeting
  $site_name = Html::getClass(\Drupal::config('system.site')->get('name'));
  $variables['attributes']['class'][] = $site_name;
}

/**
 * Implements hook_preprocess_HOOK() for Block document templates.
 */
function informed_preprocess_block(&$variables) {
  if (!empty($variables['elements']['#id'])) {
    $block = Block::load($variables['elements']['#id']);
    if ($block && $styles = $block->getThirdPartySettings('informed_module')) {
      $variables['styles'] = $styles ? $styles['container'] : null;
    }
  }

  // Add CSP nonce
  if (\Drupal::moduleHandler()->moduleExists('csp')) {
    $nonce_service = \Drupal::service('csp.nonce');
    $variables['csp_nonce'] = $nonce_service->getValue();
  }

  $has_third_party_settings = $variables['elements']['#configuration']['third_party_settings']['informed_module']['container'] ?? false;
  if ($has_third_party_settings){
    $block = array_key_exists('informed_module', $variables['elements']['#configuration']['third_party_settings']) ? $variables['elements']['#configuration']['third_party_settings']['informed_module']['container'] : null;
    if($block) {
      $variables['styles'] = $block;
    }

    if (isset($variables['styles']['containers'])){
      $variables['styles']['containers']['wrapper_container'] = _get_terms_class_bundle($variables['styles']['containers']['wrapper_container']);
      $variables['styles']['containers']['inner_wrapper_container'] = _get_terms_class_bundle($variables['styles']['containers']['inner_wrapper_container']);
      $variables['styles']['header']['header_classes'] = _get_terms_class_bundle($variables['styles']['header']['header_classes']);
    }

    if (isset($variables['styles']['background']['image'])){
      // get fid and if not a number then check if an entity browser is present and get the id from the selected values there
      $media_item = $variables['styles']['background']['image'] ?? false;
      if (is_array($media_item)){
        $media_item = $variables['styles']['background']['image']['selection']['entity_browser']['entity_ids'] ?? false;
        if (is_string($media_item)){
          $media_item = explode(':',$media_item);
          if (isset($media_item[1])){
            $media_item = $media_item[1];
          }
        }
      }
      if (is_numeric($media_item)){
        $media_item = \Drupal::entityTypeManager()->getStorage('media')->load($media_item);
        if ($media_item){
          $media_item = $variables['styles']['background']['image'] = $media_item;
        }
      }
    }



    if (isset($variables['styles']['visibility'])){
      $variables['styles']['visibility'] = $variables['styles']['visibility']['visibility'] ? implode(' ', array_values($variables['styles']['visibility']['visibility'])) : '';
    }
  }
	if (isset($variables['styles']['visibility']) && is_array($variables['styles']['visibility'])){
		$variables['styles']['visibility'] = $variables['styles']['visibility']['visibility'] ? implode(' ', array_values($variables['styles']['visibility']['visibility'])) : '';
	}
}

function _get_terms_class_bundle($terms = []){
  if (empty($terms)){
    return;
  }
  $term_ids = [];
  foreach ($terms as $id) {
    array_push($term_ids, $id['target_id']);
  }
  $terms = \Drupal::entityTypeManager()->getStorage('taxonomy_term')->loadMultiple($term_ids);
  $class_bundle = '';
  foreach ($terms as $term){
    $class_bundle .= $term->field_class_bundle->value . ' ' ;
  }

  return $class_bundle;

}

/**
 * Implements hook_theme_suggestions_hook_alter() for page.html.twig.
 */
function informed_theme_suggestions_field_alter(&$suggestions, $variables, $hook) {
  $label_display = $variables['element']['#label_display'] ?? false;
  $entity_type = $variables['element']['#entity_type'] ?? false;
  if ($label_display === 'inline'){
    $suggestions[] = 'field__' . $entity_type . '__label__' . $label_display;
  }
}


/**
 * Implements hook_preprocess_HOOK() for Block document templates.
 */
function informed_preprocess_menu(array &$variables) {
  // Add CSP nonce for inline styles
  if (\Drupal::moduleHandler()->moduleExists('csp')) {
    $nonce_service = \Drupal::service('csp.nonce');
    $variables['csp_nonce'] = $nonce_service->getValue();
  }

  addPropsToMenuLinks($variables['items']);
}

function addPropsToMenuLinks(&$items = []){
  foreach ($items as &$item){
    $item['external'] = $item['url']->isExternal();
    $item['nolink'] = $item['url']->toUriString() === 'route:<nolink>';
    if (!empty($item['below'])){
      addPropsToMenuLinks($item['below']);
    }
  }
}

function informed_preprocess_menu_local_task(array &$variables){
  if (!empty($variables['link'])){
	  if (isset($variables['link']['#attributes']['class']) && is_array($variables['link']['#attributes']['class'])) {
		  $variables['link']['#attributes']['class'][] = 'hover-bg-color-primary';
	  } else {
		  $variables['link']['#attributes']['class'] = 'hover-bg-color-primary';
	  }
  }
}


/**
 * Implements hook_preprocess_HOOK() for Block document templates.
 */
function informed_preprocess_field(array &$variables) {
	$layout_builder = false;
	if (isset($variables['element']['#third_party_settings']['layout_builder']))
		$layout_builder = $variables['element']['#third_party_settings']['layout_builder'];

  if($layout_builder){
    $variables['label_text'] = $layout_builder['label_text'] ?? '';
    $variables['label_size'] = $layout_builder['label_size'] ?? 'div';
    $variables['item_wrapper'] = $layout_builder['item_wrapper'] ?? 'div';
    $variables['item_separator'] = $layout_builder['item_separator'] ?? '';
    $variables['item_wrapper_classes'] = $layout_builder['item_wrapper_classes'] ?? '';
    $variables['item_classes'] = $layout_builder['item_classes'] ?? '';
    $item_sort = $layout_builder['item_sort'] ?? '';
    if ($item_sort === 'asc'){
      usort($variables['items'], "_sort_asc");
    }

    if ($item_sort === 'desc'){
      usort($variables['items'], "_sort_desc");
    }

  }
}

function _sort_asc ($a, $b){
  if (!empty($a['content']['#title']) && !empty($b['content']['#title'])){
    return $a['content']['#title'] <=> $b['content']['#title'];
  }
}

function _sort_desc ($a, $b){
  if (!empty($a['content']['#title']) && !empty($b['content']['#title'])){
    return $b['content']['#title'] <=> $a['content']['#title'];
  }
}


function informed_preprocess_input__submit(array &$variables){
  if (!empty($variables['attributes']) && !empty($variables['attributes']['id'])){
    $id = $variables['attributes']['id'];
    $variables['id'] = $id;
  }
}

function informed_preprocess_views_exposed_form__product_search(array &$variables){
  generateViewTypeVars($variables);
}

function informed_preprocess_views_view_unformatted__search__product_search(array &$variables){
  $is_infinite_scroll = $variables['view']->pager->options['views_infinite_scroll'] ?? false;
  if ($is_infinite_scroll){
    $variables['is_infinite_scroll'] = $is_infinite_scroll;
  }
  generateViewTypeVars($variables);
}

function informed_preprocess_container__infinite_scroll_wrapper(array &$variables){
  generateViewTypeVars($variables);
}

function informed_preprocess_container__infinite_scroll_outer_wrapper(array &$variables){

  generateViewTypeVars($variables);
}

function generateViewTypeVars(&$variables){
  $request = \Drupal::request();
  $is_ajax = $request->isXmlHttpRequest();
  $variables['is_ajax'] = $is_ajax;
  $search = false;
  $product_search_view = false;

  if (!empty($request->get('search')))
	  $search = Xss::filter($request->get('search'));

  $variables['search'] = Html::escape(($search ?? ''));

  if (!empty($request->get('view_type'))) {
	  $product_search_view = Xss::filter($request->get('view_type'));
	  $product_search_view = Html::escape($product_search_view);
  }

  $product_search_view = empty($product_search_view) ? 'grid_layout' : $product_search_view;
  $variables['view_type'] = $product_search_view;

  $args = \Drupal::request()->query->all();
  $grid_layout_args = $args;
  $grid_layout_args['view_type'] = 'grid_layout';

  $list_layout_args = $args;
  $list_layout_args['view_type'] = 'list_layout';

  $url = \Drupal\Core\Url::fromRoute('<current>');
  $url->setOptions(array('query' => $grid_layout_args));
  $grid_layout_url = $url->toString();
  $url->setOptions(array('query' => $list_layout_args));
  $list_layout_url = $url->toString();

  if ($product_search_view === 'grid_layout'){
    $variables['view_type_link'] = $list_layout_url;
  }

  if ($product_search_view === 'list_layout'){
    $variables['view_type_link'] = $grid_layout_url;
  }
}

//function informed_preprocess_views_view_field__search__product_search($variables){
//  $product_search_view = Xss::filter(\Drupal::request()->get('view_type'));
//  $product_search_view = Html::escape($product_search_view);
//  $product_search_view = empty($product_search_view) ? 'grid_layout' : $product_search_view;
//  $admin_label = $variables['field']->options['admin_label'] ?? '';
//  if (strpos($admin_label, $product_search_view) !== false){
//    $variables['field']->options['exclude'] = false;
//  } else {
//    $variables['field']->options['exclude'] = true;
//  }
//}


/**
 * Implements hook_theme_suggestions_hook_alter() for page.html.twig.
 */
function informed_theme_suggestions_menu_alter(&$suggestions, $variables, $hook) {
  if (!empty($variables['view_mode'])){
    $mode = $variables['view_mode'];
    $suggestions[] = 'menu__field_content__' . $mode;
  }
}

function informed_theme_suggestions_taxonomy_term_alter(&$suggestions, $variables, $hook) {
  $view_mode = $variables['elements']['#view_mode'] ?? false;
  if ($view_mode){
    $suggestions[] = 'taxonomy_term__view_mode__' . $view_mode;
  }
}

function informed_theme_suggestions_fieldset_alter(&$suggestions, $variables, $hook) {
  $theme = $variables['element']['#theme'] ?? false;
  if ($theme === 'bef_checkboxes'){
    $suggestions[] = "fieldset__$theme";
  }
}

function informed_theme_suggestions_container_alter(&$suggestions, $variables, $hook) {
  $classes = $variables['element']['#attributes']['class'] ?? [];

//  'views-infinite-scroll-content-wrapper'
  $is_infinite_scroll = $variables['element']['#view']->pager->options['views_infinite_scroll'] ?? false;
  if (is_array($classes) && in_array('views-infinite-scroll-content-wrapper',$classes)){
    $suggestions[] = "container__infinite_scroll_wrapper";
  }

  if ($is_infinite_scroll){
    $suggestions[] = "container__infinite_scroll_outer_wrapper";
  }
}

function informed_theme_suggestions_form_alter(&$suggestions, $variables) {
  if ($variables['element']['#form_id'] == 'user_login_form') {
    $suggestions[] = 'form__user_login_form';
  }
  if ($variables['element']['#form_id'] == 'user_register_form')
    $suggestions[] = 'form__user_all';
  if ($variables['element']['#form_id'] == 'user_pass')
    $suggestions[] = 'form__user_all';
  if ($variables['element']['#form_id'] == 'user_key_auth_form')
    $suggestions[] = 'form__user_all';
  if ($variables['element']['#form_id'] == 'user_pass_reset')
    $suggestions[] = 'form__user_all';
  if ($variables['element']['#form_id'] == 'user_form')
    $suggestions[] = 'form__user_all';
}

/**
 * Implements hook_page_attachments_alter().
 */
function informed_page_attachments_alter(array &$attachments) {
  //$attachments['#attached']['library'][] = 'csp/nonce';

  // Track if we need to add nonces for CSP policy
  $has_inline_scripts = FALSE;
  $has_inline_styles = FALSE;

  // Add nonces to all inline scripts and styles in head
//  if (isset($attachments['#attached']['html_head'])) {
//    foreach ($attachments['#attached']['html_head'] as &$attachment) {
//      if (isset($attachment[0]['#type']) && $attachment[0]['#type'] === 'html_tag') {
//        if (in_array($attachment[0]['#tag'], ['script', 'style'])) {
//          if (\Drupal::moduleHandler()->moduleExists('csp')) {
//            $placeholderKey = Drupal::service('csp.nonce_builder')->getPlaceholderKey();
//            $attachment[0]['#attributes']['nonce'] = $placeholderKey;
//
//            // Track what types of inline content we have
//            if ($attachment[0]['#tag'] === 'script') {
//              $has_inline_scripts = TRUE;
//            }
//            if ($attachment[0]['#tag'] === 'style') {
//              $has_inline_styles = TRUE;
//            }
//          }
//        }
//      }
//    }
//  }

  // Register nonce requirements with CSP module for proper policy generation
//  if (\Drupal::moduleHandler()->moduleExists('csp')) {
//    if ($has_inline_scripts) {
//      $attachments['#attached']['csp_nonce']['script'] = ['\'unsafe-inline\''];
//    }
//    if ($has_inline_styles) {
//      $attachments['#attached']['csp_nonce']['style'] = ['\'unsafe-inline\''];
//    }
//  }
}

/**
 * Implements hook_csp_policy_alter().
 */
//function informed_csp_policy_alter(Csp $policy, Response $response): void {
//  $nonce = \Drupal::service('csp.nonce')->getValue();
//  $policy->appendDirective('script-src', ["'self'", sprintf("'nonce-%s'", $nonce)]);
//  $policy->appendDirective('style-src', ["'self'", sprintf("'nonce-%s'", $nonce)]);
//}

