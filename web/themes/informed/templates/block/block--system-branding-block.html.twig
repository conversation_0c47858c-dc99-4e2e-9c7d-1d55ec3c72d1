{% extends "block.html.twig" %}
{#
/**
 * @file
 * <PERSON><PERSON>'s theme implementation for a branding block.
 *
 * Each branding element variable (logo, name, slogan) is only available if
 * enabled in the block configuration.
 *
 * Available variables:
 * - site_logo: Logo for site as defined in Appearance or theme settings.
 * - site_name: Name for site as defined in Site information settings.
 * - site_slogan: Slogan for site as defined in Site information settings.
 */
#}
{% set attributes = attributes.addClass('cell auto') %}
{% block content %}
  {% if site_logo %}
    <a href="{{ path('<front>') }}" rel="home" class="site-branding__logo">
      <img src="{{ site_logo }}" alt="{{ 'Home'|t }}" />
    </a>
  {% endif %}
  {% if site_name or site_slogan %}
    <div class="site-branding__text hide">
      {% if site_name %}
        <div class="site-branding__name">
          <a href="{{ path('<front>') }}" title="{{ 'Home'|t }}" rel="home">{{ site_name }}</a>
        </div>
      {% endif %}
      {% if site_slogan %}
        <div class="site-branding__slogan">{{ site_slogan }}</div>
      {% endif %}
    </div>
  {% endif %}
{% endblock %}
