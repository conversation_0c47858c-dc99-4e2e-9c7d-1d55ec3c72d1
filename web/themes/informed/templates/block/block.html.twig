{#
/**
 * @file
 * Default theme implementation to display a block.
 *
 * Available variables:
 * - plugin_id: The ID of the block implementation.
 * - label: The configured label of the block if visible.
 * - configuration: A list of the block's configuration values.
 *   - label: The configured label for the block.
 *   - label_display: The display settings for the label.
 *   - provider: The module or other provider that provided this block plugin.
 *   - Block plugin specific settings will also be stored here.
 * - content: The content of this block.
 * - attributes: array of HTML attributes populated by modules, intended to
 *   be added to the main container tag of this template.
 *   - id: A valid HTML ID and guaranteed unique.
 * - title_attributes: Same as attributes, except applied to the main title
 *   tag that appears in the template.
 * - content_attributes: Same as attributes, except applied to the main content
 *   tag that appears in the template.
 * - title_prefix: Additional output populated by modules, intended to be
 *   displayed in front of the main title tag that appears in the template.
 * - title_suffix: Additional output populated by modules, intended to be
 *   displayed after the main title tag that appears in the template.
 *
 * @see template_preprocess_block()
 *
 * @ingroup themeable
 */
#}
{%
  set classes = [
  'block',
  'block-' ~ configuration.provider|clean_class,
  'block-' ~ plugin_id|clean_class,
  styles.containers.wrapper_container,
  styles.background.color,
  styles.visibility,
  styles.header.header_position
]
%}

{% set field =  styles.background.image.field_media_image %}
{% set media =  field.0.entity.uri.value ? field.0.entity.uri.value : '' %}
{% set media_mime =  field.0.entity.filemime.value %}
{% set media_alt =  field.description ? field.description : field.alt %}
{% set url =  file_url(media) %}
{% set inline_styles =  url != '/' ? 'style=background-repeat:no-repeat;background-size:cover;background-image:url(' ~ url|raw ~ '); ' ~ styles.containers.wrapper_container_inline_styles ~ '' : 'style=' ~ styles.containers.wrapper_container_inline_styles %}
<div {{ attributes.addClass(classes) }} {{ inline_styles }} >
  {{ title_prefix }}
  {% if configuration.label_display %}
  <div class="block-header {{ styles.header.header_size }}-label {{ styles.layout.block_container }} ">
    <div class="cell small-12 medium-auto">
        {% if styles.header.header_size %}
      <{{ styles.header.header_size }} class="{{ styles.header.header_classes}}">
        {{ configuration.title ? configuration.title|raw : label|raw  }}
      </{{ styles.header.header_size }}>
      {% endif %}
    </div>
  </div>
  {% endif %}
  {{ title_suffix }}
    <div class="block-content {{ styles.containers.inner_wrapper_container }} {{ styles.text.color }}" >
      {% block content %}
        <div{{ content_attributes.addClass('content') }}>
          {{ content }}
        </div>
      {% endblock %}
    </div>
</div>


