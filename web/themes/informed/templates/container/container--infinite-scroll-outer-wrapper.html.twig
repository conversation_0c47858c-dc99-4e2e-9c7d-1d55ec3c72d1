{#
/**
 * @file
 * Default theme implementation of a container used to wrap child elements.
 *
 * Used for grouped form items. Can also be used as a theme wrapper for any
 * renderable element, to surround it with a <div> and HTML attributes.
 * See \Drupal\Core\Render\Element\RenderElement for more
 * information on the #theme_wrappers render array property, and
 * \Drupal\Core\Render\Element\container for usage of the container render
 * element.
 *
 * Available variables:
 * - attributes: HTML attributes for the containing element.
 * - children: The rendered child elements of the container.
 * - has_parent: A flag to indicate that the container has one or more parent
     containers.
 *
 * @see template_preprocess_container()
 *
 * @ingroup themeable
 */
#}
{%
  set classes = [
  has_parent ? 'js-form-wrapper',
  has_parent ? 'form-wrapper',
]
%}
<div{{ attributes.addClass(classes) }}>
  <div class="grid-container no-padding">
    <div class="grid-x grid-margin-x grid-padding-y">
      <div class="cell small-12 small-bottom-margin justify-content-space-between display-flex show-for-large show-for-app-flex app-justify-end">
        <h5 class=" secondary-font-family isapp-hide">
          {% if search %}
            <span class="font-weight-normal">{{ 'Search results for'|trans }}</span> {{ search }}
          {% endif %}
        </h5>
        <a href="{{view_type_link}}"><i class="grid-list-toggler {{ view_type }} show-for-large show-for-app"></i></a>
      </div>
      {% if search %}
        <h5 class="cell small-12 secondary-font-family hide-for-large">
          <span class="font-weight-normal">{{ 'Search results for'|trans }}</span> {{ search }}
        </h5>
      {% endif %}
    </div>
  </div>
  {{ children }}
</div>
