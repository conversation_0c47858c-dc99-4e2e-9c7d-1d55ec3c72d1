{#
/**
 * @file
 * Default theme implementation of a container used to wrap child elements.
 *
 * Used for grouped form items. Can also be used as a theme wrapper for any
 * renderable element, to surround it with a <div> and HTML attributes.
 * See \Drupal\Core\Render\Element\RenderElement for more
 * information on the #theme_wrappers render array property, and
 * \Drupal\Core\Render\Element\container for usage of the container render
 * element.
 *
 * Available variables:
 * - attributes: HTML attributes for the containing element.
 * - children: The rendered child elements of the container.
 * - has_parent: A flag to indicate that the container has one or more parent
     containers.
 *
 * @see template_preprocess_container()
 *
 * @ingroup themeable
 */
#}
{%
  set classes = [
  has_parent ? 'js-form-wrapper',
  has_parent ? 'form-wrapper',
  'grid-x',
  'grid-margin-x',
  (view_type == 'grid_layout' ? 'grid-margin-y' : '')
]
%}

<div class="grid-container no-padding">
  <div{{ attributes.addClass(classes) }}>
    {{ children }}
  </div>
</div>
