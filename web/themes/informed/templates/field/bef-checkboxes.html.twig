{#
  Themes Views' default multi-select element as a set of checkboxes.

  Available variables:
    - wrapper_attributes: attributes for the wrapper element.
    - element: The collection of checkboxes.
    - children: An array of keys for the children of element.
    - is_nested: TRUE if this is to be rendered as a nested list.
    - depth: If is_nested is TRUE, this holds an array in the form of
      child_id => nesting_level which defines the depth a given element should
      appear in the nested list.
#}
{% set classes = [
  'form-checkboxes',
  is_nested ? 'bef-nested',
  show_select_all_none ? 'bef-select-all-none',
  show_select_all_none_nested ? 'bef-select-all-none-nested',
  display_inline ? 'form--inline',
  'show-more-group'
] %}
{% set hash = 'checkboxes_' ~ random() %}
<div{{ wrapper_attributes.addClass(classes) }} id="{{ hash }}" data-toggler=".show-hidden-children">
  {{ clear }}
  {% set current_nesting_level = 0 %}
  {% for child in children %}
    {% set item = attribute(element, child) %}
    {% if is_nested %}
      {% set new_nesting_level = attribute(depth, child) %}
      {% include '@better_exposed_filters/bef-nested-elements.html.twig' %}
      {% set current_nesting_level = new_nesting_level %}
    {% else %}
        {{ item }}
    {% endif %}
  {% endfor %}
  {% if children|length >= 6 %}

    <div class="smallp small-top-margin no-margin-bottom show-hidden-children-button" data-toggle="{{ hash }}">
      <div class="informed-stacked-icons icon-size-16 ultra-small-right-margin"><i class="informed-icon informed-circle color-primary"></i><i class="informed-icon informed-caret-down color-black"></i></div>
      {{'View More'|trans}}
    </div>
  {% endif %}
</div>
