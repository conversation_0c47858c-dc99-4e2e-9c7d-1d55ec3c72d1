{#
/**
 * @file
 * Default theme implementation for a field.
 *
 * To override output, copy the "field.html.twig" from the templates directory
 * to your theme's directory and customize it, just like customizing other
 * Drupal templates such as page.html.twig or node.html.twig.
 *
 * Instead of overriding the theming for all fields, you can also just override
 * theming for a subset of fields using
 * @link themeable Theme hook suggestions. @endlink For example,
 * here are some theme hook suggestions that can be used for a field_foo field
 * on an article node type:
 * - field--node--field-foo--article.html.twig
 * - field--node--field-foo.html.twig
 * - field--node--article.html.twig
 * - field--field-foo.html.twig
 * - field--text-with-summary.html.twig
 * - field.html.twig
 *
 * Available variables:
 * - attributes: HTML attributes for the containing element.
 * - label_hidden: Whether to show the field label or not.
 * - title_attributes: HTML attributes for the title.
 * - label: The label for the field.
 * - multiple: TRUE if a field can contain multiple items.
 * - items: List of all the field items. Each item contains:
 *   - attributes: List of HTML attributes for each item.
 *   - content: The field item's content.
 * - entity_type: The entity type to which the field belongs.
 * - field_name: The name of the field.
 * - field_type: The type of the field.
 * - label_display: The display settings for the label.
 *
 * @see template_preprocess_field()
 *
 * @ingroup themeable
 */
#}
{%
  set title_classes = [
  label_display == 'visually_hidden' ? 'visually-hidden',
]
%}
{% if label_hidden %}
  {% if multiple %}
    <div{{ attributes }}>
      <div class="field-content {{ item_wrapper_classes }}">
        {% for item in items %}
        {% set item_attributes = item.attributes.addClass(item_classes) %}
        <{{item_wrapper ? item_wrapper : 'div'}} {{ item.attributes }}>{{ item.content }}</{{item_wrapper ? item_wrapper : 'div'}}>
      {% if multiple and not loop.last %}
        {{ item_separator|raw }}
      {% endif %}
      {% endfor %}
    </div>
    </div>
  {% else %}
  <div class="field-content {{ item_wrapper_classes }}">
    {% for item in items %}
      {% set item_attributes = item.attributes.addClass(item_classes) %}
      <{{item_wrapper ? item_wrapper : 'div'}} {{ item.attributes }}>{{ item.content }}</{{item_wrapper ? item_wrapper : 'div'}}>
      {% if multiple and not loop.last %}
        {{ item_separator|raw }}
      {% endif %}
    {% endfor %}
    </div>
  {% endif %}
{% else %}
  <div{{ attributes }}>
    <div{{ title_attributes.addClass(title_classes) }}><{{ label_size }}>{{ label_text ? label_text|raw : label }}</{{ label_size }}></div>
    <div class="field-content {{ item_wrapper_classes }}">
      {% for item in items %}
        {% set item_attributes = item.attributes.addClass(item_classes) %}
        <{{item_wrapper ? item_wrapper : 'div'}} {{ item.attributes }}>{{ item.content }}</{{item_wrapper ? item_wrapper : 'div'}}>
        {% if multiple and not loop.last %}
          {{ item_separator|raw }}
        {% endif %}
      {% endfor %}
      </div>
  </div>
{% endif %}
