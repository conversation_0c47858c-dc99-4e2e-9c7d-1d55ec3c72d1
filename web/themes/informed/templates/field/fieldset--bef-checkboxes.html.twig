{#
/**
 * @file
 * Default theme implementation for a fieldset element and its children.
 *
 * Available variables:
 * - attributes: HTML attributes for the <fieldset> element.
 * - errors: (optional) Any errors for this <fieldset> element, may not be set.
 * - required: <PERSON><PERSON>an indicating whether the <fieldeset> element is required.
 * - legend: The <legend> element containing the following properties:
 *   - title: Title of the <fieldset>, intended for use as the text
       of the <legend>.
 *   - attributes: HTML attributes to apply to the <legend> element.
 * - description: The description element containing the following properties:
 *   - content: The description content of the <fieldset>.
 *   - attributes: HTML attributes to apply to the description container.
 * - children: The rendered child elements of the <fieldset>.
 * - prefix: The content to add before the <fieldset> children.
 * - suffix: The content to add after the <fieldset> children.
 *
 * @see template_preprocess_fieldset()
 *
 * @ingroup themeable
 */
#}
{%
  set classes = [
  'js-form-item',
  'form-item',
  'js-form-wrapper',
  'form-wrapper',
  'border-color-senary',
  'border-on-small-up',
  'small-padding',
  'small-bottom-margin',

]
%}
<div{{ attributes.addClass(classes) }}>
  {%
    set legend_span_classes = [
    'fieldset-legend',
    required ? 'js-form-required',
    required ? 'form-required',
    'small-bottom-margin',
    ''
  ]
  %}
  {#  Always wrap fieldset legends in a <span> for CSS positioning. #}
  <div{{ legend.attributes }}>
    <h6{{ legend_span.attributes.addClass(legend_span_classes) }}>{{ legend.title }}</h6>
  </div>
  <div class="fieldset-wrapper">
    {% if errors %}
      <div>
        {{ errors }}
      </div>
    {% endif %}
    {% if prefix %}
      <span class="field-prefix">{{ prefix }}</span>
    {% endif %}
    {{ children }}
    {% if suffix %}
      <span class="field-suffix">{{ suffix }}</span>
    {% endif %}
    {% if description.content %}
      <div{{ description.attributes.addClass('description') }}>{{ description.content }}</div>
    {% endif %}
  </div>
</div>
