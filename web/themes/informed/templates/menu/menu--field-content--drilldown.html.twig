{#
/**
 * @file
 * Theme implementation to display a menu with fields.
 *
 * It is a very similar implementation to the system module template
 * except for the following code that will render the menu item
 * as an entity when is available.
 *
 * @code
 * {% if item.content %}
 *   {{ item.content }}
 * {% else %}
 *   {{ link(item.title, item.url) }}
 * {% endif %}
 * @endcode
 *
 * When the menu.html.twig template is overriden in your theme
 * this piece of code needs to be added.
 */
#}

<div class="drilldown-menu">
  <a href="#" data-toggle="mobile-menu body"><i class="informed-icon informed-hamburger"></i></a>

  <div id="mobile-menu" class="soft-hide" data-toggler=".display-block">
    <a href="#" data-toggle="mobile-menu body"><i class="informed-icon informed-close"></i></a>
    <div class="mobile-menu-wrapper">
      {% import _self as menus %}

      {#
        We call a macro which calls itself to render the full tree.
        @see http://twig.sensiolabs.org/doc/tags/macro.html
      #}
      {{ menus.menu_links(items, attributes, 0) }}

      {% macro menu_links(items, attributes, menu_level) %}
        {% import _self as menus %}
        {% if items %}
          {% if menu_level == 0 %}
            {% set attributes = attributes.addClass('vertical menu drilldown') %}
            <ul id="drilldown-menu" {{ attributes }} data-drilldown data-parent-link="true" data-disable-hover-on-touch="true" data-auto-height="true">
          {% else %}
            <ul class="menu vertical nested">
          {% endif %}
          {% for item in items %}
            {% set item_attributes = item.attributes.addClass((item.in_active_trail ? 'is-active' : '')) %}
            {% set color = item.content['#menu_link_content'].field_submenu_background.value.0.color %}
            {% set opacity = item.content['#menu_link_content'].field_submenu_background.value.0.opacity %}
            {% set submenu_background_color = menu_level == 0 ? 'style=background-color:' ~ color ~ ';opacity:' ~ opacity ~ ';' : '' %}

            {% if menu_level == 0 and color %}
              {% set hash = 'link_' ~ random() %}
              {% set item_attributes = item.attributes.addClass((item.in_active_trail ? 'is-active root-item' : 'root-item')) %}
              <style>
                #{{ hash }} a:hover, #{{ hash }} ul a, #{{ hash }}.is-active  a {background:{{ color }} !important; opacity:{{ opacity }} !important;}
                #{{ hash }} span:hover, #{{ hash }} ul span, #{{ hash }}.is-active span {background:{{ color }} !important; opacity:{{ opacity }} !important;}
              </style>
              <li id="{{ hash ?? '' }}" {{ item.attributes }} data-menu-bg-color="{{ color }}">
            {% else %}
              <li{{ item.attributes }}>
            {% endif %}
                <a href="{{ item.url ?? '#' }}" data-href="{{ item.url ?? '#' }}" onclick="void(0)">{{ item.title }}</a>
                  {% if item.below %}
                    {{ menus.menu_links(item.below, attributes, menu_level + 1) }}
                  {% endif %}
              </li>
          {% endfor %}
            </ul>
        {% endif %}
      {% endmacro %}
    </div>
  </div>
</div>
