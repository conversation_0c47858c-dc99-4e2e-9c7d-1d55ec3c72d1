{#
/**
 * @file
 * Theme implementation to display a menu with fields.
 *
 * It is a very similar implementation to the system module template
 * except for the following code that will render the menu item
 * as an entity when is available.
 *
 * @code
 * {% if item.content %}
 *   {{ item.content }}
 * {% else %}
 *   {{ link(item.title, item.url) }}
 * {% endif %}
 * @endcode
 *
 * When the menu.html.twig template is overriden in your theme
 * this piece of code needs to be added.
 */
#}
{% import _self as menus %}

{#
  We call a macro which calls itself to render the full tree.
  @see http://twig.sensiolabs.org/doc/tags/macro.html
#}
{{ menus.menu_links(items, attributes, 0) }}
{% macro menu_links(items, attributes, menu_level) %}
  {% import _self as menus %}
  {% if items %}
    {% if menu_level == 0 %}
      {% set attributes = attributes.addClass('menu--field-content--inline-items-with-icon-only') %}
<ul{{ attributes }}>
    {% else %}
    <ul>
        {% endif %}
        {% for item in items %}
            {% set field =  item.content['#menu_link_content'].field_icon %}
            {% set media =  field.0.entity.uri.value ? field.0.entity.uri.value : field.0.entity.uri.value %}
            {% set url =  file_url(media) %}
            {% set item_attributes = item.attributes.addClass((item.in_active_trail ? 'is-active' : '')) %}
            <li{{ item.attributes }}>
                {% if url %}

                    <a href="{{ item.url }}"><div class="icon">{{ item.content }}<div class="label hide">{{ item.title }}</div></a>
                {% endif %}
                {% if item.below %}
                    {{ menus.menu_links(item.below, attributes, menu_level + 1) }}
                {% endif %}
            </li>
        {% endfor %}
    </ul>
    {% endif %}
    {% endmacro %}
