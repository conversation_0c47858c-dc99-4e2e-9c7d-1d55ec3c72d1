{#
/**
 * @file
 * Theme implementation to display a menu with fields.
 *
 * It is a very similar implementation to the system module template
 * except for the following code that will render the menu item
 * as an entity when is available.
 *
 * @code
 * {% if item.content %}
 *   {{ item.content }}
 * {% else %}
 *   {{ link(item.title, item.url) }}
 * {% endif %}
 * @endcode
 *
 * When the menu.html.twig template is overriden in your theme
 * this piece of code needs to be added.
 */
#}
{% import _self as menus %}

{#
  We call a macro which calls itself to render the full tree.
  @see http://twig.sensiolabs.org/doc/tags/macro.html
#}
{{ menus.menu_links(items, attributes, 0) }}
{% macro menu_links(items, attributes, menu_level) %}
  {% import _self as menus %}
  {% if items %}
    {% if menu_level == 0 %}
      {% set attributes = attributes.addClass('topbar-menu') %}
    <ul{{ attributes }}>
  {% else %}
    <ul>
      {% endif %}
      {% for item in items %}
        {% set field =  item.content['#menu_link_content'].field_icon %}
        {% set media =  field.0.entity.uri.value ? field.0.entity.uri.value : field.0.entity.uri.value %}
        {% set url =  file_url(media) %}
        {% set item_attributes = item.attributes.addClass((item.external or item.nolink ? '' : 'is-active')) %}
        <li{{ item_attributes }}>
            <a href="{{ item.url }}">
              {% if url != '/' %}
                <div class="icon">
                  <img src="{{ url }}" alt="{{ item.title  }}">
                </div>
              {% endif %}
              <div class="label">{{ item.title }}</div>
            </a>
          {% if item.below %}
            {{ menus.menu_links(item.below, attributes, menu_level + 1) }}
          {% endif %}
        </li>
      {% endfor %}
    </ul>
  {% endif %}
{% endmacro %}
