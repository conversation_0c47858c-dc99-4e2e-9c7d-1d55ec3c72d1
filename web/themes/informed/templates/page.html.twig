{#
/**
 * @file
 * <PERSON><PERSON>'s theme implementation to display a single page.
 *
 * The doctype, html, head and body tags are not in this template. Instead they
 * can be found in the html.html.twig template normally located in the
 * core/modules/system directory.
 *
 * Available variables:
 *
 * General utility variables:
 * - base_path: The base URL path of the <PERSON><PERSON><PERSON> installation. Will usually be
 *   "/" unless you have installed <PERSON><PERSON><PERSON> in a sub-directory.
 * - is_front: A flag indicating if the current page is the front page.
 * - logged_in: A flag indicating if the user is registered and signed in.
 * - is_admin: A flag indicating if the user has permission to access
 *   administration pages.
 *
 * Site identity:
 * - front_page: The URL of the front page. Use this instead of base_path when
 *   linking to the front page. This includes the language domain or prefix.
 *
 * Page content (in order of occurrence in the default page.html.twig):
 * - node: Fully loaded node, if there is an automatically-loaded node
 *   associated with the page and the node ID is the second argument in the
 *   page's path (e.g. node/12345 and node/12345/revisions, but not
 *   comment/reply/12345).
 *
 * @see template_preprocess_page()
 * @see html.html.twig
 */
#}
<div id="page-wrapper" class="grid-container full">
  <div id="page" class="{{ route_prefix }}">
    {{ page.top_bar }}
    {{ page.header }}
    {{ page.content }}
    {{ page.footer }}
  </div>
</div>
