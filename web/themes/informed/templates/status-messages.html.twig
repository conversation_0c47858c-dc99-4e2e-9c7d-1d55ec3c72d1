{#
/**
 * @file
 * Default theme implementation for status messages.
 *
 * Displays status, error, and warning messages, grouped by type.
 *
 * An invisible heading identifies the messages for assistive technology.
 * Sighted users see a colored box. See http://www.w3.org/TR/WCAG-TECHS/H69.html
 * for info.
 *
 * Add an ARIA label to the contentinfo area so that assistive technology
 * user agents will better describe this landmark.
 *
 * Available variables:
 * - message_list: List of messages to be displayed, grouped by type.
 * - status_headings: List of all status types.
 * - attributes: HTML attributes for the element, including:
 *   - class: HTML classes.
 *
 * @ingroup themeable
 */
#}
<div class="grid-container xlarge-vertical-margin">
  <div data-drupal-messages style="background-color: rgb(255, 162, 162); padding: 10px;">
    {% for type, messages in message_list %}
      <div role="contentinfo" aria-label="{{ status_headings[type] }}"{{ attributes|without('role', 'aria-label') }}>
        {% if type == 'error' %}
        <div role="alert">
          {% endif %}
          {% if status_headings[type] %}
            <h2 class="visually-hidden">{{ status_headings[type] }}</h2>
          {% endif %}
          {% if messages|length > 1 %}
            <ul>
              {% for message in messages %}
                <li>{{ message }}</li>
              {% endfor %}
            </ul>
          {% else %}
            {{ messages|first }}
          {% endif %}
          {% if type == 'error' %}
        </div>
        {% endif %}
      </div>
    {% endfor %}
  </div>
</div>
