{#
/**
 * @file
 * Default theme implementation to display a taxonomy term.
 *
 * Available variables:
 * - url: URL of the current term.
 * - name: (optional) Name of the current term.
 * - content: Items for the content of the term (fields and description).
 *   Use 'content' to print them all, or print a subset such as
 *   'content.description'. Use the following code to exclude the
 *   printing of a given child element:
 *   @code
 *   {{ content|without('description') }}
 *   @endcode
 * - attributes: HTML attributes for the wrapper.
 * - page: Flag for the full page state.
 * - term: The taxonomy term entity, including:
 *   - id: The ID of the taxonomy term.
 *   - bundle: Machine name of the current vocabulary.
 * - view_mode: View mode, e.g. 'full', 'teaser', etc.
 *
 * @see template_preprocess_taxonomy_term()
 *
 * @ingroup themeable
 */
#}
<div{{ attributes }}>
  {{ title_prefix }}
  {% if name and not page %}
    <h2 class="hide"><a href="{{ url }}">{{ name }}</a></h2>
  {% endif %}
  {{ title_suffix }}
  {{ content }}
</div>
