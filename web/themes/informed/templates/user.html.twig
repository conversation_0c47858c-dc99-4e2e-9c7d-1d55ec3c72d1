{#
/**
 * @file
 * Default theme implementation for a 'form' element.
 *
 * Available variables
 * - attributes: A list of HTML attributes for the wrapper element.
 * - children: The child elements of the form.
 *
 * @see template_preprocess_form()
 *
 * @ingroup themeable
 */
#}
<div class="grid-container xlarge-vertical-margin">
  <article{{ attributes.addClass('profile') }}>
    {% if content %}
      {{- content -}}
    {% endif %}
  </article>
</div>
