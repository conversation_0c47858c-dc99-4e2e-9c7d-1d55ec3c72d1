{#
/**
 * @file
 * Default theme implementation of a views exposed form.
 *
 * Available variables:
 * - form: A render element representing the form.
 *
 * @see template_preprocess_views_exposed_form()
 *
 * @ingroup themeable
 */
#}
{% if q is not empty %}
  {#
    This ensures that, if clean URLs are off, the 'q' is added first,
    as a hidden form element, so that it shows up first in the POST URL.
  #}
  {{ q }}
{% endif %}
<div class="grid-x grid-margin-x grid-margin-y">
  <div class="cell small-6 medium-3 no-margin-right">
    {{ form['search'] }}
  </div>
  <div class="cell small-6 medium-3">
    {{ form.category }}
  </div>
</div>
