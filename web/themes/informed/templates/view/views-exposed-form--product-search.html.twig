{#
/**
 * @file
 * Default theme implementation of a views exposed form.
 *
 * Available variables:
 * - form: A render element representing the form.
 *
 * @see template_preprocess_views_exposed_form()
 *
 * @ingroup themeable
 */
#}
{% if q is not empty %}
  {#
    This ensures that, if clean URLs are off, the 'q' is added first,
    as a hidden form element, so that it shows up first in the POST URL.
  #}
  {{ q }}
{% endif %}
<div class="hide-for-large filter-open small-default-margin-bottom display-flex align-items-center justify-content-end">
  <a href="{{view_type_link}}"><i class="grid-list-toggler {{ view_type }}"></i></a>
  <a data-toggle="search-results-form body" class="anchor-color-black"><i class="fas fa-filter ultra-small-left-margin"></i></a>
</div>
<div id="search-results-form" class="show-for-large grid-container no-padding" data-toggler=".open-overlay">
  <div class="xlarge-bottom-margin hide-for-large">
    <a data-toggle="search-results-form body"><i class="informed-icon informed-close color-black hover-color-primary position-relative" ></i></a>
  </div>
  <div class="display-flex justify-content-end hide-for-large">
    <div class="ultra-small-right-margin xlarge-vertical-margin">{{form['actions']['submit']}}</div>
    <div>{{form['actions']['reset']}}</div>
  </div>
  {{ form.search }}
  {% if form.sort_bef_combine %}
    <div class="small-top-margin">
      {{ form.sort_bef_combine }}
    </div>
  {% endif %}
  {{ form|without('search','submit', 'reset', 'actions', 'sort_bef_combine') }}
</div>
