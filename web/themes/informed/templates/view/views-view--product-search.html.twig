{#
/**
 * @file
 * Default theme implementation for main view template.
 *
 * Available variables:
 * - attributes: Remaining HTML attributes for the element.
 * - css_name: A CSS-safe version of the view name.
 * - css_class: The user-specified classes names, if any.
 * - header: The optional header.
 * - footer: The optional footer.
 * - rows: The results of the view query, if any.
 * - empty: The content to display if there are no rows.
 * - pager: The optional pager next/prev links to display.
 * - exposed: Exposed widget form/info to display.
 * - feed_icons: Optional feed icons to display.
 * - more: An optional link to the next page of results.
 * - title: Title of the view, only used when displaying in the admin preview.
 * - title_prefix: Additional output populated by modules, intended to be
 *   displayed in front of the view title.
 * - title_suffix: Additional output populated by modules, intended to be
 *   displayed after the view title.
 * - attachment_before: An optional attachment view to be displayed before the
 *   view content.
 * - attachment_after: An optional attachment view to be displayed after the
 *   view content.
 * - dom_id: Unique id for every view being printed to give unique class for
 *   Javascript.
 *
 * @see template_preprocess_views_view()
 *
 * @ingroup themeable
 */
#}
{%
  set classes = [
  dom_id ? 'js-view-dom-id-' ~ dom_id,
  'grid-container',
  'no-padding'
]
%}
<div{{ attributes.addClass(classes) }}>
  {{ title_prefix }}
  {{ title }}
  {{ title_suffix }}

  {% if header %}
    <header>
      {{ header }}
    </header>
  {% endif %}
  <div class="grid-x grid-padding-x grid-margin-y">
    <div class="cell large-3 isapp-collapse">
      {{ exposed }}
    </div>
    <div class="cell large-9">
      {{ attachment_before }}
      {% if rows -%}
        {{ rows }}
      {% elseif empty -%}
        <div class="isapp-hide">
          {{ empty }}
        </div>
        <div class="app-no-results show-for-app" style="display:none; background-image: url('/sites/default/files/2021-04/Mobile%20App%20-%20No%20Results%20Page2.png');background-position: center;background-repeat: no-repeat;background-size: cover;height: 100%;width: 100%;position: fixed;top: 0;left: 0;">
	      <div style="margin-top: 16%; text-align: center;">
		    <span style="color: white;font-weight: 600;font-size: 1.6rem;line-height: 1;">No results were found.<br>Please type the name of the company or product here.</span>
		    <form class="views-exposed-form" action="/supplement-search" method="get" accept-charset="UTF-8" data-drupal-form-fields="" style="margin-top: 20px;margin-left: 5%;margin-right: 5%;vertical-align: middle;">
	          <input type="text" name="search" size="30" maxlength="128" class="form-text" style="width: 60%;display: inline-block;margin: 0;">
	          <input class="button button-primary hover-button-primary anchor-color-black" type="submit" value="Search" style="display: inline-block;margin-left: 4%;vertical-align: top;">
	        </form>
	      </div>
        </div>
      {% endif %}
      {{ pager }}

      {{ attachment_after }}
      {{ more }}

      {% if footer %}
        <footer>
          {{ footer }}
        </footer>
      {% endif %}

      {{ feed_icons }}
    </div>
  </div>
</div>
