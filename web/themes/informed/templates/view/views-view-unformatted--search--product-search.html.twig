{#
/**
 * @file
 * Default theme implementation to display a view of unformatted rows.
 *
 * Available variables:
 * - title: The title of this group of rows. May be empty.
 * - rows: A list of the view's row items.
 *   - attributes: The row's HTML attributes.
 *   - content: The row's content.
 * - view: The view object.
 * - default_row_class: A flag indicating whether default classes should be
 *   used on rows.
 *
 * @see template_preprocess_views_view_unformatted()
 *
 * @ingroup themeable
 */
#}
{% if title %}
{#  <h3>{{ title }}</h3>#}
{% endif %}
{% if is_infinite_scroll != true  %}
<div class="grid-container no-padding">
  <div class="grid-x grid-margin-x {{ view_type == 'grid_layout' ? 'grid-margin-y' : '' }}">
    <div class="cell small-12 small-bottom-margin justify-content-space-between display-flex show-for-large">
        <h5 class=" secondary-font-family">
          {% if search %}
              <span class="font-weight-normal">{{ 'Search results for'|trans }}</span> {{ search }}
          {% endif %}
        </h5>
        <a href="{{view_type_link}}"><i class="grid-list-toggler {{ view_type }} show-for-large"></i></a>
    </div>
    {% if search %}
      <h5 class="cell small-12 secondary-font-family hide-for-large">
          <span class="font-weight-normal">{{ 'Search results for'|trans }}</span> {{ search }}
      </h5>
    {% endif %}
 {% endif %}
    {% for row in rows %}
      {%
        set row_classes = [
        default_row_class ? 'views-row',
        view_type == 'grid_layout' ? 'small-12 medium-6 large-4 anchor-color-black border-color-senary border-on-small-up bg-color-senary' : '',
        view_type == 'list_layout' ? 'small-12' : '',
        view_type == 'list_layout' and loop.index is odd ? 'bg-color-senary' : '',
      ]
      %}
      <div{{ row.attributes.addClass(row_classes) }}>
        {{- row.content -}}
      </div>
    {% endfor %}
{% if is_infinite_scroll != true   %}
    </div>
  </div>
{% endif %}
